(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5144],{5610:(e,t,r)=>{Promise.resolve().then(r.bind(r,5761))},5761:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(5155),s=r(2115),o=r(767),n=r(345),l=r(3157),i=r(2166),c=r(3109),u=r(98);function d(){let[e,t]=(0,s.useState)([]),[r,d]=(0,s.useState)([]),[m,h]=(0,s.useState)(null),[p,x]=(0,s.useState)(!1),[b,g]=(0,s.useState)(!1),[v,j]=(0,s.useState)(null),[f,q]=(0,s.useState)({problematique:"",encrageId:""}),[w,y]=(0,s.useState)(!1),[k,N]=(0,s.useState)(""),C="ADMIN"===m,E=(0,s.useCallback)(async()=>{y(!0);try{let e=await u.uE.get("/api/problematiques");t(e)}catch(e){N(e.message||"Erreur lors du chargement des probl\xe9matiques.")}finally{y(!1)}},[]),L=(0,s.useCallback)(async()=>{try{let e=await u.uE.get("/api/encrages");d(e)}catch(e){console.error("Erreur lors du chargement des encrages:",e)}},[]),S=(0,s.useCallback)(async()=>{try{let e=await u.uE.get("/api/auth/session");h(e.role)}catch(e){console.error("Erreur lors du chargement de la session utilisateur:",e),h(null)}},[]);async function I(e){e.preventDefault(),N(""),y(!0);try{let e={problematique:f.problematique,encrageId:f.encrageId};b&&v?await (0,u.Zq)("/api/problematiques/".concat(v.id),{method:"PUT",body:e}):await (0,u.Zq)("/api/problematiques",{method:"POST",body:e}),x(!1),E()}catch(e){N(e.message)}finally{y(!1)}}async function A(e){if(confirm('\xcates-vous s\xfbr de vouloir supprimer la probl\xe9matique : "'.concat(e.problematique,'" ?')))try{await (0,u.Zq)("/api/problematiques/".concat(e.id),{method:"DELETE"}),E()}catch(e){N(e.message)}}(0,s.useEffect)(()=>{S(),E(),L()},[S,E,L]);let M=[{header:"Probl\xe9matique",accessorKey:"problematique"},{header:"Encrage",accessorKey:e=>{var t;return(null==(t=e.encrage)?void 0:t.nom)||"N/A"}},{header:"Cas",accessorKey:e=>{var t;return"".concat((null==(t=e.cas)?void 0:t.length)||0," cas")}}];return(0,a.jsxs)("div",{className:"  mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between mb-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-foreground",children:"Probl\xe9matiques"}),C&&(0,a.jsx)(o.Button,{onClick:function(){j(null),q({problematique:"",encrageId:""}),g(!1),x(!0)},title:"Ajouter une probl\xe9matique",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"})})})]}),k&&(0,a.jsx)(c.FormError,{message:k}),(0,a.jsx)(l.X,{data:e,columns:M,actions:C?e=>(0,a.jsxs)("div",{className:"flex justify-center items-center space-x-1 sm:space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{j(e),q({problematique:e.problematique,encrageId:e.encrageId}),g(!0),x(!0)},title:"Modifier la probl\xe9matique",className:"p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"})})}),(0,a.jsx)("button",{onClick:()=>A(e),title:"Supprimer la probl\xe9matique",className:"p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}):void 0}),(0,a.jsx)(i.Modal,{isOpen:p,onClose:()=>x(!1),title:b?"Modifier la probl\xe9matique":"Ajouter une probl\xe9matique",children:(0,a.jsxs)("form",{onSubmit:I,className:"space-y-4",children:[(0,a.jsx)(n.Input,{id:"problematique",label:"Probl\xe9matique",value:f.problematique,onChange:e=>q(t=>({...t,problematique:e.target.value})),required:!0}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"encrageId",className:"block text-sm font-medium text-gray-700",children:"Encrage"}),(0,a.jsxs)("select",{id:"encrageId",value:f.encrageId,onChange:e=>q(t=>({...t,encrageId:e.target.value})),className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm",required:!0,children:[(0,a.jsx)("option",{value:"",children:"S\xe9lectionner un encrage"}),r.map(e=>(0,a.jsx)("option",{value:e.id,children:e.nom},e.id))]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(o.Button,{variant:"outline",onClick:()=>x(!1),type:"button",children:"Annuler"}),(0,a.jsx)(o.Button,{type:"submit",isLoading:w,children:b?"Modifier":"Ajouter"})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9688,9741,8441,1684,7358],()=>t(5610)),_N_E=e.O()}]);