(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8591],{98:(e,s,t)=>{"use strict";async function a(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:t="GET",body:a}=s;console.log("Making ".concat(t," request to ").concat(e)),a&&console.log("Request body:",a);let n=await fetch(e,{method:t,headers:{"Content-Type":"application/json",...s.headers},credentials:s.credentials||"include",body:a?JSON.stringify(a):void 0});if(console.log("Response status:",n.status),!n.ok){let e="HTTP error! status: ".concat(n.status),s=null;try{let t=await n.text();if(console.log("Error response text:",t),t)try{e=(null==(s=JSON.parse(t))?void 0:s.error)||(null==s?void 0:s.message)||t}catch(s){e=t||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===n.status)throw Error("Authentication required. Please log in again.");if(403===n.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===n.status)throw Error("Resource not found.");else if(n.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===n.status)return null;let l=await n.json();return console.log("Response data:",l),l}t.d(s,{Zq:()=>a,uE:()=>n});let n={get:(e,s)=>a(e,{...s,method:"GET"}),post:(e,s,t)=>a(e,{...t,method:"POST",body:s}),put:(e,s,t)=>a(e,{...t,method:"PUT",body:s}),patch:(e,s,t)=>a(e,{...t,method:"PATCH",body:s}),delete:(e,s,t)=>a(e,{...t,method:"DELETE",body:s})}},6976:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(5155),n=t(2115),l=t(98);function r(){let[e,s]=(0,n.useState)(!1),[t,r]=(0,n.useState)(!1),[o,c]=(0,n.useState)([]),[i,d]=(0,n.useState)(null),[u,m]=(0,n.useState)(null),[x,g]=(0,n.useState)(null),[h,b]=(0,n.useState)(null),[p,y]=(0,n.useState)(5e4),[f,D]=(0,n.useState)(!1),N=async()=>{D(!0),b(null);try{console.log("\uD83E\uDDEA Test des APIs de statistiques...");let e=await (0,l.Zq)("/api/admin/test-stats-apis");if(e.success){console.log("✅ Test APIs statistiques r\xe9ussi:",e.results);let s=e.results,t=s.filter(e=>e.success).length;s.filter(e=>!e.success).length;let a="✅ Test APIs statistiques termin\xe9 !\n\n";a+="\uD83D\uDCCA ".concat(t,"/").concat(s.length," APIs fonctionnelles\n"),a+="\uD83D\uDCC8 Donn\xe9es de base: ".concat(e.baseCounts.cas," cas, ").concat(e.baseCounts.blocages," blocages\n"),a+="⏱️ Dur\xe9e: ".concat(e.performance.duration,"ms\n\n"),a+="D\xe9tails par API:\n",s.forEach(e=>{e.success?a+="✅ ".concat(e.api,": ").concat(e.dataCount," \xe9l\xe9ments\n"):a+="❌ ".concat(e.api,": ").concat(e.error,"\n")}),alert(a),e.baseCounts&&m(e.baseCounts)}else b("Test \xe9chou\xe9: ".concat(e.error,"\nD\xe9tails: ").concat(e.details||"Aucun d\xe9tail"))}catch(e){console.error("Erreur lors du test:",e),b("Test APIs statistiques \xe9chou\xe9: ".concat(e.message))}finally{D(!1)}},j=async()=>{D(!0),b(null);try{console.log("\uD83E\uDDEA Test des statistiques simples...");let e=await (0,l.Zq)("/api/test-simple-stats");if(e.success){console.log("✅ Test statistiques simples r\xe9ussi:",e.stats);let s=e.stats;alert("✅ Test statistiques simples r\xe9ussi !\n\n"+"\uD83D\uDCCA Total cas: ".concat(s.totalCas.toLocaleString(),"\n")+"✅ R\xe9gularis\xe9s: ".concat(s.regularises.toLocaleString(),"\n")+"\uD83D\uDEA7 Total blocages: ".concat(s.totalBlocages.toLocaleString(),"\n")+"⏱️ Dur\xe9e: ".concat(e.performance.duration,"ms\n")+"⏰ ".concat(e.performance.timestamp)),s&&m({cas:s.totalCas,blocages:s.totalBlocages,secteurs:0,problematiques:0,users:0})}else b("Test \xe9chou\xe9: ".concat(e.error,"\nD\xe9tails: ").concat(e.details||"Aucun d\xe9tail"))}catch(e){console.error("Erreur lors du test:",e),b("Test statistiques \xe9chou\xe9: ".concat(e.message))}finally{D(!1)}},v=async()=>{D(!0),b(null);try{console.log("\uD83D\uDD0D Lancement du diagnostic complet...");let e=await (0,l.Zq)("/api/admin/debug-api");if(e.success){console.log("✅ Diagnostic complet r\xe9ussi:",e.results);let s=e.results,t=s.database.counts;alert("✅ Diagnostic complet r\xe9ussi !\n\n"+"\uD83D\uDC64 Utilisateur: ".concat(s.user.username," (").concat(s.user.role,")\n")+"\uD83D\uDDC4️ Base de donn\xe9es: Connect\xe9e\n"+"\uD83D\uDCCA Donn\xe9es: ".concat(t.cas.toLocaleString()," cas, ").concat(t.blocages.toLocaleString()," blocages\n")+"\uD83D\uDD17 Tests: JOIN OK, SQL brut OK\n"+"⏰ ".concat(e.timestamp)),s.database.counts&&m(s.database.counts)}else{let s="❌ Diagnostic \xe9chou\xe9 \xe0 l'\xe9tape: ".concat(e.step,"\n\n")+"Erreur: ".concat(e.error,"\n")+"D\xe9tails: ".concat(e.details||"Aucun d\xe9tail");console.error("❌ Diagnostic \xe9chou\xe9:",e),b(s),alert(s)}}catch(s){console.error("Erreur lors du diagnostic:",s);let e="\uD83D\uDCA5 Erreur fatale lors du diagnostic:\n\n".concat(s.message);b(e),alert(e)}finally{D(!1)}},w=async()=>{D(!0),b(null);try{console.log("\uD83E\uDDEA Test de g\xe9n\xe9ration r\xe9elle (100 cas)...");let e=await (0,l.Zq)("/api/admin/generate-real-data",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({testCount:100})});e.success?(m(e.totals),console.log("✅ G\xe9n\xe9ration r\xe9elle r\xe9ussie:",e.message),alert("✅ G\xe9n\xe9ration r\xe9elle r\xe9ussie !\n\n\uD83D\uDCCA ".concat(e.created.cas," cas cr\xe9\xe9s\n\uD83D\uDEA7 ").concat(e.created.blocages," blocages cr\xe9\xe9s\n\uD83D\uDCC8 Total: ").concat(e.totals.cas.toLocaleString()," cas dans la base"))):b("G\xe9n\xe9ration \xe9chou\xe9e: ".concat(e.message||"Erreur inconnue"))}catch(e){console.error("Erreur lors de la g\xe9n\xe9ration:",e),b("Test de g\xe9n\xe9ration \xe9chou\xe9: ".concat(e.message))}finally{D(!1)}},C=async()=>{D(!0),b(null);try{console.log("\uD83D\uDD0D Test de connexion...");let e=await (0,l.Zq)("/api/admin/simple-test");e.success?(console.log("✅ Connexion r\xe9ussie:",e.message),alert("✅ Test de connexion r\xe9ussi !\n\n\uD83D\uDC64 Utilisateur: ".concat(e.user.username," (").concat(e.user.role,")\n⏰ Timestamp: ").concat(e.timestamp))):b("Test \xe9chou\xe9: ".concat(e.message||"Erreur inconnue"))}catch(e){console.error("Erreur lors du test:",e),b("Test de connexion \xe9chou\xe9: ".concat(e.message))}finally{D(!1)}},T=async()=>{D(!0),b(null);try{console.log("\uD83E\uDDEA Test de g\xe9n\xe9ration (10 cas)...");let e=await (0,l.Zq)("/api/admin/simple-test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({testCount:10,action:"test-generation"})});e.success?(console.log("✅ Test POST r\xe9ussi:",e.message),alert("✅ Test POST r\xe9ussi !\n\n� Utilisateur: ".concat(e.user,"\n\uD83D\uDCE6 Donn\xe9es re\xe7ues: ").concat(JSON.stringify(e.received),"\n⏰ ").concat(e.timestamp))):b("Test \xe9chou\xe9: ".concat(e.message||"Erreur inconnue"))}catch(e){console.error("Erreur lors du test:",e),b("Test de g\xe9n\xe9ration \xe9chou\xe9: ".concat(e.message))}finally{D(!1)}},E=async()=>{r(!0),b(null);try{console.log("\uD83D\uDE80 G\xe9n\xe9ration de ".concat(p.toLocaleString()," cas de test..."));let e=await (0,l.Zq)("/api/admin/generate-test-data",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({targetCas:p})});e.success&&(m(e.totals),console.log("✅ G\xe9n\xe9ration termin\xe9e: ".concat(e.created.cas," cas, ").concat(e.created.blocages," blocages cr\xe9\xe9s")),alert("\uD83C\uDF89 G\xe9n\xe9ration r\xe9ussie !\n\n\uD83D\uDCCA ".concat(e.created.cas.toLocaleString()," cas cr\xe9\xe9s\n\uD83D\uDEA7 ").concat(e.created.blocages.toLocaleString()," blocages cr\xe9\xe9s\n\uD83D\uDCE6 ").concat(e.batches," batches trait\xe9s")))}catch(n){var e,s,t;console.error("Erreur lors de la g\xe9n\xe9ration:",n),console.error("D\xe9tails de l'erreur:",{message:n.message,status:n.status,response:n.response});let a="Erreur lors de la g\xe9n\xe9ration des donn\xe9es de test";(null==(e=n.message)?void 0:e.includes("401"))?a="Erreur d'authentification. Veuillez vous reconnecter.":(null==(s=n.message)?void 0:s.includes("403"))?a="Acc\xe8s refus\xe9. Vous devez \xeatre administrateur.":(null==(t=n.message)?void 0:t.includes("400"))?a="Param\xe8tres invalides. V\xe9rifiez le nombre de cas.":n.message&&(a=n.message),b(a)}finally{r(!1)}},S=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"all";s(!0),b(null),c([]),d(null);try{console.log("\uD83D\uDE80 Lancement des tests de performance: ".concat(e));let s=await (0,l.Zq)("/api/admin/performance-test?type=".concat(e));c(s.results),d(s.summary),m(s.database),g(s.analysis),console.log("✅ Tests termin\xe9s: ".concat(s.summary.successfulTests,"/").concat(s.summary.totalTests," r\xe9ussis"))}catch(e){console.error("Erreur lors des tests:",e),b(e.message||"Erreur lors des tests de performance")}finally{s(!1)}},A=e=>e<1e3?"text-green-600":e<5e3?"text-yellow-600":e<1e4?"text-orange-600":"text-red-600",P=e=>e<1e3?"\uD83D\uDE80":e<5e3?"⚡":e<1e4?"⚠️":"\uD83D\uDC0C";return(0,a.jsx)("div",{className:" mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"\uD83D\uDE80 Tests de Performance"}),u&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-blue-900 mb-4",children:"\uD83D\uDCCA Base de donn\xe9es"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.cas.toLocaleString()}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Cas"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.blocages.toLocaleString()}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Blocages"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.secteurs}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Secteurs"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.problematiques}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Probl\xe9matiques"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.users}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Utilisateurs"})]})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-yellow-900 mb-4",children:"\uD83D\uDD0D Tests de diagnostic"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mb-4",children:[(0,a.jsx)("button",{onClick:C,disabled:f||t||e,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:f?"\uD83D\uDD04 Test...":"\uD83D\uDD0D Test Auth"}),(0,a.jsx)("button",{onClick:T,disabled:f||t||e,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:f?"\uD83D\uDD04 Test...":"\uD83E\uDDEA Test API POST"}),(0,a.jsx)("button",{onClick:w,disabled:f||t||e,className:"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:f?"\uD83D\uDD04 Test...":"\uD83D\uDE80 Test G\xe9n\xe9ration (100 cas)"}),(0,a.jsx)("button",{onClick:N,disabled:f||t||e,className:"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:f?"\uD83D\uDD04 Test...":"\uD83D\uDCCA Test APIs Stats"}),(0,a.jsx)("button",{onClick:j,disabled:f||t||e,className:"bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:f?"\uD83D\uDD04 Test...":"\uD83E\uDDEA Test Stats Simples"}),(0,a.jsx)("button",{onClick:v,disabled:f||t||e,className:"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:f?"\uD83D\uDD04 Test...":"\uD83D\uDD0D Diagnostic Complet"})]}),(0,a.jsx)("p",{className:"text-sm text-yellow-700",children:"Utilisez ces tests pour diagnostiquer les probl\xe8mes avant la g\xe9n\xe9ration compl\xe8te."})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83D\uDCCA G\xe9n\xe9ration de donn\xe9es de test"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Nombre de cas \xe0 g\xe9n\xe9rer:"}),(0,a.jsx)("input",{type:"number",value:p,onChange:e=>y(parseInt(e.target.value)||5e4),min:"1000",max:"100000",step:"1000",className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-24",disabled:t})]}),(0,a.jsx)("button",{onClick:E,disabled:t||e,className:"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:t?"\uD83D\uDD04 G\xe9n\xe9ration en cours...":"\uD83D\uDCCA G\xe9n\xe9rer les donn\xe9es"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"G\xe9n\xe8re des cas fictifs avec leurs contraintes associ\xe9es pour tester les performances. Chaque cas aura 1-3 contraintes avec des r\xe9solutions vari\xe9es."})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83E\uDDEA Lancer les tests de performance"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)("button",{onClick:()=>S("all"),disabled:e,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:e?"\uD83D\uDD04 Tests en cours...":"\uD83D\uDE80 Tous les tests"}),(0,a.jsx)("button",{onClick:()=>S("dashboard"),disabled:e,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"\uD83D\uDCCA Dashboard"}),(0,a.jsx)("button",{onClick:()=>S("listing"),disabled:e,className:"bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"\uD83D\uDCCB Listing"}),(0,a.jsx)("button",{onClick:()=>S("stats"),disabled:e,className:"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"\uD83D\uDCC8 Statistiques"})]})]}),i&&(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83D\uDCCB R\xe9sum\xe9 des tests"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-6 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-600",children:[Math.round(i.totalDuration),"ms"]}),(0,a.jsx)("div",{className:"text-sm text-gray-700",children:"Dur\xe9e totale"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.totalTests}),(0,a.jsx)("div",{className:"text-sm text-gray-700",children:"Tests total"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:i.successfulTests}),(0,a.jsx)("div",{className:"text-sm text-gray-700",children:"R\xe9ussis"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:i.failedTests}),(0,a.jsx)("div",{className:"text-sm text-gray-700",children:"\xc9chou\xe9s"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:i.slowTests}),(0,a.jsx)("div",{className:"text-sm text-gray-700",children:"Lents (5-10s)"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:i.verySlowTests}),(0,a.jsx)("div",{className:"text-sm text-gray-700",children:"Tr\xe8s lents (>10s)"})]})]})]}),x&&(0,a.jsxs)("div",{className:"border rounded-lg p-6 mb-6 ".concat("excellent"===x.performance?"bg-green-50 border-green-200":"good"===x.performance?"bg-yellow-50 border-yellow-200":"bg-red-50 border-red-200"),children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"excellent"===x.performance?"\uD83D\uDE80 Performance Excellente":"good"===x.performance?"⚡ Performance Correcte":"\uD83D\uDC0C Performance \xe0 Optimiser"}),x.recommendations.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCA1 Recommandations:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-1",children:x.recommendations.map((e,s)=>(0,a.jsx)("li",{className:"text-sm",children:e},s))})]})]}),h&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-red-900 mb-2",children:"❌ Erreur"}),(0,a.jsx)("p",{className:"text-red-700",children:h})]}),o.length>0&&(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83D\uDCCA R\xe9sultats d\xe9taill\xe9s"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Dur\xe9e"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"R\xe9sultats"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"M\xe9moire"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Statut"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map((e,s)=>{var t;return(0,a.jsxs)("tr",{className:e.success?"":"bg-red-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium ".concat(A(e.duration)),children:[P(e.duration)," ",e.duration,"ms"]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(t=e.resultCount)?void 0:t.toLocaleString())||"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.memoryUsage?"".concat(Math.round(e.memoryUsage.heapUsed/1024/1024),"MB"):"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.success?(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"✅ R\xe9ussi"}):(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"❌ \xc9chou\xe9"})})]},s)})})]})})]})]})})}},9360:(e,s,t)=>{Promise.resolve().then(t.bind(t,6976))}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(9360)),_N_E=e.O()}]);