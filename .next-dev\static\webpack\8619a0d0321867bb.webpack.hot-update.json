{"c": ["app/layout", "app/dashboard/statistiques/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/components/RoleBasedAccess.tsx", "(app-pages-browser)/./app/dashboard/statistiques/page.tsx", "(app-pages-browser)/./lib/client-permissions.ts", "(app-pages-browser)/./lib/hooks/usePermissions.ts", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js", "(app-pages-browser)/./node_modules/@kurkle/color/dist/color.esm.js", "(app-pages-browser)/./node_modules/chart.js/dist/chart.js", "(app-pages-browser)/./node_modules/chart.js/dist/chunks/helpers.dataset.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Cdashboard%5C%5Cstatistiques%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/react-chartjs-2/dist/index.js"]}