(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5513],{767:(e,s,r)=>{"use strict";r.r(s),r.d(s,{Button:()=>i,default:()=>o});var a=r(5155),t=r(9688),l=r(3084);function i(e){let{children:s,className:r,variant:i="primary",size:o="default",isLoading:n=!1,disabled:c,...d}=e;return(0,a.jsx)("button",{className:(0,t.QP)("rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/90 focus:ring-secondary/50",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-primary/50",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500/50"}[i],{default:"px-4 py-2",sm:"px-3 py-1.5 text-sm",icon:"p-2"}[o],r),disabled:n||c,...d,children:n?(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)(l.LoadingSpinner,{})}):s})}let o=i},984:(e,s,r)=>{Promise.resolve().then(r.bind(r,1917))},1917:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g,dynamic:()=>h});var a=r(5155),t=r(2115),l=r(5695),i=r(98),o=r(3084),n=r(3109),c=r(767),d=r(7055),u=r(7209);function m(e){let{casList:s,onZoomToCas:r,onViewCasDetails:t,selectedCas:l,onSelectCas:i}=e;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Liste des Dossiers (",Array.isArray(s)?s.length:0,")"]}),(0,a.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:Array.isArray(s)&&s.map(e=>{var s;return(0,a.jsx)("div",{className:"border rounded-lg p-3 transition-colors cursor-pointer ".concat((null==l?void 0:l.id)===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"),onClick:()=>i&&i(e),children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate cursor-pointer hover:text-blue-600",onClick:()=>r(e.id),children:e.nom}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.superficie," Ha •"," ",null==(s=e.communes)?void 0:s.map(e=>e.nom).join(", "),e.user&&" • DSA: ".concat(e.user.username)]})]})})},e.id)})}),(!Array.isArray(s)||0===s.length)&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("svg",{className:"w-12 h-12 mx-auto mb-3 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,a.jsx)("p",{children:"Aucun cas trouv\xe9 avec les filtres actuels"})]})]})}let h="force-dynamic";function g(){let e=(0,l.useRouter)(),[s,r]=(0,t.useState)([]),[h,g]=(0,t.useState)([]),[x,p]=(0,t.useState)([]),[j,v]=(0,t.useState)(null),[b,f]=(0,t.useState)(!0),[y,N]=(0,t.useState)(null),[w,k]=(0,t.useState)(""),[C,S]=(0,t.useState)(""),[A,L]=(0,t.useState)(!1),[D,q]=(0,t.useState)([]),[E]=(0,t.useState)([36.75,3.06]);(0,t.useEffect)(()=>{T()},[]),(0,t.useEffect)(()=>{z()},[w,C,A]);let T=async()=>{try{f(!0);let[e,s,a]=await Promise.all([(0,i.Zq)("/api/cas?withGeojson=true&includeKML=true&pageSize=1000"),(0,i.Zq)("/api/problematiques?context=formCreation"),(0,i.Zq)("/api/encrages")]);r((null==e?void 0:e.data)||[]),g(s||[]);let t=Array.isArray(a)?a:Array.isArray(null==a?void 0:a.data)?a.data:[];p(t)}catch(e){console.error("Erreur lors du chargement des donn\xe9es:",e),N("Erreur lors du chargement des donn\xe9es")}finally{f(!1)}},z=async()=>{try{let e="/api/cas?withGeojson=true&includeKML=true&pageSize=1000",s=new URLSearchParams;w&&s.append("encrageId",w),C&&s.append("problematiqueId",C),A&&s.append("regularisation","true"),s.toString()&&(e+="&".concat(s.toString()));let a=await (0,i.Zq)(e);a?r(a.data||[]):r([])}catch(e){console.error("Erreur lors du filtrage des cas:",e),N("Erreur lors du filtrage des cas")}},O=w?(Array.isArray(h)?h:[]).filter(e=>{var s;return(null==(s=e.encrage)?void 0:s.id)===w}):Array.isArray(h)?h:[];console.log("casList is:",s);let _=s.filter(e=>{var s;return(null==(s=e.geojson)?void 0:s.coordinates)||e.kmlData&&e.kmlData.features});console.log("casList with KML:",s.filter(e=>e.kmlData)),console.log("casList with geojson:",s.filter(e=>e.geojson)),console.log("casWithGeojson count:",_.length),console.log("Total casList length:",s.length),console.log("Sample cas item:",s[0]);let B=s=>{e.push("/cas/".concat(s))},R=s=>{e.push("/cas/".concat(s,"/cartographie"))},V=e=>{window.zoomToCas&&window.zoomToCas(e)},Z=e=>{let s,r,a;if(e.kmlData&&e.kmlData.features)s=JSON.stringify(e.kmlData,null,2),r="".concat(e.nom,"_geometry.json"),a="application/json";else{if(!e.geojson)return void alert("Aucune donn\xe9e g\xe9ographique disponible pour ce dossier.");s=JSON.stringify(e.geojson,null,2),r="".concat(e.nom,"_geometry.geojson"),a="application/geo+json"}let t=new Blob([s],{type:a}),l=URL.createObjectURL(t),i=document.createElement("a");i.href=l,i.download=r,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(l)};return b?(0,a.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,a.jsx)(o.LoadingSpinner,{})}):y?(0,a.jsx)(n.FormError,{message:y}):(0,a.jsxs)("div",{className:" mx-auto px-2 py-4",children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("div",{className:"bg-white p-2 rounded-lg shadow-sm border mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Encrage juridique (programme)"}),(0,a.jsxs)(d.l,{value:w,onChange:e=>{k(e.target.value),S("")},children:[(0,a.jsx)("option",{value:"",children:"Tous les encrages"}),(Array.isArray(x)?x:[]).map(e=>(0,a.jsx)("option",{value:e.id,children:e.nom},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Probl\xe9matique associ\xe9e"}),(0,a.jsxs)(d.l,{value:C,onChange:e=>S(e.target.value),disabled:!w,children:[(0,a.jsx)("option",{value:"",children:"Toutes les probl\xe9matiques"}),O.map(e=>(0,a.jsx)("option",{value:e.id,children:e.problematique},e.id))]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:A,onChange:e=>L(e.target.checked),className:"mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Dossiers r\xe9gularis\xe9s"})]})})]})}),(0,a.jsx)("div",{className:"bg-white p-2 rounded-lg shadow-sm border mb-1",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total des Dossiers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:_.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Dossiers g\xe9olocalis\xe9s"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:s.filter(e=>e.regularisation).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Dossiers r\xe9gularis\xe9s"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,a.jsx)(u.A,{casList:s,selectedCas:j,setSelectedCas:v,onViewCasDetails:B,onViewCasCartographie:R,center:E,zoom:10,height:"600px",kmlLayers:D,onZoomToCas:V,showOnlySelected:!0})})}),(0,a.jsxs)("div",{className:"lg:col-span-1 space-y-3",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-2",children:(0,a.jsx)(m,{casList:s,onZoomToCas:V,onViewCasDetails:B,selectedCas:j,onSelectCas:v})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:j?"D\xe9tails du Dossier s\xe9lectionn\xe9":"S\xe9lectionnez un Dossier sur la carte"}),j?(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2 pt-4 border-t",children:[(0,a.jsx)(c.Button,{onClick:()=>B(j.id),className:"w-full",children:"Voir tous les d\xe9tails"}),(0,a.jsx)(c.Button,{onClick:()=>R(j.id),variant:"outline",className:"w-full",children:"Cartographie d\xe9taill\xe9e"}),(j.geojson||j.kmlData&&j.kmlData.features)&&(0,a.jsx)(c.Button,{onClick:()=>Z(j),variant:"outline",className:"w-full",children:"T\xe9l\xe9charger GeoJSON/KML"})]})}):(0,a.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,a.jsx)("p",{children:"Cliquez sur un marqueur sur la carte pour voir les d\xe9tails du cas."}),0===_.length&&(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Aucun Dossier g\xe9olocalis\xe9 trouv\xe9 avec les filtres actuels."})]})]})]})]})]})}},7055:(e,s,r)=>{"use strict";r.d(s,{l:()=>t});var a=r(5155);r(2115);let t=e=>{let{label:s,id:r,name:t,value:l,onChange:i,required:o,multiple:n,children:c,className:d,error:u,...m}=e;return(0,a.jsxs)("div",{children:[s&&(0,a.jsxs)("label",{htmlFor:r||t,className:"block text-sm font-medium text-gray-700 mb-1",children:[s," ",o&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("select",{id:r||t,name:t,value:l,onChange:i,required:o,multiple:n,className:"".concat("block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md shadow-sm"," ").concat(u?"border-red-500 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"," ").concat(d||""),...m,children:c}),u&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:u})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[1761,9688,9359,5075,8441,1684,7358],()=>s(984)),_N_E=e.O()}]);