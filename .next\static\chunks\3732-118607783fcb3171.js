"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3732],{98:(e,t,r)=>{async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:r="GET",body:s}=t;console.log("Making ".concat(r," request to ").concat(e)),s&&console.log("Request body:",s);let n=await fetch(e,{method:r,headers:{"Content-Type":"application/json",...t.headers},credentials:t.credentials||"include",body:s?JSON.stringify(s):void 0});if(console.log("Response status:",n.status),!n.ok){let e="HTTP error! status: ".concat(n.status),t=null;try{let r=await n.text();if(console.log("Error response text:",r),r)try{e=(null==(t=JSON.parse(r))?void 0:t.error)||(null==t?void 0:t.message)||r}catch(t){e=r||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===n.status)throw Error("Authentication required. Please log in again.");if(403===n.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===n.status)throw Error("Resource not found.");else if(n.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===n.status)return null;let a=await n.json();return console.log("Response data:",a),a}r.d(t,{Zq:()=>s,uE:()=>n});let n={get:(e,t)=>s(e,{...t,method:"GET"}),post:(e,t,r)=>s(e,{...r,method:"POST",body:t}),put:(e,t,r)=>s(e,{...r,method:"PUT",body:t}),patch:(e,t,r)=>s(e,{...r,method:"PATCH",body:t}),delete:(e,t,r)=>s(e,{...r,method:"DELETE",body:t})}},184:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(2115);let n=s.forwardRef(function(e,t){let{title:r,titleId:n,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},a),r?s.createElement("title",{id:n},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},255:(e,t,r)=>{function s(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return s}}),r(5155),r(7650),r(5744),r(589)},767:(e,t,r)=>{r.r(t),r.d(t,{Button:()=>l,default:()=>o});var s=r(5155),n=r(9688),a=r(3084);function l(e){let{children:t,className:r,variant:l="primary",size:o="default",isLoading:i=!1,disabled:c,...d}=e;return(0,s.jsx)("button",{className:(0,n.QP)("rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/90 focus:ring-secondary/50",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-primary/50",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500/50"}[l],{default:"px-4 py-2",sm:"px-3 py-1.5 text-sm",icon:"p-2"}[o],r),disabled:i||c,...d,children:i?(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)(a.LoadingSpinner,{})}):t})}let o=l},1151:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(2115);let n=s.forwardRef(function(e,t){let{title:r,titleId:n,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},a),r?s.createElement("title",{id:n},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},2146:(e,t,r)=>{function s(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}}),r(5262)},3084:(e,t,r)=>{r.r(t),r.d(t,{LoadingSpinner:()=>a,default:()=>l});var s=r(5155),n=r(9688);function a(e){let{className:t,color:r="light",size:a="md"}=e;return(0,s.jsx)("div",{className:(0,n.QP)("border-2 rounded-full animate-spin",{light:"border-white/80 border-t-transparent",dark:"border-gray-700 border-t-transparent"}[r],{sm:"w-4 h-4",md:"w-5 h-5",lg:"w-8 h-8"}[a],t)})}let l=a},4054:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return l},createAsyncLocalStorage:function(){return a},createSnapshot:function(){return o}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class s{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let n="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return n?new n:new s}function l(e){return n?n.bind(e):s.bind(e)}function o(){return n?n.snapshot():function(e,...t){return e(...t)}}},5028:(e,t,r)=>{r.d(t,{default:()=>n.a});var s=r(6645),n=r.n(s)},5037:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(2115);let n=s.forwardRef(function(e,t){let{title:r,titleId:n,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},a),r?s.createElement("title",{id:n},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},5442:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(2115);let n=s.forwardRef(function(e,t){let{title:r,titleId:n,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},a),r?s.createElement("title",{id:n},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))})},5744:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return s.workAsyncStorageInstance}});let s=r(7828)},6645:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let s=r(8229)._(r(7357));function n(e,t){var r;let n={};"function"==typeof e&&(n.loader=e);let a={...n,...t};return(0,s.default)({...a,modules:null==(r=a.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7357:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let s=r(5155),n=r(2115),a=r(2146);function l(e){return{default:e&&"default"in e?e.default:e}}r(255);let o={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},i=function(e){let t={...o,...e},r=(0,n.lazy)(()=>t.loader().then(l)),i=t.loading;function c(e){let l=i?(0,s.jsx)(i,{isLoading:!0,pastDelay:!0,error:null}):null,o=!t.ssr||!!t.loading,c=o?n.Suspense:n.Fragment,d=t.ssr?(0,s.jsxs)(s.Fragment,{children:[null,(0,s.jsx)(r,{...e})]}):(0,s.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(r,{...e})});return(0,s.jsx)(c,{...o?{fallback:l}:{},children:d})}return c.displayName="LoadableComponent",c}},7359:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(2115);let n=s.forwardRef(function(e,t){let{title:r,titleId:n,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},a),r?s.createElement("title",{id:n},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},7614:(e,t,r)=>{r.r(t),r.d(t,{default:()=>I});var s=r(5155),n=r(2115),a=r(5695),l=r(6874),o=r.n(l),i=r(6671),c=r(98),d=r(5028),u=r(8861);let m=e=>{let{height:t=20,width:r="100%",className:n=""}=e;return(0,s.jsx)("div",{className:"animate-pulse bg-gray-300 rounded ".concat(n),style:{height:t,width:r}})},x=n.forwardRef(function(e,t){let{title:r,titleId:s,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},a),r?n.createElement("title",{id:s},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),g=n.forwardRef(function(e,t){let{title:r,titleId:s,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},a),r?n.createElement("title",{id:s},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var h=r(1151),p=r(7359),f=r(5037);let b=e=>{let{checked:t,onChange:r,onCheckedChange:a,className:l,id:o,...i}=e,c=o||n.useId();return(0,s.jsxs)("label",{htmlFor:c,className:"relative inline-flex items-center cursor-pointer ".concat(l||""),style:{userSelect:"none"},children:[(0,s.jsx)("input",{type:"checkbox",id:c,className:"sr-only peer",checked:t,onChange:e=>{r&&r(e),a&&a(e.target.checked)},...i}),(0,s.jsx)("div",{className:"w-11 h-6 flex items-center transition-colors duration-200 relative border-2 rounded-full overflow-hidden\n          ".concat(t?"bg-green-600 border-green-700":"bg-red-600 border-red-700","\n        "),children:(0,s.jsx)("span",{className:"inline-block h-5 w-5 rounded-full bg-white shadow transform transition-transform duration-200 absolute top-0.5\n            ".concat(t?"translate-x-5 border-green-700":"translate-x-1 border-red-700"," border-2"),style:{boxSizing:"border-box"}})})]})},j={ATTENTE:"En attente",ACCEPTE:"Accept\xe9",AJOURNE:"Ajourn\xe9",REJETE:"Rejet\xe9"},y={ATTENTE:"bg-gray-100 text-gray-800 border-gray-200",ACCEPTE:"bg-green-100 text-green-800 border-green-200",AJOURNE:"bg-orange-100 text-orange-800 border-orange-200",REJETE:"bg-red-100 text-red-800 border-red-200"};function v(e){let{value:t,onChange:r,disabled:n=!1,className:a=""}=e;return(0,s.jsx)("select",{value:t,onChange:e=>r(e.target.value),disabled:n,className:"\n                border rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-300 focus:border-blue-400 \n                transition-all ".concat(a,"\n                ").concat(n?"bg-gray-100 cursor-not-allowed":"bg-white","\n            "),children:Object.entries(j).map(e=>{let[t,r]=e;return(0,s.jsx)("option",{value:t,children:r},t)})})}function N(e){let{resolution:t,className:r=""}=e;return(0,s.jsx)("span",{className:"\n            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border\n            ".concat(y[t]," ").concat(r,"\n        "),children:j[t]})}let w={REGULARISE:"R\xe9gularis\xe9",AJOURNE:"Ajourn\xe9",NON_EXAMINE:"Non examin\xe9",REJETE:"Rejet\xe9"},E={REGULARISE:"bg-green-100 text-green-800 border-green-200",AJOURNE:"bg-orange-100 text-orange-800 border-orange-200",NON_EXAMINE:"bg-gray-100 text-gray-800 border-gray-200",REJETE:"bg-red-100 text-red-800 border-red-200"};function k(e){let{status:t,className:r=""}=e;return(0,s.jsx)("span",{className:"\n            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border\n            ".concat(E[t]," ").concat(r,"\n        "),children:w[t]})}var C=r(767);function S(e){let t=[];return e.trim().split(/\s+/).forEach(e=>{let r=e.split(",");if(r.length>=2){let e=parseFloat(r[0]),s=parseFloat(r[1]),n=r.length>2?parseFloat(r[2]):0;isNaN(e)||isNaN(s)||t.push([e,s,n])}}),t}function A(e){let{onKMLLoaded:t,onError:r}=e,[a,l]=(0,n.useState)(!1),o=(0,n.useRef)(null),i=async e=>{var s;let n=null==(s=e.target.files)?void 0:s[0];if(!n)return;let a=n.name.toLowerCase().split(".").pop();if(!["kml"].includes(a||""))return void r("Veuillez s\xe9lectionner un fichier KML");l(!0);try{let e;if("kmz"===a)throw Error("Les fichiers KMZ ne sont pas encore support\xe9s. Veuillez extraire le fichier KML et l'importer directement.");e=await n.text();let r=new DOMParser().parseFromString(e,"text/xml");if(r.querySelector("parsererror"))throw Error("Le fichier KML n'est pas valide");let s=function(e){let t=[];return e.querySelectorAll("Placemark").forEach(e=>{let r={type:"Feature",properties:{},geometry:null},s=e.querySelector("name");s&&(r.properties.name=s.textContent);let n=e.querySelector("description");n&&(r.properties.description=n.textContent);let a=e.querySelector("ExtendedData");a&&a.querySelectorAll("Data").forEach(e=>{let t=e.getAttribute("name"),s=e.querySelector("value");t&&s&&(r.properties[t]=s.textContent)});let l=e.querySelector("Point coordinates"),o=e.querySelector("LineString coordinates"),i=e.querySelector("Polygon outerBoundaryIs LinearRing coordinates");if(l){let e=S(l.textContent||"");e.length>0&&(r.geometry={type:"Point",coordinates:e[0]})}else if(o){let e=S(o.textContent||"");e.length>0&&(r.geometry={type:"LineString",coordinates:e})}else if(i){let e=S(i.textContent||"");e.length>0&&(r.geometry={type:"Polygon",coordinates:[e]})}r.geometry&&t.push(r)}),{type:"FeatureCollection",features:t}}(r);if(!s||!s.features||0===s.features.length)throw Error("Aucune donn\xe9e g\xe9ographique trouv\xe9e dans le fichier");t(s,n.name)}catch(e){console.error("Erreur lors du traitement du fichier:",e),r(e instanceof Error?e.message:"Erreur lors du traitement du fichier")}finally{l(!1),o.current&&(o.current.value="")}};return(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{ref:o,type:"file",accept:".kml",onChange:i,className:"hidden"}),(0,s.jsx)(C.Button,{onClick:()=>{var e;null==(e=o.current)||e.click()},disabled:a,variant:"outline",className:"flex items-center gap-2",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,s.jsx)("span",{children:"Traitement..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,s.jsx)("span",{children:"Importer KML"})]})}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Format support\xe9: .kml"})]})}function L(e){let{cas:t,onKMLUpdated:r,onKMLRemoved:a,onError:l,readOnly:o=!1}=e,[i,c]=(0,n.useState)(!1),[d,u]=(0,n.useState)(!1),m=async(e,s)=>{if(!t.id)return void l("ID du cas manquant");u(!0);try{var n,a;let l=await fetch("/api/cas/".concat(t.id,"/kml"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({kmlData:e,kmlFileName:s,geojson:(null==(a=e.features)||null==(n=a[0])?void 0:n.geometry)||null})});if(!l.ok)throw Error("Erreur lors de la mise \xe0 jour du KML");await l.json(),r(t.id,e,s),c(!1)}catch(e){console.error("Erreur:",e),l(e instanceof Error?e.message:"Erreur lors de la mise \xe0 jour")}finally{u(!1)}},x=async()=>{if(!t.id)return void l("ID du cas manquant");if(confirm("\xcates-vous s\xfbr de vouloir supprimer les donn\xe9es KML de ce cas ?")){u(!0);try{if(!(await fetch("/api/cas/".concat(t.id,"/kml"),{method:"DELETE"})).ok)throw Error("Erreur lors de la suppression du KML");a(t.id)}catch(e){console.error("Erreur:",e),l(e instanceof Error?e.message:"Erreur lors de la suppression")}finally{u(!1)}}},g=(()=>{if(!t.kmlData)return null;let e=t.kmlData.features||[],r=new Set(e.map(e=>{var t;return null==(t=e.geometry)?void 0:t.type}).filter(Boolean));return{featureCount:e.length,types:Array.from(r),fileName:t.kmlFileName||"Fichier KML"}})();return(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Donn\xe9es g\xe9ographiques KML"}),g&&!i&&!o&&(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(C.Button,{onClick:()=>c(!0),variant:"outline",disabled:d,children:"Modifier"}),(0,s.jsx)(C.Button,{onClick:x,variant:"outline",disabled:d,className:"text-red-600 hover:text-red-700",children:"Supprimer"})]}),o&&g&&(0,s.jsx)("div",{className:"text-sm text-gray-500 italic",children:"Mode lecture seule - Modification non autoris\xe9e"})]}),d&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,s.jsx)("span",{className:"ml-2 text-gray-600",children:"Traitement en cours..."})]}),!d&&(0,s.jsx)(s.Fragment,{children:g&&!i?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"font-medium text-green-800",children:"Donn\xe9es KML attach\xe9es"})]}),(0,s.jsxs)("div",{className:"text-sm text-green-700 space-y-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Fichier :"})," ",g.fileName]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"\xc9l\xe9ments :"})," ",g.featureCount]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Types :"})," ",g.types.join(", ")]})]})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Ces donn\xe9es g\xe9ographiques sont affich\xe9es sur la carte et peuvent \xeatre utilis\xe9es pour localiser pr\xe9cis\xe9ment ce cas d'assainissement."})]}):i?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-blue-800 text-sm",children:"Importez un nouveau fichier KML pour remplacer les donn\xe9es existantes."})}),(0,s.jsx)(A,{onKMLLoaded:m,onError:l}),(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsx)(C.Button,{onClick:()=>c(!1),variant:"outline",children:"Annuler"})})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 text-center",children:[(0,s.jsx)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"})}),(0,s.jsx)("p",{className:"text-gray-600 mb-3",children:"Aucune donn\xe9e g\xe9ographique KML attach\xe9e \xe0 ce cas"}),o?(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Mode lecture seule - Aucune donn\xe9e KML disponible"}):(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Importez un fichier KML pour enrichir ce cas avec des donn\xe9es g\xe9ographiques pr\xe9cises"})]}),!o&&(0,s.jsx)(A,{onKMLLoaded:m,onError:l})]})})]})}var P=r(9662),_=r(8682);let T=(0,d.default)(()=>Promise.resolve().then(r.bind(r,3084)).then(e=>e.default),{loadableGenerated:{webpack:()=>[3084]},ssr:!1,loading:()=>(0,s.jsx)(m,{height:32,width:32})}),M=(0,d.default)(()=>r.e(3109).then(r.bind(r,3109)).then(e=>e.default),{loadableGenerated:{webpack:()=>[3109]},ssr:!1,loading:()=>(0,s.jsx)(m,{height:20,width:200})}),D=(0,d.default)(()=>Promise.resolve().then(r.bind(r,767)).then(e=>e.default),{loadableGenerated:{webpack:()=>[767]},ssr:!1,loading:()=>(0,s.jsx)(m,{height:36,width:100})}),R=(0,d.default)(()=>r.e(345).then(r.bind(r,345)).then(e=>e.default),{loadableGenerated:{webpack:()=>[345]},ssr:!1,loading:()=>(0,s.jsx)(m,{height:36,width:200})}),O=(0,d.default)(()=>r.e(1381).then(r.bind(r,1381)).then(e=>e.default),{loadableGenerated:{webpack:()=>[1381]},ssr:!1,loading:()=>(0,s.jsx)(m,{height:80,width:200})}),F=(0,d.default)(()=>r.e(2166).then(r.bind(r,2166)).then(e=>e.default),{loadableGenerated:{webpack:()=>[2166]},ssr:!1,loading:()=>(0,s.jsx)(m,{height:200,width:400})});function I(){var e,t,r,l;let d=(0,a.useParams)();(0,a.useSearchParams)();let j=d.id,{afterCreate:y,afterUpdate:w,afterDelete:E}=(0,u.gD)(),[C,S]=(0,n.useState)(null),[A,I]=(0,n.useState)([]),[q,z]=(0,n.useState)([]),[B,V]=(0,n.useState)([]),[J,U]=(0,n.useState)([]),[K,G]=(0,n.useState)(!0),[H,W]=(0,n.useState)(!0),[Z,X]=(0,n.useState)(!0),[Q,Y]=(0,n.useState)(!0),[$,ee]=(0,n.useState)(null),[et,er]=(0,n.useState)({description:"",secteurId:"",blocageDate:""}),[es,en]=(0,n.useState)({description:"",secteurId:"",blocageDate:""}),[ea,el]=(0,n.useState)(!1),[eo,ei]=(0,n.useState)(null),[ec,ed]=(0,n.useState)(null),[eu,em]=(0,n.useState)(!1),[ex,eg]=(0,n.useState)(!1),[eh,ep]=(0,n.useState)({}),[ef,eb]=(0,n.useState)(!1),[ej,ey]=(0,n.useState)(null),[ev,eN]=(0,n.useState)(!1),[ew,eE]=(0,n.useState)(null),[ek,eC]=(0,n.useState)(""),[eS,eA]=(0,n.useState)(null),[eL,eP]=(0,n.useState)(""),[e_,eT]=(0,n.useState)("ACCEPTE"),[eM,eD]=(0,n.useState)(""),[eR,eO]=(0,n.useState)(!1),[eF,eI]=(0,n.useState)([]),[eq,ez]=(0,n.useState)(!1),[eB,eV]=(0,n.useState)(null),[eJ,eU]=(0,n.useState)(null),[eK,eG]=(0,n.useState)(null),[eH,eW]=(0,n.useState)(!1),[eZ,eX]=(0,n.useState)(null),eQ=async()=>{if(C){eW(!0),eX(null);try{var e;let t=await fetch("/api/cas/".concat(j,"/pdf"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok){let e="Erreur HTTP ".concat(t.status);try{let r=t.headers.get("content-type");if(r&&r.includes("application/json")){let r=await t.json();e=r.error||r.message||e}else{let r=await t.text();r&&(e=r)}}catch(e){console.warn("Could not parse error response:",e)}switch(t.status){case 401:e="Session expir\xe9e. Veuillez vous reconnecter.";break;case 403:e="Vous n'avez pas les permissions pour t\xe9l\xe9charger ce PDF.";break;case 404:e="Dossier non trouv\xe9.";break;case 500:e="Erreur serveur lors de la g\xe9n\xe9ration du PDF. Veuillez r\xe9essayer."}throw Error(e)}let r=await t.blob();if(r.type&&!r.type.includes("pdf"))throw Error("Le serveur n'a pas retourn\xe9 un fichier PDF valide.");let s=window.URL.createObjectURL(r),n=document.createElement("a");n.href=s;let a=new Date().toISOString().split("T")[0],l=(null==(e=C.nom)?void 0:e.replace(/[^a-zA-Z0-9]/g,"_"))||"Dossier";n.download="Fiche_Cas_".concat(C.id,"_").concat(l,"_").concat(a,".pdf"),document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(s)}catch(e){console.error("Erreur lors du t\xe9l\xe9chargement PDF:",e),eX(e instanceof Error?e.message:"Erreur inconnue lors de la g\xe9n\xe9ration du PDF")}finally{eW(!1)}}},eY=async e=>{let t=e.map(e=>e.resolution||"ATTENTE"),r=t.length>0&&t.every(e=>"ACCEPTE"===e);if(C&&C.regularisation!==r){S(t=>t?{...t,regularisation:r,blocage:e}:null);try{await c.uE.put("/api/cas/".concat(j),{regularisation:r})}catch(e){console.error("Failed to update Cas regularisation status on server:",e)}}else C&&S(t=>t?{...t,blocage:e}:null)};(0,n.useEffect)(()=>{if(C&&ex){var e,t;ep({nom:C.nom||"",nif:C.nif,nin:C.nin,superficie:C.superficie||void 0,regularisation:C.regularisation||!1,observation:C.observation,problematiqueId:C.problematiqueId||"",genre:C.genre||void 0,date_depot:C.date_depot?new Date(C.date_depot).toISOString().split("T")[0]:"",communeIds:C.communes?C.communes.map(e=>String(e.id)):[],encrageId:(null==(t=C.problematique)||null==(e=t.encrage)?void 0:e.id)||""})}else ex||(ep({}),ey(null))},[C,ex]),(0,n.useEffect)(()=>{C&&ex&&eU(C.geojson||null)},[C,ex]);let e$=e=>{ep(t=>{let r=t.communeIds||[],s=r.includes(e)?r.filter(t=>t!==e):[...r,e];return{...t,communeIds:s}})};(0,n.useEffect)(()=>{!async function(){console.time("fetchMain"),G(!0);try{let e=await c.uE.get("/api/cas/".concat(j,"?mainOnly=true"));S(e)}finally{G(!1),console.timeEnd("fetchMain")}}()},[j]),(0,n.useEffect)(()=>{C&&(async()=>{W(!0),ez(!0),X(!0),Y(!0),console.time("fetchBlocages"),console.time("fetchAllCommunes"),console.time("fetchProblematiques"),console.time("fetchEncrages");try{let[e,t,r,s]=await Promise.all([c.uE.get("/api/cas/".concat(j,"/blocages")),c.uE.get("/api/communes"),c.uE.get("/api/problematiques"),c.uE.get("/api/encrages")]);I(e),eI(t),V(r);let n=Array.isArray(s)?s:Array.isArray(null==s?void 0:s.data)?s.data:[];U(n)}catch(e){console.error("Erreur lors du chargement parall\xe8le :",e)}finally{W(!1),ez(!1),X(!1),Y(!1),console.timeEnd("fetchBlocages"),console.timeEnd("fetchAllCommunes"),console.timeEnd("fetchProblematiques"),console.timeEnd("fetchEncrages")}})()},[C,j]);let e0=async()=>{console.time("fetchAllCommunes"),ez(!0),eV(null);try{let e=await c.uE.get("/api/communes");eI(e)}catch(r){var e,t;console.error("Failed to fetch communes:",r),eV((null==(t=r.response)||null==(e=t.data)?void 0:e.error)||r.message||"Failed to fetch communes")}finally{ez(!1),console.timeEnd("fetchAllCommunes")}},e1=async()=>{console.time("fetchProblematiques");try{let e=await c.uE.get("/api/problematiques");V(e)}catch(e){console.error("Failed to fetch problematiques:",e)}finally{console.timeEnd("fetchProblematiques")}},e2=async()=>{console.time("fetchEncrages");try{let e=await c.uE.get("/api/encrages");U(e)}catch(e){console.error("Failed to fetch encrages:",e)}finally{console.timeEnd("fetchEncrages")}};async function e5(e,t){let r=A.find(t=>t.id===e);r&&(eE(r),eC(r.solution?new Date(r.solution).toISOString().split("T")[0]:""),t?(eT(r.resolution||"ACCEPTE"),eD(r.detail_resolution||"")):(eT("ACCEPTE"),eD("")),eP(""),eN(!0))}let e4=async e=>{if(e&&e.preventDefault(),!ew||!ek)return void eA("Solution date is required");eA(null);try{if(!await c.uE.put("/api/blocages/".concat(ew.id),{regularise:"ACCEPTE"===e_,solution:new Date(ek).toISOString(),resolution:e_,detail_resolution:eM||null}))throw Error("Failed to submit solution date");let e=A.map(e=>e.id===ew.id?{...e,regularise:"ACCEPTE"===e_,solution:new Date(ek),resolution:e_,detail_resolution:eM||null}:e);I(e),await eY(e),console.log("\uD83D\uDD04 D\xe9clenchement du rafra\xeechissement apr\xe8s mise \xe0 jour de blocage"),await w("blocage"),eN(!1),eC(""),eT("ACCEPTE"),eD(""),eE(null)}catch(e){console.error("Error submitting solution date:",e),eA(e.message||"Failed to submit solution")}},e3=async()=>{G(!0),ee(null);try{let e=await c.uE.get("/api/cas/".concat(j));S(e);let t=e.blocage||[];I(t),e&&await eY(t)}catch(r){var e,t;console.error("Failed to fetch case details:",r),ee((null==(t=r.response)||null==(e=t.data)?void 0:e.error)||r.message||"Failed to fetch case details")}G(!1)},e6=async()=>{try{let e=await c.uE.get("/api/secteurs");z(e)}catch(e){console.error("Failed to fetch sectors:",e)}},e7=e=>{er({...et,[e.target.name]:e.target.value})},e9=async e=>{if(e.preventDefault(),!et.description||!et.secteurId)return void ei("La description et le secteur sont requis.");el(!0),ei(null);try{await c.uE.post("/api/blocages",{...et,casId:j,blocage:et.blocageDate?new Date(et.blocageDate):null});let e=await c.uE.get("/api/cas/".concat(j,"/blocages"));I(e),await eY(e),er({description:"",secteurId:"",blocageDate:""}),eO(!1),console.log("\uD83D\uDD04 D\xe9clenchement du rafra\xeechissement apr\xe8s cr\xe9ation de blocage"),await y("blocage")}catch(e){ei(e.message||"Erreur lors de l'ajout du blocage.")}finally{el(!1)}},e8=e=>{ed(e),em(!0)},te=async()=>{if(ec){ee(null);try{await c.uE.delete("/api/blocages/".concat(ec));let e=A.filter(e=>e.id!==ec);I(e),await eY(e),ed(null),em(!1),await E("blocage")}catch(r){var e,t;console.error("Error deleting blocage:",r),ee((null==(t=r.response)||null==(e=t.data)?void 0:e.error)||r.message||"Error deleting blocage."),ed(null),em(!1)}}},tt=()=>{ed(null),em(!1)},tr=e=>{let{name:t,value:r,type:s}=e.target,n=r;"number"===s&&(n=""===r?void 0:parseFloat(r)),"checkbox"===e.target.type&&(n=e.target.checked),ep(e=>({...e,[t]:n}))},ts=async e=>{e.preventDefault(),eb(!0),ey(null);try{let e=eh.communeIds?eF.filter(e=>eh.communeIds.includes(String(e.id))).map(e=>({nom:e.nom,wilayaId:e.wilayaId})):[],{communeIds:t,...r}=eh,s={...r,communes:e,geojson:eJ||null};console.log("Donn\xe9es envoy\xe9es pour la modification:",s),await c.uE.put("/api/cas/".concat(j),s),eg(!1),await w("cas")}catch(e){console.error("Erreur lors de la modification:",e),ey("Erreur lors de la modification du cas.")}eb(!1)};(0,n.useEffect)(()=>{e0(),e1(),e2()},[]),(0,u.kF)("cas-details",e3,[j]),(0,u.kF)("communes-details",e0,[]),(0,u.kF)("problematiques-details",e1,[]),(0,u.kF)("encrages-details",e2,[]),(0,n.useEffect)(()=>{e6()},[]);let[tn,ta]=(0,n.useState)(null),{canWrite:tl,canDelete:to,canUploadFiles:ti,isViewer:tc}=(0,_.Sk)();if((0,n.useEffect)(()=>{!async function(){try{let e=await c.uE.get("/api/auth/me");ta(e)}catch(e){ta(null)}}()},[]),$)return(0,s.jsxs)("div",{className:"  mx-auto p-1 text-center",children:[(0,s.jsx)(M,{message:$}),(0,s.jsx)(D,{onClick:e3,className:"mt-4",children:"R\xe9essayer"})]});if(!C)return(0,s.jsx)("div",{className:"  mx-auto p-1 text-center",children:K?(0,s.jsx)(T,{}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{children:"Cas non trouv\xe9."}),(0,s.jsx)(o(),{href:"/cas",className:"text-purple-600 hover:underline",children:"Retour \xe0 la liste des cas"})]})});let td=n.memo(function(e){var t;let{blocage:r,blocageToDeleteId:n,onDelete:a,onCancelDelete:l,onConfirmDelete:o,onToggleRegularisation:i,isDeleteModalOpen:c,setIsDeleteModalOpen:d}=e;return(0,s.jsxs)("div",{className:"p-4 bg-white rounded-md shadow border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:r.description}),(0,s.jsxs)("p",{className:"text-xs text-gray-600",children:["Secteur: ",(null==(t=r.secteur)?void 0:t.nom)||"N/A"]}),(0,s.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"R\xe9solution:"}),(0,s.jsx)(N,{resolution:r.resolution||"ATTENTE",className:"text-xs"})]}),r.detail_resolution&&(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-1 italic",children:r.detail_resolution})]}),(0,s.jsx)("div",{className:"flex space-x-2 flex-shrink-0",children:n===r.id?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(D,{variant:"primary",size:"icon",onClick:o,className:"p-1.5 rounded-full bg-green-100 hover:bg-green-200 text-green-700","aria-label":"Confirmer la suppression",children:(0,s.jsx)(x,{className:"w-5 h-5"})}),(0,s.jsx)(D,{variant:"secondary",size:"icon",onClick:()=>d(!1),className:"p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700","aria-label":"Annuler la suppression",children:(0,s.jsx)(g,{className:"w-5 h-5"})})]}):(0,s.jsx)(P.g1,{children:(0,s.jsx)(D,{variant:"destructive",size:"icon",onClick:()=>a(r.id),className:"ml-2 flex-shrink-0 p-1.5 rounded-full hover:bg-red-100 hover:text-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500","aria-label":"Supprimer le blocage",title:to?void 0:"Vous n'avez pas les permissions pour supprimer des blocages",disabled:!to,children:(0,s.jsx)(h.A,{className:"w-5 h-5"})})})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-2 pt-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(P._T,{fallback:(0,s.jsx)("div",{className:"inline-flex h-6 w-11 items-center rounded-full bg-gray-200 border border-gray-300 opacity-50 cursor-not-allowed",children:(0,s.jsx)("span",{className:"inline-block h-4 w-4 rounded-full bg-white shadow transform transition-transform ".concat(r.regularise?"translate-x-6":"translate-x-1")})}),children:(0,s.jsx)(b,{checked:r.regularise,onChange:()=>i(r.id,r.regularise),disabled:!tl,className:"ACCEPTE"===r.resolution?"bg-green-600 border-green-600 focus:ring-green-500":"REJETE"===r.resolution?"bg-red-600 border-red-600 focus:ring-red-500":"AJOURNE"===r.resolution?"bg-orange-600 border-orange-600 focus:ring-orange-500":"bg-gray-400 border-gray-400 focus:ring-gray-500 inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border"})}),(0,s.jsx)("span",{className:"ml-2 text-sm font-medium cursor-pointer ".concat("ACCEPTE"===r.resolution?"text-green-700":"REJETE"===r.resolution?"text-red-700":"AJOURNE"===r.resolution?"text-orange-700":"text-gray-600"),onClick:()=>i(r.id,r.regularise),children:"ACCEPTE"===r.resolution?"Accept\xe9":"REJETE"===r.resolution?"Rejet\xe9":"AJOURNE"===r.resolution?"Ajourn\xe9":"En attente"}),r.solution&&(0,s.jsxs)("span",{className:"ml-3 text-sm ".concat(r.regularise?"text-green-700":"text-red-700"),children:[r.regularise?"Solutionn\xe9":""," ","le:"," ",new Date(r.solution).toLocaleDateString("fr-CA")]})]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:r.blocage&&(0,s.jsxs)("p",{className:"text-xs text-gray-400",children:["Ajout\xe9 le:"," ",new Date(r.blocage).toLocaleDateString("fr-CA")]})})]})]})}),tu=n.memo(function(e){let{casData:t}=e,r=function(e){if(!e||0===e.length)return"NON_EXAMINE";let t=e.map(e=>e.resolution||"ATTENTE");return t.every(e=>"ATTENTE"===e)?"NON_EXAMINE":t.some(e=>"REJETE"===e)?"REJETE":t.some(e=>"AJOURNE"===e)?"AJOURNE":t.every(e=>"ACCEPTE"===e)?"REGULARISE":"NON_EXAMINE"}((null==t?void 0:t.blocage)||[]);return(0,s.jsx)("div",{className:"ml-4",children:(0,s.jsx)(k,{status:r})})});return(0,s.jsxs)("div",{className:"  mx-auto p-2 md:p-2 bg-gray-50 min-h-screen",children:[K&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50 transition-opacity duration-300 animate-fade-in",children:(0,s.jsx)(T,{})}),(0,s.jsx)(F,{isOpen:eR,onClose:()=>eO(!1),title:"Ajouter une Nouvelle Contrainte",children:(0,s.jsxs)("form",{onSubmit:e9,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description de la contrainte"}),(0,s.jsx)(O,{name:"description",id:"description",value:et.description,onChange:e7,required:!0,rows:3})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"secteurId",className:"block text-sm font-medium text-gray-700",children:"Secteur Concern\xe9"}),(0,s.jsxs)("select",{name:"secteurId",id:"secteurId",value:et.secteurId,onChange:e7,required:!0,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",children:"S\xe9lectionner un secteur"}),q.map(e=>(0,s.jsx)("option",{value:e.id,children:e.nom},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"blocageDate",className:"block text-sm font-medium text-gray-700",children:"Date de blocage"}),(0,s.jsx)(R,{type:"date",name:"blocageDate",id:"blocageDate",value:et.blocageDate,onChange:e7,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"})]}),eo&&(0,s.jsx)(M,{message:eo}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)(D,{type:"button",variant:"secondary",onClick:()=>eO(!1),children:"Annuler"}),(0,s.jsx)(P._T,{children:(0,s.jsx)(D,{type:"submit",disabled:ea||!tl,title:tl?void 0:"Vous n'avez pas les permissions pour ajouter des blocages",children:ea?(0,s.jsx)(T,{}):"Ajouter"})})]})]})}),ex?(0,s.jsxs)("form",{onSubmit:ts,className:"bg-white shadow-lg rounded-xl p-6 border border-gray-200 mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Modifier le Cas"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Communes"}),eq&&(0,s.jsx)(T,{}),eB&&(0,s.jsx)(M,{message:eB}),!eq&&!eB&&eF.length>0&&(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 max-h-60 overflow-y-auto p-2 border rounded",children:eF.map(e=>{var t;return(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"commune-".concat(e.id),name:"communes",value:e.id,checked:(null==(t=eh.communeIds)?void 0:t.includes(String(e.id)))||!1,onChange:()=>e$(String(e.id)),className:"h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"}),(0,s.jsx)("label",{htmlFor:"commune-".concat(e.id),className:"ml-2 block text-sm text-gray-900",children:e.nom})]},e.id)})}),!eq&&!eB&&0===eF.length&&(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Aucune commune disponible."})]}),ej&&(0,s.jsx)(M,{message:ej}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nom_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Nom du Cas"}),(0,s.jsx)(R,{type:"text",name:"nom",id:"nom_edit",value:eh.nom||"",onChange:tr,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"date_depot_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date d\xe9p\xf4t dossier"}),(0,s.jsx)(R,{type:"date",name:"date_depot",id:"date_depot_edit",value:eh.date_depot||"",onChange:tr})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nif_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"NIF"}),(0,s.jsx)(R,{type:"text",name:"nif",id:"nif_edit",value:eh.nif||"",onChange:tr})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nin_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"NIN"}),(0,s.jsx)(R,{type:"text",name:"nin",id:"nin_edit",value:eh.nin||"",onChange:tr})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"superficie_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Superficie (Ha)"}),(0,s.jsx)(R,{type:"number",name:"superficie",id:"superficie_edit",value:eh.superficie||"",onChange:tr})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"genre_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Genre"}),(0,s.jsxs)("select",{name:"genre",id:"genre_edit",value:eh.genre||"",onChange:tr,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",children:"S\xe9lectionner un genre"}),(0,s.jsx)("option",{value:i.TypePersonne.PERSONNE_PHYSIQUE,children:"Personne Physique"}),(0,s.jsx)("option",{value:i.TypePersonne.PERSONNE_MORALE,children:"Personne Morale"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"problematiqueId_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Probl\xe9matique"}),(0,s.jsxs)("select",{name:"problematiqueId",id:"problematiqueId_edit",value:eh.problematiqueId||"",onChange:tr,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",children:"S\xe9lectionner une probl\xe9matique"}),B.filter(e=>e.encrageId===eh.encrageId).map(e=>(0,s.jsx)("option",{value:e.id,children:e.problematique},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"encrageId_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Encrage"}),(0,s.jsxs)("select",{name:"encrageId",id:"encrageId_edit",value:eh.encrageId||"",onChange:tr,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",disabled:!0,children:"S\xe9lectionner un encrage"}),(Array.isArray(J)?J:[]).map(e=>(0,s.jsx)("option",{value:e.id,children:e.nom},e.id))]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{htmlFor:"observation_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Observation"}),(0,s.jsx)(O,{name:"observation",id:"observation_edit",value:eh.observation||"",onChange:tr,rows:3})]}),(0,s.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,s.jsx)(D,{type:"button",onClick:()=>eg(!1),variant:"secondary",children:"Annuler"}),(0,s.jsx)(P._T,{children:(0,s.jsx)(D,{type:"submit",isLoading:ef,disabled:ef||!tl,title:tl?void 0:"Vous n'avez pas les permissions pour modifier ce dossier",children:"Enregistrer"})})]})]}):(0,s.jsxs)("div",{className:"bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-100 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 ",children:K?(0,s.jsx)(m,{height:48,width:"100%"}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"flex items-center gap-4 min-w-0",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 truncate",children:C.nom}),(0,s.jsx)(tu,{casData:C})]})})}),(0,s.jsx)("div",{className:"flex items-center justify-end gap-4 min-w-0",children:(0,s.jsxs)("div",{className:"flex space-x-2 flex-shrink-0",children:[(0,s.jsxs)(o(),{href:"/cas/".concat(j,"/cartographie"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"})}),"Cartographie"]}),(0,s.jsxs)(D,{onClick:eQ,variant:"secondary",className:"flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white",isLoading:eH,disabled:eH,title:"T\xe9l\xe9charger la fiche PDF du dossier",children:[(0,s.jsx)(p.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:eH?"G\xe9n\xe9ration...":"Fiche PDF"})]}),(0,s.jsx)(P._T,{children:(0,s.jsx)(D,{onClick:()=>eg(!ex),variant:ex?"secondary":"primary",title:tl?void 0:"Vous n'avez pas les permissions pour modifier ce dossier",disabled:!tl,children:ex?"Annuler la Modification":"Modifier le Dossier"})}),(0,s.jsx)(P._T,{children:(0,s.jsxs)(D,{onClick:()=>eO(!0),variant:"primary",className:"flex items-center space-x-2",title:tl?void 0:"Vous n'avez pas les permissions pour ajouter des blocages",disabled:!tl,children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Ajouter une Contrainte"})]})})]})}),tc&&(0,s.jsx)("div",{className:"p-3",children:(0,s.jsx)(P.Au,{message:"Vous \xeates en mode lecture seule. Vous pouvez consulter toutes les informations du dossier mais ne pouvez pas les modifier."})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-2 gap-6 xl:gap-8 p-3",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 space-y-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M10 9a3 3 0 100-6 3 3 0 000 6zM6 8a2 2 0 11-4 0 2 2 0 014 0zM1.49 15.326a.78.78 0 01-.358-.442 3 3 0 014.308-3.516 6.484 6.484 0 00-1.905 3.959c-.023.222-.014.442.025.654a4.97 4.97 0 01-2.07-.655zM16.44 15.98a4.97 4.97 0 002.07-.654.78.78 0 00.357-.442 3 3 0 00-4.308-3.517 6.484 6.484 0 011.907 3.96 2.32 2.32 0 01-.026.654zM18 8a2 2 0 11-4 0 2 2 0 014 0zM5.304 16.19a.844.844 0 01-.277-.71 5 5 0 019.947 0 .843.843 0 01-.277.71A6.975 6.975 0 0110 18a6.974 6.974 0 01-4.696-1.81z"})}),"Informations G\xe9n\xe9rales"]}),K?(0,s.jsx)(m,{height:120,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsxs)("dl",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Identifiant"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:C.nif?"NIF: ".concat(C.nif):C.nin?"NIN: ".concat(C.nin):"Non sp\xe9cifi\xe9"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Genre"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:C.genre===i.TypePersonne.PERSONNE_PHYSIQUE?"Personne Physique":C.genre===i.TypePersonne.PERSONNE_MORALE?"Personne Morale":"Non sp\xe9cifi\xe9"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Commune(s)"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:C.communes&&C.communes.length>0?C.communes.map(e=>e.nom).join(", "):"Non sp\xe9cifi\xe9e"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Superficie"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:C.superficie?"".concat(C.superficie," Ha"):"Non sp\xe9cifi\xe9e"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Date d\xe9p\xf4t dossier"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:C.date_depot?new Date(C.date_depot).toLocaleDateString("fr-CA"):"Non sp\xe9cifi\xe9e"})]})]})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center mb-4",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-yellow-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zm0 16a3 3 0 01-3-3h6a3 3 0 01-3 3z",clipRule:"evenodd"})}),"Observations"]}),K?(0,s.jsx)(m,{height:40,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:C.observation||"Aucune observation"})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center mb-4",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-blue-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})}),"Probl\xe9matique Associ\xe9e"]}),K?(0,s.jsx)(m,{height:80,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsxs)("dl",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-blue-700",children:"Probl\xe9matique"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:null==(e=C.problematique)?void 0:e.problematique})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-blue-700",children:"Encrage Juridique"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:null==(t=C.problematique)?void 0:t.encrage.nom})]})]})]}),(0,s.jsxs)("div",{className:"bg-green-50 rounded-lg p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center mb-4",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-green-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})}),"Informations du cr\xe9ateur"]}),K?(0,s.jsx)(m,{height:60,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsxs)("dl",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-green-700",children:"Nom d'utilisateur"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:null==(r=C.user)?void 0:r.username})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-green-700",children:"R\xf4le"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:null==(l=C.user)?void 0:l.role})]})]})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-2 bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100",children:[(0,s.jsx)("div",{className:"px-6 py-5 border-b border-gray-100 bg-purple-50",children:(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,s.jsxs)("svg",{className:"w-6 h-6 mr-2 text-purple-600",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{d:"M3.505 2.365A41.369 41.369 0 009 2c1.863 0 3.697.124 5.495.365 1.247.167 2.18 1.108 2.435 2.268a4.45 4.45 0 00-.577-.069 43.141 43.141 0 00-4.706 0C9.229 4.696 7.5 6.727 7.5 8.998v2.24c0 1.413.67 2.735 1.76 3.562l-2.98 2.98A.75.75 0 015 17.25v-3.443c-.501-.048-1-.106-1.495-.172C2.033 13.438 1 12.162 1 10.72V5.28c0-1.441 1.033-2.717 2.505-2.914z"}),(0,s.jsx)("path",{d:"M14 6c-.762 0-1.52.02-2.271.062C10.157 6.148 9 7.472 9 8.998v2.24c0 1.519 1.147 2.839 2.71 2.935.214.013.428.024.642.034.2.009.385.09.518.224l2.35 2.35a.75.75 0 001.28-.531v-2.07c1.453-.195 2.5-1.463 2.5-2.915V8.998c0-1.526-1.157-2.85-2.729-2.936A41.645 41.645 0 0014 6z"})]}),"Niveaux de Contrainte"]})}),(0,s.jsx)("div",{className:"p-1",children:H?(0,s.jsx)(m,{height:80,width:"100%"}):(0,s.jsx)("div",{className:"mt-2 space-y-3",children:A.length>0?A.map(e=>(0,s.jsx)(td,{blocage:e,blocageToDeleteId:ec,onDelete:e8,onCancelDelete:tt,onConfirmDelete:te,onToggleRegularisation:e5,isDeleteModalOpen:eu,setIsDeleteModalOpen:em},e.id)):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-gray-400",children:[(0,s.jsx)("svg",{className:"w-12 h-12 mb-2",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.75 17L6 21m0 0l-3.75-4M6 21V3"})}),(0,s.jsx)("span",{className:"text-lg",children:"Aucun point de blocage identifi\xe9 pour ce cas."})]})})})]}),(0,s.jsx)(F,{isOpen:eu,onClose:tt,title:"\xcates-vous s\xfbr de vouloir supprimer ce blocage ? Cette action est irr\xe9versible.",children:(0,s.jsxs)("div",{className:"mt-4 flex justify-end space-x-3",children:[(0,s.jsxs)(D,{variant:"secondary",onClick:tt,children:[" ","Annuler"]}),(0,s.jsx)(D,{variant:"destructive",onClick:te,children:"Supprimer"})]})}),ev&&ew&&(0,s.jsx)(F,{isOpen:ev,onClose:()=>{eN(!1),eE(null),eA("")},title:" ".concat(ew.description),children:(0,s.jsxs)("form",{onSubmit:e4,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"solutionDate_modal",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date de R\xe9solution"}),(0,s.jsx)(R,{type:"date",name:"solutionDate",id:"solutionDate_modal",value:ek,onChange:e=>eC(e.target.value),required:!0,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"solutionResolution_modal",className:"block text-sm font-medium text-gray-700 mb-1",children:"R\xe9solution"}),(0,s.jsx)(v,{value:e_,onChange:eT,className:"mt-1 block w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"solutionDetailResolution_modal",className:"block text-sm font-medium text-gray-700 mb-1",children:"D\xe9tail de la r\xe9solution"}),(0,s.jsx)(O,{name:"solutionDetailResolution",id:"solutionDetailResolution_modal",value:eM,onChange:e=>eD(e.target.value),rows:3,placeholder:"D\xe9tails sur la r\xe9solution...",className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"})]}),eS&&(0,s.jsx)(M,{message:eS}),(0,s.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,s.jsx)(D,{type:"button",onClick:()=>{eN(!1),eE(null),eA("")},className:"bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Annuler"}),(0,s.jsx)(D,{type:"submit",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Valider"})]})]})}),C&&(0,s.jsxs)("section",{className:"mt-8",children:[(0,s.jsx)(L,{cas:C,onKMLUpdated:(e,t,r)=>{C&&S({...C,kmlData:t,kmlFileName:r}),eG(null)},onKMLRemoved:e=>{C&&S({...C,kmlData:null,kmlFileName:void 0}),eG(null)},onError:e=>{eG(e),setTimeout(()=>eG(null),5e3)},readOnly:!ti}),eK&&(0,s.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:eK})})]})]})}(0,d.default)(()=>Promise.all([r.e(9359),r.e(4612)]).then(r.bind(r,4612)),{loadableGenerated:{webpack:()=>[4612]},ssr:!1,loading:()=>(0,s.jsx)("div",{className:"w-full h-[300px] bg-gray-200 rounded animate-pulse"})})},7828:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,r(4054).createAsyncLocalStorage)()}}]);