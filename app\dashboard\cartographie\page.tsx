"use client";

export const dynamic = "force-dynamic";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { fetchApi } from "@/lib/api-client";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { FormError } from "@/app/components/FormError";
import { Button } from "@/app/components/Button";
import { Select } from "@/app/components/Select";
import MapComponent from "@/app/components/MapComponentV2";

import KMLLayerManager, { KMLLayer } from "@/app/components/KMLLayerManager";
import CasListWithZoom from "@/app/components/CasListWithZoom";

interface Cas {
    id: string;
    nom: string;
    superficie: number;
    observation?: string;
    geojson?: any;
    kmlData?: any; // Add KML data field
    kmlFileName?: string; // Add KML filename field
    regularisation: boolean;
    communes: Array<{
        id: string;
        nom: string;
    }>;
    problematique?: {
        id: string;
        problematique: string;
        encrage?: {
            id: string;
            nom: string;
        };
    };
}

interface Problematique {
    id: string;
    problematique: string;
    encrage: {
        id: string;
        nom: string;
    };
}

interface Encrage {
    id: string;
    nom: string;
}

export default function CartographiePage() {
    const router = useRouter();

    const [casList, setCasList] = useState<Cas[]>([]);
    const [problematiques, setProblematiques] = useState<Problematique[]>([]);
    const [encrages, setEncrages] = useState<Encrage[]>([]);
    const [selectedCas, setSelectedCas] = useState<Cas | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Filtres
    const [selectedEncrage, setSelectedEncrage] = useState<string>("");
    const [selectedProblematique, setSelectedProblematique] =
        useState<string>("");
    const [showRegularisedOnly, setShowRegularisedOnly] = useState(false);

    // État pour les couches KML
    const [kmlLayers, setKmlLayers] = useState<KMLLayer[]>([]);

    const [mapCenter] = useState<[number, number]>([36.75, 3.06]); // Centre par défaut (Alger)

    useEffect(() => {
        loadInitialData();
    }, []);

    useEffect(() => {
        loadCasData();
    }, [selectedEncrage, selectedProblematique, showRegularisedOnly]);

    const loadInitialData = async () => {
        try {
            setIsLoading(true);
            const [casResponse, problematiqueData, encrageData] =
                await Promise.all([
                    fetchApi<{
                        data: Cas[];
                        pagination: {
                            page: number;
                            pageSize: number;
                            totalCount: number;
                            totalPages: number;
                            hasNextPage: boolean;
                            hasPrevPage: boolean;
                        };
                    }>(
                        "/api/cas?withGeojson=true&includeKML=true&pageSize=1000"
                    ),
                    fetchApi<Problematique[]>(
                        "/api/problematiques?context=formCreation"
                    ),
                    fetchApi<any>("/api/encrages"),
                ]);

            setCasList(casResponse?.data || []);
            setProblematiques(problematiqueData || []);
            // /api/encrages retourne un objet paginé { data, pagination }
            const encragesArray = Array.isArray(encrageData)
                ? encrageData
                : Array.isArray(encrageData?.data)
                ? encrageData.data
                : [];
            setEncrages(encragesArray);
        } catch (err) {
            console.error("Erreur lors du chargement des données:", err);
            setError("Erreur lors du chargement des données");
        } finally {
            setIsLoading(false);
        }
    };

    const loadCasData = async () => {
        try {
            let url = "/api/cas?withGeojson=true&includeKML=true&pageSize=1000";
            const params = new URLSearchParams();

            if (selectedEncrage) params.append("encrageId", selectedEncrage);
            if (selectedProblematique)
                params.append("problematiqueId", selectedProblematique);
            if (showRegularisedOnly) params.append("regularisation", "true");

            if (params.toString()) {
                url += `&${params.toString()}`;
            }

            const response = await fetchApi<{
                data: Cas[];
                pagination: {
                    page: number;
                    pageSize: number;
                    totalCount: number;
                    totalPages: number;
                    hasNextPage: boolean;
                    hasPrevPage: boolean;
                };
            }>(url);

            if (response) {
                setCasList(response.data || []);
            } else {
                setCasList([]);
            }
        } catch (err) {
            console.error("Erreur lors du filtrage des cas:", err);
            setError("Erreur lors du filtrage des cas");
        }
    };

    const filteredProblematiques = selectedEncrage
        ? (Array.isArray(problematiques) ? problematiques : []).filter(
              (p) => p.encrage?.id === selectedEncrage
          )
        : Array.isArray(problematiques)
        ? problematiques
        : [];
    console.log("casList is:", casList);

    // Fix the statistics calculation - casList is already an array
    const casWithGeojson = casList.filter(
        (cas) =>
            cas.geojson?.coordinates || (cas.kmlData && cas.kmlData.features)
    );

    // Debug: Log KML data
    console.log(
        "casList with KML:",
        casList.filter((cas) => cas.kmlData)
    );
    console.log(
        "casList with geojson:",
        casList.filter((cas) => cas.geojson)
    );
    console.log("casWithGeojson count:", casWithGeojson.length);
    console.log("Total casList length:", casList.length);
    console.log("Sample cas item:", casList[0]);

    const handleCasClick = (cas: Cas) => {
        setSelectedCas(cas);
    };

    const handleViewCasDetails = (casId: string) => {
        router.push(`/cas/${casId}`);
    };

    const handleViewCasCartographie = (casId: string) => {
        router.push(`/cas/${casId}/cartographie`);
    };

    const handleZoomToCas = (casId: string) => {
        // Cette fonction sera appelée par le composant MapComponent
        // via la fonction globale window.zoomToCas
        if (typeof window !== "undefined" && (window as any).zoomToCas) {
            (window as any).zoomToCas(casId);
        }
    };

    const handleDownloadGeoData = (cas: Cas) => {
        let data: any;
        let filename: string;
        let mimeType: string;

        if (cas.kmlData && cas.kmlData.features) {
            // Download as KML - but kmlData is parsed JSON, need to convert back or download original
            // For now, download as JSON
            data = JSON.stringify(cas.kmlData, null, 2);
            filename = `${cas.nom}_geometry.json`;
            mimeType = "application/json";
        } else if (cas.geojson) {
            data = JSON.stringify(cas.geojson, null, 2);
            filename = `${cas.nom}_geometry.geojson`;
            mimeType = "application/geo+json";
        } else {
            alert("Aucune donnée géographique disponible pour ce dossier.");
            return;
        }

        const blob = new Blob([data], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    // Fonctions pour gérer les couches KML

    const handleToggleKMLLayer = (layerId: string) => {
        setKmlLayers((prev) =>
            prev.map((layer) =>
                layer.id === layerId
                    ? { ...layer, visible: !layer.visible }
                    : layer
            )
        );
    };

    const handleRemoveKMLLayer = (layerId: string) => {
        setKmlLayers((prev) => prev.filter((layer) => layer.id !== layerId));
    };

    const handleChangeKMLLayerColor = (layerId: string, color: string) => {
        setKmlLayers((prev) =>
            prev.map((layer) =>
                layer.id === layerId ? { ...layer, color } : layer
            )
        );
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-96">
                <LoadingSpinner />
            </div>
        );
    }

    if (error) {
        return <FormError message={error} />;
    }

    return (
        <div className=" mx-auto px-2 py-4">
            <div className="mb-3">
                {/* <h1 className="text-3xl font-bold text-gray-800 mb-4">
                    Cartographie des Dossiers d'Assainissement
                </h1> */}

                {/* Filtres */}
                <div className="bg-white p-2 rounded-lg shadow-sm border mb-2">
                    {/* <h2 className="text-lg font-semibold mb-3">Filtres</h2> */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Encrage juridique (programme)
                            </label>
                            <Select
                                value={selectedEncrage}
                                onChange={(e) => {
                                    setSelectedEncrage(e.target.value);
                                    setSelectedProblematique(""); // Reset problématique
                                }}
                            >
                                <option value="">Tous les encrages</option>
                                {(Array.isArray(encrages) ? encrages : []).map(
                                    (encrage) => (
                                        <option
                                            key={encrage.id}
                                            value={encrage.id}
                                        >
                                            {encrage.nom}
                                        </option>
                                    )
                                )}
                            </Select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Problématique associée
                            </label>
                            <Select
                                value={selectedProblematique}
                                onChange={(e) =>
                                    setSelectedProblematique(e.target.value)
                                }
                                disabled={!selectedEncrage}
                            >
                                <option value="">
                                    Toutes les problématiques
                                </option>
                                {filteredProblematiques.map((prob) => (
                                    <option key={prob.id} value={prob.id}>
                                        {prob.problematique}
                                    </option>
                                ))}
                            </Select>
                        </div>

                        <div className="flex items-end">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={showRegularisedOnly}
                                    onChange={(e) =>
                                        setShowRegularisedOnly(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-sm font-medium text-gray-700">
                                    Dossiers régularisés
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                {/* Statistiques */}
                <div className="bg-white p-2 rounded-lg shadow-sm border mb-1">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                        <div>
                            <div className="text-2xl font-bold text-blue-600">
                                {casList.length}
                            </div>
                            <div className="text-sm text-gray-600">
                                Total des Dossiers
                            </div>
                        </div>
                        <div>
                            <div className="text-2xl font-bold text-green-600">
                                {casWithGeojson.length}
                            </div>
                            <div className="text-sm text-gray-600">
                                Dossiers géolocalisés
                            </div>
                        </div>
                        <div>
                            <div className="text-2xl font-bold text-orange-600">
                                {casList.filter((c) => c.regularisation).length}
                            </div>
                            <div className="text-sm text-gray-600">
                                Dossiers régularisés
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Carte */}
                <div className="lg:col-span-3">
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                        <MapComponent
                            casList={casList}
                            selectedCas={selectedCas}
                            setSelectedCas={setSelectedCas}
                            onViewCasDetails={handleViewCasDetails}
                            onViewCasCartographie={handleViewCasCartographie}
                            center={mapCenter}
                            zoom={10}
                            height="600px"
                            kmlLayers={kmlLayers}
                            onZoomToCas={handleZoomToCas}
                            showOnlySelected={true}
                        />
                    </div>
                </div>

                {/* Panneau d'informations et couches */}
                <div className="lg:col-span-1 space-y-3">
                    <div className="bg-white rounded-lg shadow-lg p-2">
                        {/* <div className="flex items-center justify-between mb-2">
                            <h3 className="text-sm font-semibold text-gray-800">
                                Liste des Dossiersddd ({casList.length})
                            </h3>
                            <span className="text-xs text-gray-500">
                                {casWithGeojson.length} géolocalisés
                            </span>
                        </div> */}
                        <CasListWithZoom
                            casList={casList}
                            onZoomToCas={handleZoomToCas}
                            onViewCasDetails={handleViewCasDetails}
                            selectedCas={selectedCas}
                            onSelectCas={setSelectedCas}
                        />
                    </div>
                    {/* Gestionnaire de couches KML */}
                    {/* <KMLLayerManager
                        layers={kmlLayers}
                        onToggleLayer={handleToggleKMLLayer}
                        onRemoveLayer={handleRemoveKMLLayer}
                        onChangeLayerColor={handleChangeKMLLayerColor}
                    /> */}
                    {/* Panneau d'informations du cas sélectionné */}
                    <div className="bg-white rounded-lg shadow-lg p-4">
                        <h3 className="text-lg font-semibold mb-4">
                            {selectedCas
                                ? "Détails du Dossier sélectionné"
                                : "Sélectionnez un Dossier sur la carte"}
                        </h3>

                        {selectedCas ? (
                            <div className="space-y-4">
                                {/* <div>
                                    <h4 className="font-bold text-xl">
                                        {selectedCas.nom}
                                    </h4>
                                </div>

                                <div className="space-y-2">
                                    <div>
                                        <span className="font-medium">
                                            Superficie:
                                        </span>
                                        <span className="ml-2">
                                            {selectedCas.superficie} Ha
                                        </span>
                                    </div>

                                    <div>
                                        <span className="font-medium">
                                            Problématique:
                                        </span>
                                        <span className="m-1 font-mono text-xs">
                                            {
                                                selectedCas.problematique
                                                    ?.problematique
                                            }
                                        </span>
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Encrage:
                                        </span>
                                        <span className="m-1 font-mono text-xs">
                                            {
                                                selectedCas.problematique
                                                    ?.encrage?.nom
                                            }
                                        </span>
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Communes:
                                        </span>
                                        <span className="ml-2">
                                            {selectedCas.communes
                                                .map((c) => c.nom)
                                                .join(", ")}
                                        </span>
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Statut:
                                        </span>
                                        <span
                                            className={`ml-2 px-2 py-1 rounded text-xs ${
                                                selectedCas.regularisation
                                                    ? "bg-green-100 text-green-800"
                                                    : "bg-red-100 text-red-800"
                                            }`}
                                        >
                                            {selectedCas.regularisation
                                                ? "Régularisé"
                                                : "Non régularisé"}
                                        </span>
                                    </div>
                                </div>

                                {selectedCas.observation && (
                                    <div>
                                        <span className="font-medium">
                                            Observation:
                                        </span>
                                        <p className="mt-1 text-sm text-gray-700">
                                            {selectedCas.observation}
                                        </p>
                                    </div>
                                )} */}

                                <div className="space-y-2 pt-4 border-t">
                                    <Button
                                        onClick={() =>
                                            handleViewCasDetails(selectedCas.id)
                                        }
                                        className="w-full"
                                    >
                                        Voir tous les détails
                                    </Button>
                                    <Button
                                        onClick={() =>
                                            handleViewCasCartographie(
                                                selectedCas.id
                                            )
                                        }
                                        variant="outline"
                                        className="w-full"
                                    >
                                        Cartographie détaillée
                                    </Button>
                                    {(selectedCas.geojson ||
                                        (selectedCas.kmlData &&
                                            selectedCas.kmlData.features)) && (
                                        <Button
                                            onClick={() =>
                                                handleDownloadGeoData(
                                                    selectedCas
                                                )
                                            }
                                            variant="outline"
                                            className="w-full"
                                        >
                                            Télécharger GeoJSON/KML
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div className="text-center text-gray-500 py-8">
                                <p>
                                    Cliquez sur un marqueur sur la carte pour
                                    voir les détails du cas.
                                </p>
                                {casWithGeojson.length === 0 && (
                                    <p className="mt-2 text-sm">
                                        Aucun Dossier géolocalisé trouvé avec
                                        les filtres actuels.
                                    </p>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
