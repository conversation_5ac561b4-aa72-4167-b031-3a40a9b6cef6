(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9940],{7874:(e,s,o)=>{Promise.resolve().then(o.bind(o,8539))},8539:(e,s,o)=>{"use strict";o.r(s),o.d(s,{default:()=>m,dynamic:()=>c});var r=o(5155),n=o(2115),a=o(5695),l=o(98),i=o(3084),t=o(3109),d=o(7209);let c="force-dynamic";function m(){var e;let s=(0,a.useParams)().id,[o,c]=(0,n.useState)(null),[m,u]=(0,n.useState)(!0),[h,g]=(0,n.useState)(null),[x,j]=(0,n.useState)([36.75,3.06]);(0,n.useEffect)(()=>{p()},[s]);let p=async()=>{try{var e;u(!0);let o=await (0,l.Zq)("/api/cas/".concat(s));if(c(o),null==o||null==(e=o.geojson)?void 0:e.coordinates)if(console.log("Coordonn\xe9es GeoJSON trouv\xe9es:",o.geojson.coordinates),Array.isArray(o.geojson.coordinates)&&2===o.geojson.coordinates.length&&"number"==typeof o.geojson.coordinates[0]&&"number"==typeof o.geojson.coordinates[1]){let[e,s]=o.geojson.coordinates;console.log("Centre de la carte d\xe9fini \xe0:",[s,e]),j([s,e])}else console.warn("Coordonn\xe9es GeoJSON invalides, utilisation du centre par d\xe9faut")}catch(e){console.error("Erreur lors du chargement du cas:",e),g("Erreur lors du chargement des donn\xe9es du cas")}finally{u(!1)}};return m?(0,r.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,r.jsx)(i.LoadingSpinner,{})}):h?(0,r.jsx)(t.FormError,{message:h}):o?(0,r.jsxs)("div",{className:" mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:["Cartographie - ",o.nom]}),(0,r.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm border",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-gray-600",children:"Superficie:"}),(0,r.jsxs)("span",{className:"ml-2",children:[o.superficie," Ha"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-gray-600",children:"Probl\xe9matique:"}),(0,r.jsx)("span",{className:"ml-2",children:(null==(e=o.problematique)?void 0:e.problematique)||"Non d\xe9finie"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-gray-600",children:"Communes:"}),(0,r.jsx)("span",{className:"ml-2",children:o.communes.map(e=>e.nom).join(", ")})]})]}),o.observation&&(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t",children:[(0,r.jsx)("span",{className:"font-medium text-gray-600",children:"Observation:"}),(0,r.jsx)("p",{className:"mt-1 text-gray-700",children:o.observation})]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,r.jsx)(d.A,{casList:o?[o]:[],selectedCas:o,setSelectedCas:()=>{},center:x,zoom:13,height:"600px"})}),!o.geojson&&(0,r.jsx)("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-yellow-800",children:[(0,r.jsx)("strong",{children:"Aucune donn\xe9e g\xe9ographique disponible"})," ","pour ce cas. Les coordonn\xe9es ou le fichier GeoJSON n'ont pas \xe9t\xe9 fournis."]})})]}):(0,r.jsx)(t.FormError,{message:"Cas non trouv\xe9"})}}},e=>{var s=s=>e(e.s=s);e.O(0,[1761,9688,9359,5075,8441,1684,7358],()=>s(7874)),_N_E=e.O()}]);