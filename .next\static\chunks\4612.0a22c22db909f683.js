"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4612],{4612:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var n=t(5155),a=t(2115),o=t(4615),l=t(3145);let s=e=>{let{geojsonData:r,casId:s,onMapReady:i}=e,c=(0,a.useRef)(null),d=(0,a.useRef)(null),[p,u]=(0,a.useState)(!0),[m,g]=(0,a.useState)(null);return((0,a.useEffect)(()=>{let e=!0;return(async()=>{try{if(!c.current)return;if(d.current)try{d.current.remove(),d.current=null}catch(e){console.warn("Erreur lors du nettoyage de la carte:",e)}c.current&&(c.current.innerHTML="");let n=await t.e(1761).then(t.t.bind(t,5752,23));if(delete n.Icon.Default.prototype._getIconUrl,n.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"}),!e||!c.current)return;let a=(()=>{if(r&&r.coordinates)try{if("Polygon"===r.type&&r.coordinates[0]){let e=r.coordinates[0][0];return[e[1],e[0]]}}catch(e){console.warn("Error calculating center from GeoJSON:",e)}return[36.75,3.06]})(),p=r?12:8,m=n.map(c.current).setView(a,p);n.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}).addTo(m);let g=n.geoJSON(o,{style:{color:"#ff7800",weight:2,opacity:.8,fillOpacity:.1},onEachFeature:(e,r)=>{if(e.properties){let t='\n                                <div style="min-width: 200px;">\n                                    <h4 style="font-weight: bold; margin-bottom: 8px;">Wilaya '.concat(e.properties.id,'</h4>\n                                    <p style="margin: 4px 0;"><strong>Nom fran\xe7ais:</strong> ').concat(e.properties.name_fr,'</p>\n                                    <p style="margin: 4px 0;"><strong>Nom arabe:</strong> ').concat(e.properties.name_ar,"</p>\n                                </div>\n                            ");r.bindPopup(t)}}}),f=n.geoJSON(l,{style:{color:"#0066cc",weight:1,opacity:.7,fillOpacity:.05},onEachFeature:(e,r)=>{if(e.properties){var t;let n='\n                                <div style="min-width: 200px;">\n                                    <h4 style="font-weight: bold; margin-bottom: 8px;">Commune</h4>\n                                    <p style="margin: 4px 0;"><strong>Nom fran\xe7ais:</strong> '.concat(e.properties.name_fr,'</p>\n                                    <p style="margin: 4px 0;"><strong>Nom arabe:</strong> ').concat(e.properties.name_ar,'</p>\n                                    <p style="margin: 4px 0;"><strong>Superficie:</strong> ').concat(null==(t=e.properties.SHAPE_Area)?void 0:t.toFixed(2)," km\xb2</p>\n                                </div>\n                            ");r.bindPopup(n)}}});if(n.control.layers({},{Wilayas:g,Communes:f}).addTo(m),g.addTo(m),r){let e=n.geoJSON(r,{style:{color:"#3388ff",weight:3,opacity:.8,fillOpacity:.2}}).addTo(m);try{m.fitBounds(e.getBounds(),{padding:[10,10]})}catch(e){console.warn("Impossible d'ajuster les limites:",e)}}d.current=m,e&&(u(!1),console.log("Carte pr\xeate pour le cas:",s,"avec donn\xe9es:",!!r),i&&setTimeout(()=>i(),100))}catch(r){console.error("Erreur lors de l'initialisation de la carte:",r),e&&(g("Erreur lors du chargement de la carte"),u(!1))}})(),()=>{if(e=!1,d.current)try{d.current.remove(),d.current=null}catch(e){console.warn("Erreur lors du nettoyage de la carte:",e)}}},[s,r,i]),m)?(0,n.jsx)("div",{className:"w-full h-[300px] bg-red-50 rounded flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-red-500",children:m})}):p?(0,n.jsx)("div",{className:"w-full h-[300px] bg-gray-200 animate-pulse rounded flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-gray-500",children:"Chargement de la carte..."})}):r?(0,n.jsx)("div",{className:"w-full h-[300px] rounded overflow-hidden",children:(0,n.jsx)("div",{ref:c,className:"w-full h-full"},"leaflet--".concat(s))}):(0,n.jsx)("div",{className:"w-full h-[300px] bg-gray-50 rounded-lg flex items-center justify-center border border-gray-200",children:(0,n.jsxs)("div",{className:"text-center text-gray-500",children:[(0,n.jsx)("svg",{className:"w-12 h-12 mx-auto mb-3 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"})}),(0,n.jsx)("p",{className:"font-medium",children:"Aucune g\xe9om\xe9trie disponible"}),(0,n.jsx)("p",{className:"text-sm mt-1",children:"Ce cas n'a pas de donn\xe9es cartographiques"})]})})}}}]);