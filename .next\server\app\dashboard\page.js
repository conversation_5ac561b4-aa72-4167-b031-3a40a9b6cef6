(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14169:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),o=s(88170),n=s.n(o),l=s(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,64118)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,83249)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},15654:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(60687),a=s(47726);function o({children:e}){let{isCollapsed:t}=(0,a.c)();return(0,r.jsx)("div",{className:"space-y-0 pt-1 md:pt-1 transition-all duration-300 w-full",style:{minHeight:"100vh"},children:e})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27052:(e,t,s)=>{Promise.resolve().then(s.bind(s,54150))},27395:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>b});var r=s(60687),a=s(85814),o=s.n(a),n=s(16189),l=s(47726),i=s(20816),d=s(55510),c=s(10799),u=s(72871),m=s(45994),x=s(87061),p=s(37132),g=s(43655),h=s(66524),f=s(91028);function b({user:e}){let t=(0,n.usePathname)(),{isCollapsed:s,setIsCollapsed:a}=(0,l.c)(),b=[{name:"Tableau de bord",href:"/dashboard",icon:i.A},{name:"Gestion Dossiers",href:"/dashboard/cas",icon:d.A},{name:"Cartographie",href:"/dashboard/cartographie",icon:c.A},{name:"R\xe9glementation",href:"/dashboard/reglementation",icon:u.A},{name:"Statistiques",href:"/dashboard/statistiques",icon:m.A},...e?.role==="ADMIN"?[{name:"Utilisateurs",href:"/users",icon:x.A}]:[]];return e?(0,r.jsx)("div",{className:`h-[calc(100vh-2rem)] bg-gradient-to-b   from-sky-900 to-indigo-900/90 text-white flex flex-col transition-all duration-300 shadow-2xl rounded-r-3xl backdrop-blur-md border-r border-indigo-200/30 z-30 ${s?"w-16":"w-56"}`,children:(0,r.jsxs)("div",{className:"flex flex-col h-full ",children:[(0,r.jsx)("div",{className:"p-1 font-extrabold text-xl tracking-wide border-b border-indigo-700 flex items-center gap-2",children:!s&&(0,r.jsx)("span",{className:"bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent drop-shadow-lg",children:"Assainissement"})}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-indigo-800",children:[!s&&(0,r.jsx)("div",{className:"text-base font-semibold text-white",children:"Menu"}),(0,r.jsx)("button",{onClick:()=>a(!s),className:"p-2 rounded-md text-indigo-200 hover:bg-indigo-700 hover:text-white focus:outline-none",children:s?(0,r.jsx)(p.A,{className:"w-7 h-7"}):(0,r.jsx)(g.A,{className:"w-7 h-7"})})]}),(0,r.jsxs)("div",{className:"flex-grow p-0 overflow-y-auto",children:[(0,r.jsxs)("div",{className:`mb-4 pb-2 border-b border-indigo-800 ${s?"hidden":"block"}`,children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"h-12 w-12 rounded-full bg-gradient-to-r from-sky-400 to-indigo-500 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})}),!s&&(0,r.jsxs)("div",{className:"ml-2",children:[(0,r.jsx)("div",{className:"font-semibold text-white text-sm",children:e?.username||"Utilisateur"}),(0,r.jsxs)("div",{className:"text-[11px] text-indigo-200 flex items-center",children:[(0,r.jsx)("span",{className:`inline-block w-2 h-2 rounded-full mr-1.5 ${e?.role==="ADMIN"?"bg-red-400":e?.role==="EDITOR"?"bg-green-400":e?.role==="VIEWER"?"bg-gray-400":"bg-blue-400"}`}),e?.role||"BASIC",e?.role==="VIEWER"&&(0,r.jsx)(h.A,{className:"h-3 w-3 ml-1 text-orange-300",title:"Mode lecture seule"})]})]})]})}),s&&(0,r.jsx)("div",{className:"mt-2 flex flex-col items-center space-y-2",children:(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-gradient-to-r from-sky-40.0 to-indigo-500 flex items-center justify-center text-white font-bold text-sm shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})})})]}),(0,r.jsx)("nav",{children:(0,r.jsx)("ul",{className:"space-y-2",children:b.map(e=>{let a="/dashboard"===e.href?"/dashboard"===t:t===e.href||t.startsWith(`${e.href}/`),n=e.icon;return(0,r.jsx)("li",{children:(0,r.jsxs)(o(),{href:e.href,title:e.name,className:`flex items-center p-2 rounded-xl transition-colors duration-150 text-base font-medium gap-2 ${a?"bg-gradient-to-r from-sky-500 to-indigo-500 text-white shadow-md":"text-indigo-100 hover:bg-indigo-700 hover:text-white"} ${s?"justify-center":""}`,children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(n,{className:`w-7 h-7 ${!s?"mr-2":""} drop-shadow-lg`})}),!s&&(0,r.jsx)("div",{className:"flex items-center justify-between flex-1",children:(0,r.jsx)("span",{children:e.name})})]})},e.name)})})})]}),(0,r.jsx)("div",{className:`p-2 border-t border-indigo-800  ${s?"flex justify-center":""}`,children:(0,r.jsxs)("button",{onClick:()=>window.location.href="/api/auth/logout",title:"Se d\xe9connecter",className:`w-full flex items-center p-1 text-base rounded-xl transition-colors duration-150 ${s?"justify-center":""} text-indigo-100 hover:text-white hover:bg-indigo-700 font-semibold gap-2`,children:[(0,r.jsx)(f.A,{className:`w-6 h-6 ${!s?"mr-2":""}`}),!s&&"Se d\xe9connecter"]})})]})}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29228:(e,t,s)=>{Promise.resolve().then(s.bind(s,27395)),Promise.resolve().then(s.bind(s,47726)),Promise.resolve().then(s.bind(s,15654))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36224:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\DashboardStats.tsx","default")},42380:(e,t,s)=>{Promise.resolve().then(s.bind(s,97021)),Promise.resolve().then(s.bind(s,98440)),Promise.resolve().then(s.bind(s,58508))},47726:(e,t,s)=>{"use strict";s.d(t,{SidebarProvider:()=>l,c:()=>n});var r=s(60687),a=s(43210);let o=(0,a.createContext)({isCollapsed:!1,setIsCollapsed:e=>{}}),n=()=>(0,a.useContext)(o);function l({children:e}){let[t,s]=(0,a.useState)(!1);return(0,r.jsx)(o.Provider,{value:{isCollapsed:t,setIsCollapsed:s},children:e})}},54150:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),a=s(43210),o=s(69266),n=s(30845),l=s(73018);let i=(0,a.memo)(function(){let[e,t]=(0,a.useState)({total:0,regularises:0,enAttente:0}),[s,i]=(0,a.useState)({total:0,regularises:0,nonRegularises:0,ajournes:0,nonExamines:0,rejetes:0}),{encrages:d}=(0,l.W)({limit:100}),[c,u]=(0,a.useState)([]),[m,x]=(0,a.useState)(null),[p,g]=(0,a.useState)(!0),[h,f]=(0,a.useState)(null),[b,v]=(0,a.useState)(null);(0,a.useCallback)(()=>{if(e.total>0&&c.length>0){let t=c.reduce((e,t)=>e+t.totalCas,0),r=c.reduce((e,t)=>e+t.casRegularises,0);console.log("\uD83D\uDCCA Stats Validation:"),console.log(`   Global total: ${e.total}`),console.log(`   Encrages total: ${t}`),console.log(`   Global regularises: ${e.regularises}`),console.log(`   Encrages regularises: ${r}`),console.log(`   Resolution total: ${s.total}`),e.total!==t&&console.warn("⚠️ Inconsistency: Global total ≠ Encrages total"),e.regularises!==r&&console.warn("⚠️ Inconsistency: Global regularises ≠ Encrages regularises"),e.total!==s.total&&console.warn("⚠️ Inconsistency: Global total ≠ Resolution total")}},[e,c,s]);let j=async()=>{try{console.log("\uD83D\uDD04 Rechargement des statistiques des cas...");let e=m?.role==="ADMIN"?void 0:m?.wilayaId?.toString(),s=await (0,o.Zq)(`/api/stats/cas-optimized${e?`?wilayaId=${e}`:""}`);console.log("\uD83D\uDCCA Nouvelles statistiques des cas:",s),t(s||{total:0,regularises:0,enAttente:0})}catch(e){console.error("Erreur lors du chargement des statistiques des cas:",e),f("Erreur lors du chargement des statistiques")}},w=async()=>{try{let e=m?.role==="ADMIN"||m?.role==="VIEWER"?void 0:m?.wilayaId?.toString(),t=e?`/api/stats/resolution?wilayaId=${e}`:"/api/stats/resolution",s=await (0,o.Zq)(t);i(s||{total:0,regularises:0,nonRegularises:0,ajournes:0,nonExamines:0,rejetes:0})}catch(e){console.error("Erreur lors du chargement des statistiques de r\xe9solution:",e)}},y=async()=>{try{let e=m?.role==="ADMIN"?void 0:m?.wilayaId?.toString(),t=await (0,o.Zq)(`/api/stats/encrages${e?`?wilayaId=${e}`:""}`);console.log("\uD83D\uDCCA Encrage stats loaded:",t),u(t||[])}catch(e){console.error("Erreur lors du chargement des statistiques des encrages:",e),f("Erreur lors du chargement des statistiques")}},N=async()=>{try{let e=await (0,o.Zq)("/api/auth/me");x(e)}catch(e){console.error("Erreur lors du chargement de l'utilisateur:",e)}},C=async()=>{g(!0),f(null);let e=performance.now();console.log("\uD83D\uDCCA D\xe9but chargement dashboard stats...");try{await N();let t=(e,t)=>Promise.race([e(),new Promise((e,s)=>setTimeout(()=>s(Error(`Timeout ${t}`)),12e4))]);await Promise.all([t(j,"cas stats"),t(y,"encrage stats"),t(w,"resolution stats")]);let s=performance.now(),r=Math.round(s-e);console.log(`✅ Dashboard stats charg\xe9 en ${r}ms`),r>1e4&&console.warn(`⚠️ Chargement lent d\xe9tect\xe9 (${r}ms). Consid\xe9rez l'optimisation de la base de donn\xe9es.`)}catch(e){console.error("Erreur lors du chargement des donn\xe9es:",e),e.message?.includes("Timeout")?f("Chargement trop long. La base de donn\xe9es contient beaucoup de donn\xe9es. Veuillez patienter ou rafra\xeechir la page."):413===e.status?f("Volume de donn\xe9es trop important. Contactez l'administrateur pour optimiser les performances."):503===e.status?f("Service temporairement indisponible. Veuillez r\xe9essayer dans quelques instants."):f("Erreur lors du chargement des donn\xe9es. Veuillez r\xe9essayer.")}finally{g(!1)}};(0,n.kF)("dashboard-cas-stats",j,[m]),(0,n.kF)("dashboard-encrage-stats",y,[m]),(0,n.kF)("dashboard-resolution-stats",w,[m]);let S=(0,a.useMemo)(()=>e.total>0?(e.regularises/e.total*100).toFixed(1):"0",[e.regularises,e.total]),P=(0,a.useMemo)(()=>e.total>0?(e.enAttente/e.total*100).toFixed(1):"0",[e.enAttente,e.total]),A=(0,a.useMemo)(()=>d.map(e=>{let t=c.find(t=>t.id===e.id)||{id:e.id,nom:e.nom,totalCas:0,casRegularises:0},s=t.totalCas>0?t.casRegularises/t.totalCas*100:0,r="bg-green-500";return 0===s?r="bg-red-500":s<50?r="bg-orange-400":s<100&&(r="bg-yellow-400"),{...e,stat:t,percentage:s,progressBarColorClass:r}}),[d,c]);return p?(0,r.jsx)("div",{className:"space-y-8 px-1 sm:px-2 lg:px-4 pt-2 md:pt-4",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 lg:gap-6 mb-6",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"bg-gray-200 rounded-2xl p-4 lg:p-6 h-20 lg:h-24"},e))}),(0,r.jsx)("div",{className:"w-full h-5 bg-gray-200 rounded-full mb-8"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-gray-200 rounded-2xl h-40 lg:h-48"},e))})]})}):h?(0,r.jsxs)("div",{className:"text-center py-10",children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-red-600 mb-4",children:"Erreur de chargement des donn\xe9es"}),(0,r.jsx)("p",{className:"text-slate-600 mb-4",children:h}),(0,r.jsx)("button",{onClick:C,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"R\xe9essayer"})]}):(0,r.jsxs)("div",{className:"space-y-6 lg:space-y-8 px-1 sm:px-2 lg:px-4 pt-2 md:pt-4",children:[(0,r.jsxs)("section",{className:"mb-6 lg:mb-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-4 lg:mb-6 font-inter text-[1.05rem]",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-sky-100 to-sky-50 rounded-2xl p-4 lg:p-6 shadow flex flex-col items-center border border-sky-100",children:[(0,r.jsx)("span",{className:"text-2xl lg:text-3xl font-extrabold text-sky-700 drop-shadow font-inter",children:e.total}),(0,r.jsx)("span",{className:"text-sm lg:text-base text-sky-800 mt-2 font-medium font-inter",children:"Total dossiers"})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-green-100 to-green-50 rounded-2xl p-4 lg:p-6 shadow flex flex-col items-center border border-green-100",children:[(0,r.jsx)("span",{className:"text-2xl lg:text-3xl font-extrabold text-green-700 drop-shadow font-inter",children:e.regularises}),(0,r.jsx)("span",{className:"text-sm lg:text-base text-green-800 mt-2 font-medium font-inter",children:"r\xe9gularis\xe9s"})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-orange-100 to-orange-50 rounded-2xl p-4 lg:p-6 shadow flex flex-col items-center border border-orange-100",children:[(0,r.jsx)("span",{className:"text-2xl lg:text-3xl font-extrabold text-orange-700 drop-shadow font-inter",children:s.ajournes}),(0,r.jsx)("span",{className:"text-sm lg:text-base text-orange-800 mt-2 font-medium font-inter",children:"Ajourn\xe9s"})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-gray-100 to-gray-50 rounded-2xl p-4 lg:p-6 shadow flex flex-col items-center border border-gray-100",children:[(0,r.jsx)("span",{className:"text-2xl lg:text-3xl font-extrabold text-gray-700 drop-shadow font-inter",children:s.nonExamines}),(0,r.jsx)("span",{className:"text-sm lg:text-base text-gray-800 mt-2 font-medium font-inter",children:"Non examin\xe9s"})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-red-100 to-red-50 rounded-2xl p-4 lg:p-6 shadow flex flex-col items-center border border-red-100",children:[(0,r.jsx)("span",{className:"text-2xl lg:text-3xl font-extrabold text-red-700 drop-shadow font-inter",children:s.rejetes}),(0,r.jsx)("span",{className:"text-sm lg:text-base text-red-800 mt-2 font-medium font-inter",children:"Rejet\xe9s"})]})]}),(0,r.jsxs)("div",{className:"w-full h-5 bg-gray-200 rounded-full overflow-hidden flex relative group shadow mb-2",children:[(0,r.jsx)("div",{className:"h-5 bg-gradient-to-r from-green-400 to-green-600 transition-all duration-500 group-hover:opacity-90",style:{width:`${e.total>0?e.regularises/e.total*100:0}%`},title:`R\xe9gularis\xe9s: ${S}%`}),(0,r.jsx)("div",{className:"h-5 bg-gradient-to-r from-orange-400 to-red-500 transition-all duration-500 group-hover:opacity-90",style:{width:`${e.total>0?e.enAttente/e.total*100:0}%`},title:`En attente: ${P}%`}),e.total>0&&(0,r.jsxs)("span",{className:"absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 bg-white/80 rounded px-3 py-1 text-xs text-indigo-700 shadow font-bold",children:[S,"% r\xe9gularis\xe9s"]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-slate-500 mt-1",children:[(0,r.jsx)("span",{children:"0%"}),(0,r.jsx)("span",{children:"100%"})]})]}),(0,r.jsx)("section",{children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6 text-[0.97rem] font-inter",children:A.map(e=>{let{stat:t,percentage:s,progressBarColorClass:a}=e;return(0,r.jsxs)("div",{className:"group bg-white/80 backdrop-blur-lg rounded-2xl shadow-xl hover:shadow-2xl transition-shadow border border-transparent hover:border-sky-400 min-h-[200px] lg:min-h-[220px] flex flex-col p-4 lg:p-6 relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2 flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-base md:text-lg font-bold text-indigo-700 drop-shadow font-inter line-clamp-3",title:e.nom,children:e.nom}),(0,r.jsxs)("span",{className:"text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded-full font-semibold",children:[t.totalCas," dossiers"]})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col justify-end",children:[(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 mb-2",children:(0,r.jsx)("div",{className:`h-2.5 rounded-full transition-all duration-500 ${a}`,style:{width:`${s}%`}})}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-slate-500",children:[(0,r.jsxs)("span",{children:[t.casRegularises," ","r\xe9gularis\xe9s"]}),(0,r.jsxs)("span",{children:[t.totalCas-t.casRegularises," ","en attente"]})]})]})]}),(0,r.jsx)("div",{className:"mt-4 pt-2 border-t border-slate-100",children:(0,r.jsxs)("a",{href:`/cas?encrageId=${e.id}`,className:"text-xs font-semibold text-sky-600 group-hover:text-sky-500 flex items-center gap-1 transition-colors",children:["Voir les dossiers",(0,r.jsx)("svg",{className:"w-4 h-4 ml-1 group-hover:translate-x-0.5 transition-transform",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})})]},e.id)})})})]})})},55511:e=>{"use strict";e.exports=require("crypto")},58508:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\MainContentClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\MainContentClient.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63500:(e,t,s)=>{Promise.resolve().then(s.bind(s,36224))},64118:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(37413),a=s(36224);function o(){return(0,r.jsx)(a.default,{})}},69266:(e,t,s)=>{"use strict";async function r(e,t={}){let{method:s="GET",body:a}=t;console.log(`Making ${s} request to ${e}`),a&&console.log("Request body:",a);let o=await fetch(e,{method:s,headers:{"Content-Type":"application/json",...t.headers},credentials:t.credentials||"include",body:a?JSON.stringify(a):void 0});if(console.log("Response status:",o.status),!o.ok){let e=`HTTP error! status: ${o.status}`,t=null;try{let s=await o.text();if(console.log("Error response text:",s),s)try{t=JSON.parse(s),e=t?.error||t?.message||s}catch(t){e=s||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===o.status)throw Error("Authentication required. Please log in again.");if(403===o.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===o.status)throw Error("Resource not found.");else if(o.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===o.status)return null;let n=await o.json();return console.log("Response data:",n),n}s.d(t,{Zq:()=>r,uE:()=>a});let a={get:(e,t)=>r(e,{...t,method:"GET"}),post:(e,t,s)=>r(e,{...s,method:"POST",body:t}),put:(e,t,s)=>r(e,{...s,method:"PUT",body:t}),patch:(e,t,s)=>r(e,{...s,method:"PATCH",body:t}),delete:(e,t,s)=>r(e,{...s,method:"DELETE",body:t})}},73018:(e,t,s)=>{"use strict";s.d(t,{W:()=>o});var r=s(43210),a=s(69266);function o(e={}){let{limit:t=50,includeStats:s=!1,search:n="",includeProblematiques:l=!1,sortBy:i="nom",sortDirection:d="asc",dateFrom:c="",dateTo:u="",status:m=""}=e,[x,p]=(0,r.useState)([]),[g,h]=(0,r.useState)([]),[f,b]=(0,r.useState)(!1),[v,j]=(0,r.useState)(null),[w,y]=(0,r.useState)(1),[N,C]=(0,r.useState)(1),[S,P]=(0,r.useState)(0),A=`encrages-${t}-${s}-${n}-${l}-${w}-${i}-${d}-${c}-${u}-${m}`,k=(0,r.useCallback)(async()=>{let e=sessionStorage.getItem(A);if(e)try{let t=JSON.parse(e),r=t.timestamp;if(Date.now()-r<3e5){p(t.data),s&&h(t.stats||[]),C(t.pagination?.totalPages||1),P(t.pagination?.total||0);return}}catch(e){console.error("Erreur lors de la lecture du cache:",e)}b(!0),j(null);try{let e=new URLSearchParams;e.append("page",w.toString()),e.append("limit",t.toString()),e.append("includeStats",s.toString()),e.append("includeProblematiques",l.toString()),n&&e.append("search",n),i&&e.append("sortBy",i),d&&e.append("sortDirection",d),c&&e.append("dateFrom",c),u&&e.append("dateTo",u),m&&e.append("status",m);let r=`/api/encrages?${e.toString()}`,o=await a.uE.get(r);if(p(o.data),C(o.pagination.totalPages),P(o.pagination.total),s){let e=o.data.filter(e=>e.casStats).map(e=>({id:e.id,nom:e.nom,totalCas:e.casStats.total,casRegularises:e.casStats.regularises}));h(e),sessionStorage.setItem(A,JSON.stringify({data:o.data,stats:e,pagination:o.pagination,timestamp:Date.now()}))}else sessionStorage.setItem(A,JSON.stringify({data:o.data,pagination:o.pagination,timestamp:Date.now()}))}catch(e){j(e.message||"Erreur lors du chargement des encrages"),console.error("Erreur lors du chargement des encrages:",e)}finally{b(!1)}},[A,l,s,t,w,n]);return{encrages:x,encrageStats:g,isLoading:f,error:v,page:w,totalPages:N,totalItems:S,goToPage:(0,r.useCallback)(e=>{y(e)},[]),refresh:k}}},79428:e=>{"use strict";e.exports=require("buffer")},83249:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(37413),a=s(41098),o=s(39916),n=s(98440),l=s(97021),i=s(58508);async function d({children:e}){let t=await (0,a.wz)();return t||(0,o.redirect)("/login"),(0,r.jsx)(n.SidebarProvider,{children:(0,r.jsxs)("div",{className:"flex min-h-screen bg-gradient-to-br  from-red-50 via-indigo-50 to-purple-100 font-inter w-full",children:[(0,r.jsx)("aside",{className:"transition-all duration-300 shadow-2xl  ",children:(0,r.jsx)(l.Sidebar,{user:t})}),(0,r.jsx)("main",{className:"flex-1 min-w-0 px-1 md:px-1 py-1 md:py-1",children:(0,r.jsx)("div",{className:"bg-white rounded-3xl shadow-2xl p-1 md:p-1 border border-gray-100 min-h-[80vh]",children:(0,r.jsx)(i.default,{children:e})})})]})})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},97021:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\Sidebar.tsx","Sidebar")},98440:(e,t,s)=>{"use strict";s.d(t,{SidebarProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","useSidebar");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","SidebarProvider")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,3903,5262,2082,2797],()=>s(14169));module.exports=r})();