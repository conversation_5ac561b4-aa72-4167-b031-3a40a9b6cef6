(()=>{var e={};e.id=5009,e.ids=[5009],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32407:(e,s,r)=>{Promise.resolve().then(r.bind(r,73216))},33873:e=>{"use strict";e.exports=require("path")},46221:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let d={children:["",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57326)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\users\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/users/page",pathname:"/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},57326:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\users\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72159:(e,s,r)=>{Promise.resolve().then(r.bind(r,57326))},73216:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(60687),a=r(43210),i=r(9171),l=r(52643),n=r(99235),o=r(78712),d=r(89679),c=r(69266),u=r(16189);function m(){(0,u.useRouter)();let[e,s]=(0,a.useState)([]),[r,m]=(0,a.useState)(!1),[x,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)(null),[v,f]=(0,a.useState)(null),[j,b]=(0,a.useState)({email:"",username:"",password:"",role:"BASIC",wilayaId:null}),[y,w]=(0,a.useState)(""),[N,I]=(0,a.useState)(!1);async function A(){try{I(!0);let e=await (0,c.Zq)("/api/users");s(e||[])}catch(e){console.error("Error fetching users:",e),e instanceof Error&&e.message.includes("Authentication")?window.location.href="/login":w("Erreur lors du chargement des utilisateurs")}finally{I(!1)}}async function k(e){e.preventDefault(),w(""),I(!0),console.log("Submitting username:",j.username);try{if(x&&h){let e={...j},s=e.password?e:{email:e.email,username:e.username,role:e.role,wilayaId:e.wilayaId};console.log("Data to send (edit):",s),await (0,c.Zq)(`/api/users/${h.id}`,{method:"PUT",body:s})}else{if(!j.password){w("Le mot de passe est requis"),I(!1);return}console.log("Data to send (add):",j),await (0,c.Zq)("/api/users",{method:"POST",body:j})}m(!1),A()}catch(e){w(e.message)}finally{I(!1)}}async function C(e){if(confirm("\xcates-vous s\xfbr de vouloir supprimer cet utilisateur ?"))try{await (0,c.Zq)(`/api/users/${e.id}`,{method:"DELETE"}),A()}catch(e){w(e.message)}}let P=[{header:"Nom d'utilisateur",accessorKey:"username"},{header:"Email",accessorKey:"email"},{header:"R\xf4le",accessorKey:"role",cell:e=>{let s=e.role,r="";switch(s){case"ADMIN":r="bg-red-100 text-red-800 border-red-200";break;case"EDITOR":r="bg-green-100 text-green-800 border-green-200";break;case"VIEWER":r="bg-gray-100 text-gray-800 border-gray-200";break;default:r="bg-blue-100 text-blue-800 border-blue-200"}return(0,t.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r} border`,children:s})}},{header:"Cas",accessorKey:"id",cell:e=>e._count?.cas||0},{header:"Date de cr\xe9ation",accessorKey:"createdAt",cell:e=>new Date(e.createdAt).toLocaleDateString()}];return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("div",{className:" mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Gestion des Utilisateurs"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"G\xe9rez les comptes utilisateurs et leurs permissions d'acc\xe8s au syst\xe8me."})]}),(0,t.jsxs)(i.$,{onClick:function(){g(null),b({email:"",username:"",password:"",role:"BASIC",wilayaId:null}),console.log("Adding new user, username initialized as empty string"),p(!1),m(!0)},className:"flex-shrink-0",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5 mr-2",children:(0,t.jsx)("path",{d:"M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z"})}),"Nouvel utilisateur"]})]})}),y&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,t.jsx)(d.j,{message:y})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total utilisateurs"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Administrateurs"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"ADMIN"===e.role).length})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"\xc9diteurs"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"EDITOR"===e.role).length})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white shadow-xl rounded-xl overflow-hidden border border-gray-200",children:[(0,t.jsx)("div",{className:"px-6 py-5 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl leading-7 font-semibold text-gray-800",children:"Liste des Utilisateurs"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Visualisez, modifiez ou supprimez les utilisateurs existants."})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[e.length," utilisateur",e.length>1?"s":""," au total"]})]})}),(0,t.jsx)(n.X,{data:e,columns:P,pageSize:25,pageSizeOptions:[10,25,50,100],showPaginationInfo:!0,isLoading:N,actions:e=>(0,t.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-2",children:[(0,t.jsxs)(i.$,{variant:"secondary",size:"sm",onClick:()=>{g(e),b({email:e.email,username:e.username,password:"",role:e.role,wilayaId:e.wilayaId||null}),console.log("Editing user, username set to:",e.username),p(!0),m(!0)},className:"flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-4 h-4 mr-1.5",children:(0,t.jsx)("path",{d:"M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z"})}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Modifier"})]}),(0,t.jsxs)(i.$,{variant:"destructive",size:"sm",onClick:()=>C(e),disabled:e.id===v?.id,className:"flex items-center",title:e.id===v?.id?"Vous ne pouvez pas supprimer votre propre compte":"Supprimer cet utilisateur",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-4 h-4 sm:mr-1.5",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.58.177-2.34.296a.75.75 0 00-.707.707A48.69 48.69 0 002 6.499V16c0 .69.56 1.25 1.25 1.25H16.75c.69 0 1.25-.56 1.25-1.25V6.5c0-.434-.025-.864-.073-1.282a.75.75 0 00-.707-.707 48.689 48.689 0 00-2.34-.296V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.538-.054 2.208-.152l.002-.003L15 3.5l.003.002A2.5 2.5 0 0115 3.75V5H5V3.75a2.5 2.5 0 012.289-2.498L7.292 3.5l.002.003L7.5 3.5A2.5 2.5 0 017.75 3.5V4h2.25zM5 6.58V16h10V6.579A47.187 47.187 0 0110 6.5c-1.944 0-3.803.146-5.524.418l-.001.003L5 6.58z",clipRule:"evenodd"})}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Supprimer"})]})]})})]})]})}),(0,t.jsx)(o.a,{isOpen:r,onClose:()=>m(!1),title:x?"Modifier l'utilisateur":"Ajouter un utilisateur",children:(0,t.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(l.p,{id:"email",label:"Email",type:"email",value:j.email,onChange:e=>b(s=>({...s,email:e.target.value})),required:!0}),(0,t.jsx)(l.p,{id:"username",label:"Nom d'utilisateur",value:j.username,onChange:e=>{let s=e.target.value;console.log("Username input changed to:",s),b(e=>({...e,username:s}))},required:!0}),(0,t.jsx)(l.p,{id:"password",label:x?"Mot de passe (laisser vide pour ne pas changer)":"Mot de passe",type:"password",value:j.password,onChange:e=>b(s=>({...s,password:e.target.value})),required:!x}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-foreground",children:"R\xf4le"}),(0,t.jsxs)("select",{id:"role",value:j.role,onChange:e=>b(s=>({...s,role:e.target.value})),required:!0,className:"mt-1 block w-full rounded-md border-input bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm",children:[(0,t.jsx)("option",{value:"BASIC",children:"Utilisateur (BASIC)"}),(0,t.jsx)("option",{value:"EDITOR",children:"\xc9diteur (EDITOR)"}),(0,t.jsx)("option",{value:"VIEWER",children:"Lecteur (VIEWER)"}),(0,t.jsx)("option",{value:"ADMIN",children:"Administrateur (ADMIN)"})]})]}),(0,t.jsx)(l.p,{id:"wilayaId",label:"ID Wilaya (optionnel)",type:"number",value:null!==j.wilayaId?j.wilayaId:"",onChange:e=>b(s=>({...s,wilayaId:e.target.value?parseInt(e.target.value):null}))})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200",children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>m(!1),type:"button",children:"Annuler"}),(0,t.jsx)(i.$,{type:"submit",isLoading:N,children:x?"Enregistrer ":"Ajouter l'utilisateur"})]})]})})]})}},79428:e=>{"use strict";e.exports=require("buffer")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[7719,3903,5262,2348,2797,2049],()=>r(46221));module.exports=t})();