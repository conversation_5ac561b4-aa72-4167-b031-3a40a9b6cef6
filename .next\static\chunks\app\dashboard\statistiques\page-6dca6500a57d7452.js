(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3885],{98:(e,t,a)=>{"use strict";async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:a="GET",body:s}=t;console.log("Making ".concat(a," request to ").concat(e)),s&&console.log("Request body:",s);let r=await fetch(e,{method:a,headers:{"Content-Type":"application/json",...t.headers},credentials:t.credentials||"include",body:s?JSON.stringify(s):void 0});if(console.log("Response status:",r.status),!r.ok){let e="HTTP error! status: ".concat(r.status),t=null;try{let a=await r.text();if(console.log("Error response text:",a),a)try{e=(null==(t=JSON.parse(a))?void 0:t.error)||(null==t?void 0:t.message)||a}catch(t){e=a||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===r.status)throw Error("Authentication required. Please log in again.");if(403===r.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===r.status)throw Error("Resource not found.");else if(r.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===r.status)return null;let l=await r.json();return console.log("Response data:",l),l}a.d(t,{Zq:()=>s,uE:()=>r});let r={get:(e,t)=>s(e,{...t,method:"GET"}),post:(e,t,a)=>s(e,{...a,method:"POST",body:t}),put:(e,t,a)=>s(e,{...a,method:"PUT",body:t}),patch:(e,t,a)=>s(e,{...a,method:"PATCH",body:t}),delete:(e,t,a)=>s(e,{...a,method:"DELETE",body:t})}},408:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(5155),r=a(2115),l=a(98),i=a(4065),n=a(2502),o=a(3084),d=a(9662),c=a(8682),x=a(8861);let u={1:"Adrar",2:"Chlef",3:"Laghouat",4:"Oum El Bouaghi",5:"Batna",6:"B\xe9ja\xefa",7:"Biskra",8:"B\xe9char",9:"Blida",10:"Bouira",11:"Tamanrasset",12:"T\xe9bessa",13:"Tlemcen",14:"Tiaret",15:"Tizi Ouzou",16:"Alger",17:"Djelfa",18:"Jijel",19:"S\xe9tif",20:"Sa\xefda",21:"Skikda",22:"Sidi Bel Abb\xe8s",23:"Annaba",24:"Guelma",25:"Constantine",26:"M\xe9d\xe9a",27:"Mostaganem",28:"M'Sila",29:"Mascara",30:"Ouargla",31:"Oran",32:"El Bayadh",33:"Illizi",34:"Bordj Bou Arr\xe9ridj",35:"Boumerd\xe8s",36:"El Tarf",37:"Tindouf",38:"Tissemsilt",39:"El Oued",40:"Khenchela",41:"Souk Ahras",42:"Tipaza",43:"Mila",44:"A\xefn Defla",45:"Na\xe2ma",46:"A\xefn T\xe9mouchent",47:"Gharda\xefa",48:"Relizane",49:"Timimoun",50:"Bordj Badji Mokhtar",51:"Ouled Djellal",52:"B\xe9ni Abb\xe8s",53:"In Salah",54:"In Guezzam",55:"Touggourt",56:"Djanet",57:"El M'Ghair",58:"El Meniaa"};function m(){let{user:e,isAdmin:t}=(0,c.Sk)(),[a,n]=(0,r.useState)(null),[m,p]=(0,r.useState)(!0),[h,g]=(0,r.useState)(null),[y,b]=(0,r.useState)(""),[j,N]=(0,r.useState)("statuts"),f=t||!(null==e?void 0:e.wilayaId),w=(null==e?void 0:e.wilayaId)&&!t?e.wilayaId.toString():"",v=async()=>{try{p(!0),g(null);let e=y?"/api/stats/analyse-complete?wilayaId=".concat(y):"/api/stats/analyse-complete";console.log("\uD83D\uDCCA Chargement de l'analyse depuis:",e);let t=await (0,l.Zq)(e);t.success&&t.data?(n(t.data),console.log("✅ Analyse charg\xe9e:",t.data)):g(t.error||"Erreur lors du chargement de l'analyse")}catch(e){console.error("Erreur lors du chargement de l'analyse:",e),g(e.message||"Erreur inconnue")}finally{p(!1)}};return((0,r.useEffect)(()=>{v()},[y]),(0,r.useEffect)(()=>{e&&b(w)},[e,w]),(0,x.kF)("statistiques-analyse-complete",v,[y]),m)?(0,s.jsx)("div",{className:"  mx-auto px-4 py-8",children:(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)(o.LoadingSpinner,{})})}):h?(0,s.jsx)("div",{className:"    mx-auto px-4 py-8",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-red-600 mb-2",children:"Erreur"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:h}),(0,s.jsx)("button",{onClick:v,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"R\xe9essayer"})]})})}):a?(0,s.jsxs)("div",{className:"  mx-auto px-4 py-4",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Analyse Compl\xe8te des Dossiers et Contraintes"}),(0,s.jsx)(d.tK,{className:"mt-2"})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[f&&(0,s.jsxs)("select",{value:y,onChange:e=>b(e.target.value),disabled:m,className:"border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-48 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("option",{value:"",children:"Toutes les DSA"}),Array.from({length:58},(e,t)=>{let a=t+1;return(0,s.jsx)("option",{value:a.toString(),children:u[a]||"DSA ".concat(a)},a)})]}),(0,s.jsxs)("button",{onClick:v,disabled:m,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",children:[m&&(0,s.jsx)(o.LoadingSpinner,{size:"sm"}),"Actualiser"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4",children:[(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"\uD83D\uDD35 Total dossiers"}),(0,s.jsx)("p",{className:" text-2xl font-bold text-blue-900",children:a.totalCas.toLocaleString()})]}),(0,s.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"\uD83D\uDFE2 R\xe9gularis\xe9s"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-900",children:a.tableauStatuts.reduce((e,t)=>e+t.wilayas.reduce((e,t)=>e+t.regularise,0),0).toLocaleString()})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"\uD83D\uDFE1 Ajourn\xe9s"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-900",children:a.tableauStatuts.reduce((e,t)=>e+t.wilayas.reduce((e,t)=>e+t.ajourne,0),0).toLocaleString()})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-800",children:"⚪ Non examin\xe9s"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.tableauStatuts.reduce((e,t)=>e+t.wilayas.reduce((e,t)=>e+t.nonExamine,0),0).toLocaleString()})]}),(0,s.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"\uD83D\uDD34 Rejet\xe9s"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-900",children:a.tableauStatuts.reduce((e,t)=>e+t.wilayas.reduce((e,t)=>e+t.rejete,0),0).toLocaleString()})]})]})]}),(0,s.jsx)("div",{className:"mb-3",children:(0,s.jsx)("div",{className:"border-b border-gray-200",children:(0,s.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,s.jsx)("button",{onClick:()=>N("statuts"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("statuts"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"\uD83D\uDCCA Analyse des Dossiers par DSA"}),(0,s.jsx)("button",{onClick:()=>N("contraintes"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("contraintes"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"\uD83D\uDD0D Analyse des Contraintes par Secteur"}),(0,s.jsx)("button",{onClick:()=>N("charts"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("charts"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"\uD83D\uDCC8 Graphiques Dynamiques"})]})})}),"statuts"===j&&(0,s.jsx)("div",{className:"space-y-8",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200",children:(0,s.jsx)("div",{className:"px-6 py-2",children:(0,s.jsx)("div",{className:"overflow-x-auto xl:overflow-visible",children:(0,s.jsxs)("table",{className:"min-w-full xl:min-w-0 divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"DSA"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total dossiers"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"R\xe9gularis\xe9"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Ajourn\xe9"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rejet\xe9"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Non examin\xe9"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Taux R\xe9gularisation"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:(()=>{let e=new Map;return a.tableauStatuts.forEach(t=>{t.wilayas.forEach(t=>{e.has(t.wilayaId)||e.set(t.wilayaId,{dsaName:u[t.wilayaId]||"DSA ".concat(t.wilayaId),total:0,regularise:0,ajourne:0,rejete:0,nonExamine:0});let a=e.get(t.wilayaId);a.total+=t.total,a.regularise+=t.regularise,a.ajourne+=t.ajourne,a.rejete+=t.rejete,a.nonExamine+=t.nonExamine})}),Array.from(e.entries()).sort((e,t)=>{let[a]=e,[s]=t;return a-s}).map(e=>{let[t,a]=e;return(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:a.dsaName}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold",children:a.total.toLocaleString()}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium",children:a.regularise.toLocaleString()}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-yellow-600",children:a.ajourne.toLocaleString()}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-red-600",children:a.rejete.toLocaleString()}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-600",children:a.nonExamine.toLocaleString()}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium",children:a.total>0?"".concat(Math.round(a.regularise/a.total*100),"%"):"0%"})]},t)})})()})]})})})})}),"contraintes"===j&&(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Analyse des Contraintes par Structure Administrative"}),a.tableauContraintes.map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:u[e.wilayaId]||"DSA ".concat(e.wilayaId)})}),(0,s.jsx)("div",{className:"px-6 py-4",children:0===e.encrages.length?(0,s.jsx)("p",{className:"text-gray-500 italic",children:"Aucune contrainte identifi\xe9e pour cette DSA"}):(0,s.jsx)("div",{className:"space-y-6",children:e.encrages.map((e,t)=>(0,s.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,s.jsx)("div",{className:"mb-4",children:"Secteur non d\xe9fini"===e.secteur?(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-md font-semibold text-yellow-800",children:"\uD83D\uDCCB Dossiers sans contrainte"}),(0,s.jsxs)("p",{className:"text-sm text-yellow-700",children:["Total de dossiersss sans contrainte:"," ",(0,s.jsx)("span",{className:"font-bold text-yellow-900",children:e.totalCas})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("h4",{className:"text-md font-semibold text-gray-800",children:["\uD83D\uDCCB"," ",e.secteur]}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Structure Administrative:"," ",(0,s.jsx)("span",{className:"font-medium",children:e.secteur})," ","| Total contraintes:"," ",(0,s.jsx)("span",{className:"font-medium text-blue-600",children:e.totalCas})]})]})}),(0,s.jsx)("div",{className:"overflow-x-auto xl:overflow-visible",children:(0,s.jsxs)("table",{className:"min-w-full xl:min-w-0 divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Probl\xe9matique"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Contraintes"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"R\xe9gularis\xe9"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Ajourn\xe9"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rejet\xe9"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Non examin\xe9"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Taux R\xe9gularisation"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.problematiques.map((t,a)=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50 ".concat("Secteur non d\xe9fini"===e.secteur?"bg-yellow-50":""),children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:t.problematiqueName}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm font-semibold text-gray-900",children:t.count}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-green-600 font-medium",children:t.statuts.regularise}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-yellow-600",children:t.statuts.ajourne}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-red-600",children:t.statuts.rejete}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:t.statuts.nonExamine}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-blue-600 font-medium",children:t.count>0?"".concat(Math.round(t.statuts.regularise/t.count*100),"%"):"0%"})]},a))})]})})]},t))})})]},e.wilayaId))]}),"charts"===j&&(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Graphiques Dynamiques"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-1",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"R\xe9partition par Statut"})}),(0,s.jsx)("div",{className:"px-6 py-4",children:(0,s.jsx)("div",{style:{height:"500px"},className:"xl:h-[600px] 2xl:h-[700px]",children:(0,s.jsx)(i.nu,{data:a.chartStatuts,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}},title:{display:!0,text:"Distribution des dossiers par statut",font:{size:16}},tooltip:{callbacks:{label:function(e){let t=e.dataset.data.reduce((e,t)=>e+t,0),a=(e.parsed/t*100).toFixed(1);return"".concat(e.label,": ").concat(e.parsed.toLocaleString()," (").concat(a,"%)")}}}}}})})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-2",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Nombre de dossiers par DSA"})}),(0,s.jsx)("div",{className:"px-6 py-4",children:(0,s.jsx)("div",{style:{height:"500px"},className:"xl:h-[600px] 2xl:h-[700px]",children:(0,s.jsx)(i.yP,{data:a.chartWilayas,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:15}},title:{display:!0,text:"R\xe9partition des dossiers par DSA et statut",font:{size:16}},tooltip:{mode:"index",intersect:!1,callbacks:{footer:function(e){let t=0;return e.forEach(function(e){t+=e.parsed.y}),"Total: ".concat(t.toLocaleString())}}}},scales:{x:{stacked:!0,title:{display:!0,text:"DSA (Tri\xe9es par nombre total de dossiers)",font:{size:14}},ticks:{maxRotation:45,minRotation:45}},y:{stacked:!0,beginAtZero:!0,title:{display:!0,text:"Nombre de dossiers",font:{size:14}}}},interaction:{mode:"index",intersect:!1}}})})})]})]}),(()=>{let e=new Map;a.tableauContraintes.forEach(t=>{t.encrages.forEach(t=>{let a=t.secteur;if("Secteur non d\xe9fini"===a||0===t.problematiques.reduce((e,t)=>e+t.statuts.regularise+t.statuts.ajourne+t.statuts.rejete+t.statuts.nonExamine,0))return;e.has(a)||e.set(a,{regularise:0,ajourne:0,rejete:0,nonExamine:0});let s=e.get(a);s.regularise+=t.problematiques.reduce((e,t)=>e+t.statuts.regularise,0),s.ajourne+=t.problematiques.reduce((e,t)=>e+t.statuts.ajourne,0),s.rejete+=t.problematiques.reduce((e,t)=>e+t.statuts.rejete,0),s.nonExamine+=t.problematiques.reduce((e,t)=>e+t.statuts.nonExamine,0)})});let t=Array.from(e.entries()).map(e=>{let[t,a]=e;return{secteur:t,total:a.regularise+a.ajourne+a.rejete+a.nonExamine,...a}}).sort((e,t)=>t.total-e.total);return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Nombre de Contraintes par Secteur selon la R\xe9solution"})}),(0,s.jsx)("div",{className:"px-6 py-4",children:(0,s.jsx)("div",{style:{height:"600px"},className:"xl:h-[700px] 2xl:h-[800px]",children:(0,s.jsx)(i.yP,{data:{labels:t.map(e=>e.secteur),datasets:[{label:"R\xe9gularis\xe9",data:t.map(e=>e.regularise),backgroundColor:"#10B981"},{label:"Ajourn\xe9",data:t.map(e=>e.ajourne),backgroundColor:"#F59E0B"},{label:"Rejet\xe9",data:t.map(e=>e.rejete),backgroundColor:"#EF4444"},{label:"Non examin\xe9",data:t.map(e=>e.nonExamine),backgroundColor:"#6B7280"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20}},title:{display:!0,text:"R\xe9partition des Contraintes par secteur et statut de r\xe9solution",font:{size:16}},tooltip:{mode:"index",intersect:!1,callbacks:{footer:function(e){let t=0;return e.forEach(function(e){t+=e.parsed.y}),"Total contraintes dans ce secteur: ".concat(t.toLocaleString())}}}},scales:{x:{stacked:!0,title:{display:!0,text:"Secteurs",font:{size:14}},ticks:{maxRotation:45,minRotation:45}},y:{stacked:!0,beginAtZero:!0,title:{display:!0,text:"Nombre de contraintes",font:{size:14}}}},interaction:{mode:"index",intersect:!1}}})})})]})})(),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Vue d'ensemble"})}),(0,s.jsx)("div",{className:"px-6 py-4",children:(0,s.jsx)("div",{style:{height:"600px"},className:"xl:h-[700px] 2xl:h-[800px]",children:(0,s.jsx)(i.yP,{data:{...a.chartWilayas,labels:a.chartWilayas.labels.slice(0,58),datasets:a.chartWilayas.datasets.map(e=>({...e,data:e.data.slice(0,58)}))},options:{indexAxis:"y",responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20}},title:{display:!0,text:" Nombre de dossiers - R\xe9partition d\xe9taill\xe9e par statut",font:{size:18}}},scales:{x:{stacked:!0,beginAtZero:!0,title:{display:!0,text:"Nombre de dossiers",font:{size:16}}},y:{stacked:!0,title:{display:!0,text:"DSA (Tri\xe9es par nombre d\xe9croissant)",font:{size:16}},ticks:{maxRotation:0,minRotation:0,font:{size:12}}}}}})})})]})]})]}):(0,s.jsx)("div",{className:"  mx-auto px-4 py-8",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Aucune donn\xe9e"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Aucune statistique \xe0 afficher pour le moment."}),(0,s.jsx)("button",{onClick:v,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Actualiser"})]})})})}n.t1.register(n.PP,n.kc,n.E8,n.hE,n.m_,n.s$,n.Bs)},3084:(e,t,a)=>{"use strict";a.r(t),a.d(t,{LoadingSpinner:()=>l,default:()=>i});var s=a(5155),r=a(9688);function l(e){let{className:t,color:a="light",size:l="md"}=e;return(0,s.jsx)("div",{className:(0,r.QP)("border-2 rounded-full animate-spin",{light:"border-white/80 border-t-transparent",dark:"border-gray-700 border-t-transparent"}[a],{sm:"w-4 h-4",md:"w-5 h-5",lg:"w-8 h-8"}[l],t)})}let i=l},6676:(e,t,a)=>{Promise.resolve().then(a.bind(a,408))}},e=>{var t=t=>e(e.s=t);e.O(0,[5647,9688,6895,5292,8441,1684,7358],()=>t(6676)),_N_E=e.O()}]);