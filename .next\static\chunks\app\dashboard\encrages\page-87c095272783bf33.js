(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[264],{989:(e,t,s)=>{Promise.resolve().then(s.bind(s,2617))},2617:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(5155),n=s(2115),r=s(767),i=s(345),o=s(3157),l=s(2166),c=s(3109),d=s(98),m=s(6280);function u(){let{encrages:e,encrageStats:t,isLoading:s,error:u,page:g,totalPages:x,goToPage:h,refresh:p}=(0,m.W)({limit:50,includeStats:!0}),[f,j]=(0,n.useState)(null),[v,y]=(0,n.useState)(!1),[N,w]=(0,n.useState)(!1),[b,S]=(0,n.useState)(null),[k,C]=(0,n.useState)({nom:""}),[E,L]=(0,n.useState)(""),P="ADMIN"===f,A=(0,n.useCallback)(async()=>{try{let e=await d.uE.get("/api/auth/session");j(e.role)}catch(e){console.error("Erreur lors du chargement de la session utilisateur:",e),j(null)}},[]);async function B(e){if(e.preventDefault(),!P)return void L("Action non autoris\xe9e.");L("");try{let e={nom:k.nom};N&&b?await (0,d.Zq)("/api/encrages/".concat(b.id),{method:"PUT",body:e}):await (0,d.Zq)("/api/encrages",{method:"POST",body:e}),y(!1),p()}catch(e){L(e.message)}finally{}}async function M(e){if(!P)return void L("Action non autoris\xe9e.");if(confirm("\xcates-vous s\xfbr de vouloir supprimer l'encrage : \"".concat(e.nom,'" ?')))try{await (0,d.Zq)("/api/encrages/".concat(e.id),{method:"DELETE"}),p()}catch(e){L(e.message)}}return(0,n.useEffect)(()=>{A()},[A]),(0,n.useEffect)(()=>{u&&L(u)},[u]),(0,a.jsxs)("div",{className:" mx-auto px-4 py-8 ",children:[t.length>0&&(0,a.jsx)("div",{className:"mb-10",children:(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map(e=>{let t=e.totalCas>0?e.casRegularises/e.totalCas*100:0,s="bg-green-500";return 0===t||t<50?s="bg-red-500":t<100&&(s="bg-orange-500"),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex flex-col justify-between min-h-[200px] pb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.nom}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total des cas:"}),(0,a.jsx)("span",{className:"font-medium text-primary-600",children:e.totalCas})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Cas r\xe9gularis\xe9s:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:e.casRegularises})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"En attente:"}),(0,a.jsx)("span",{className:"font-medium text-orange-600",children:e.totalCas-e.casRegularises})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Progression"}),(0,a.jsxs)("span",{className:"text-sm font-medium ".concat(0===t||t<50?"text-red-700 dark:text-red-500":t<100?"text-orange-700 dark:text-orange-500":"text-green-700 dark:text-green-500"),children:[t.toFixed(0),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700",children:(0,a.jsx)("div",{className:"".concat(s," h-2.5 rounded-full transition-all duration-300"),style:{width:"".concat(t,"%")}})})]})]},e.id)})})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 mt-8 gap-4",children:[(0,a.jsx)("h1",{className:"sm:flex sm:items-center text-2xl font-semibold text-foreground",children:"Gestion des Encrages"}),P&&(0,a.jsx)(r.Button,{onClick:function(){P&&(S(null),C({nom:""}),w(!1),y(!0))},title:"Ajouter un encrage",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"})})})]}),E&&(0,a.jsx)(c.FormError,{message:E}),(0,a.jsxs)("div",{className:"overflow-x-auto",children:[(0,a.jsx)(o.X,{data:e,columns:[{header:"Nom de l'encrage",accessorKey:"nom"}],actions:P?e=>(0,a.jsxs)("div",{className:"flex justify-center items-center space-x-1 sm:space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{P&&(S(e),C({nom:e.nom}),w(!0),y(!0))},title:"Modifier l'encrage",className:"p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"})})}),(0,a.jsx)("button",{onClick:()=>M(e),title:"Supprimer l'encrage",className:"p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}):void 0}),x>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center p-4 border-t",children:[(0,a.jsx)(r.Button,{variant:"outline",onClick:()=>h(g-1),disabled:1===g,children:"Pr\xe9c\xe9dent"}),(0,a.jsxs)("span",{className:"mx-4",children:["Page ",g," sur ",x]}),(0,a.jsx)(r.Button,{variant:"outline",onClick:()=>h(g+1),disabled:g===x,children:"Suivant"})]})]}),P&&v&&(0,a.jsx)(l.Modal,{isOpen:v,onClose:()=>y(!1),title:N?"Modifier l'encrage":"Ajouter un encrage",children:(0,a.jsxs)("form",{onSubmit:B,className:"space-y-4",children:[(0,a.jsx)(i.Input,{id:"nom",label:"Nom de l'encrage",value:k.nom,onChange:e=>C({...k,nom:e.target.value}),required:!0}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(r.Button,{variant:"outline",onClick:()=>y(!1),type:"button",children:"Annuler"}),(0,a.jsx)(r.Button,{type:"submit",isLoading:s,children:N?"Modifier":"Ajouter"})]})]})})]})}},6280:(e,t,s)=>{"use strict";s.d(t,{W:()=>r});var a=s(2115),n=s(98);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{limit:t=50,includeStats:s=!1,search:r="",includeProblematiques:i=!1,sortBy:o="nom",sortDirection:l="asc",dateFrom:c="",dateTo:d="",status:m=""}=e,[u,g]=(0,a.useState)([]),[x,h]=(0,a.useState)([]),[p,f]=(0,a.useState)(!1),[j,v]=(0,a.useState)(null),[y,N]=(0,a.useState)(1),[w,b]=(0,a.useState)(1),[S,k]=(0,a.useState)(0),C="encrages-".concat(t,"-").concat(s,"-").concat(r,"-").concat(i,"-").concat(y,"-").concat(o,"-").concat(l,"-").concat(c,"-").concat(d,"-").concat(m),E=(0,a.useCallback)(async()=>{let e=sessionStorage.getItem(C);if(e)try{let t=JSON.parse(e),n=t.timestamp;if(Date.now()-n<3e5){var a,u;g(t.data),s&&h(t.stats||[]),b((null==(a=t.pagination)?void 0:a.totalPages)||1),k((null==(u=t.pagination)?void 0:u.total)||0);return}}catch(e){console.error("Erreur lors de la lecture du cache:",e)}f(!0),v(null);try{let e=new URLSearchParams;e.append("page",y.toString()),e.append("limit",t.toString()),e.append("includeStats",s.toString()),e.append("includeProblematiques",i.toString()),r&&e.append("search",r),o&&e.append("sortBy",o),l&&e.append("sortDirection",l),c&&e.append("dateFrom",c),d&&e.append("dateTo",d),m&&e.append("status",m);let a="/api/encrages?".concat(e.toString()),u=await n.uE.get(a);if(g(u.data),b(u.pagination.totalPages),k(u.pagination.total),s){let e=u.data.filter(e=>e.casStats).map(e=>({id:e.id,nom:e.nom,totalCas:e.casStats.total,casRegularises:e.casStats.regularises}));h(e),sessionStorage.setItem(C,JSON.stringify({data:u.data,stats:e,pagination:u.pagination,timestamp:Date.now()}))}else sessionStorage.setItem(C,JSON.stringify({data:u.data,pagination:u.pagination,timestamp:Date.now()}))}catch(e){v(e.message||"Erreur lors du chargement des encrages"),console.error("Erreur lors du chargement des encrages:",e)}finally{f(!1)}},[C,i,s,t,y,r]);return(0,a.useEffect)(()=>{E()},[E]),{encrages:u,encrageStats:x,isLoading:p,error:j,page:y,totalPages:w,totalItems:S,goToPage:(0,a.useCallback)(e=>{N(e)},[]),refresh:E}}}},e=>{var t=t=>e(e.s=t);e.O(0,[9688,9741,8441,1684,7358],()=>t(989)),_N_E=e.O()}]);