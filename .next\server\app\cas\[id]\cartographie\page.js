(()=>{var e={};e.id=9940,e.ids=[9940],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3989:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c,dynamic:()=>d});var t=r(60687),a=r(43210),i=r(16189);r(69266);var n=r(87056),o=r(89679),l=r(19763);let d="force-dynamic";function c(){(0,i.useParams)().id;let[e,s]=(0,a.useState)(null),[r,d]=(0,a.useState)(!0),[c,p]=(0,a.useState)(null),[u,m]=(0,a.useState)([36.75,3.06]);return r?(0,t.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,t.jsx)(n.k,{})}):c?(0,t.jsx)(o.j,{message:c}):e?(0,t.jsxs)("div",{className:" mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:["Cartographie - ",e.nom]}),(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm border",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Superficie:"}),(0,t.jsxs)("span",{className:"ml-2",children:[e.superficie," Ha"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Probl\xe9matique:"}),(0,t.jsx)("span",{className:"ml-2",children:e.problematique?.problematique||"Non d\xe9finie"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Communes:"}),(0,t.jsx)("span",{className:"ml-2",children:e.communes.map(e=>e.nom).join(", ")})]})]}),e.observation&&(0,t.jsxs)("div",{className:"mt-3 pt-3 border-t",children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Observation:"}),(0,t.jsx)("p",{className:"mt-1 text-gray-700",children:e.observation})]})]})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,t.jsx)(l.A,{casList:e?[e]:[],selectedCas:e,setSelectedCas:()=>{},center:u,zoom:13,height:"600px"})}),!e.geojson&&(0,t.jsx)("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,t.jsxs)("p",{className:"text-yellow-800",children:[(0,t.jsx)("strong",{children:"Aucune donn\xe9e g\xe9ographique disponible"})," ","pour ce cas. Les coordonn\xe9es ou le fichier GeoJSON n'ont pas \xe9t\xe9 fournis."]})})]}):(0,t.jsx)(o.j,{message:"Cas non trouv\xe9"})}},8175:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["cas",{children:["[id]",{children:["cartographie",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72663)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\[id]\\cartographie\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,92392)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\[id]\\cartographie\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/cas/[id]/cartographie/page",pathname:"/cas/[id]/cartographie",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20094:(e,s,r)=>{Promise.resolve().then(r.bind(r,72663))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72663:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i,dynamic:()=>a});var t=r(12907);let a=(0,t.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\[id]\\cartographie\\page.tsx","dynamic"),i=(0,t.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\cartographie\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\[id]\\cartographie\\page.tsx","default")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},88550:(e,s,r)=>{Promise.resolve().then(r.bind(r,3989))},92392:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(37413);function a({children:e}){return(0,t.jsx)("div",{className:"w-full px-[5px] py-2 max-w-screen-xl mx-auto font-inter text-[0.95rem] text-gray-800",style:{fontFamily:"Inter, Segoe UI, Arial, sans-serif",lineHeight:1.5},children:e})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[7719,3903,5262,2348,2797,2997],()=>r(8175));module.exports=t})();