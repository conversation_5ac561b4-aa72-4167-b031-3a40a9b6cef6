"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/statistiques/page",{

/***/ "(app-pages-browser)/./app/dashboard/statistiques/page.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/statistiques/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatistiquesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-chartjs-2 */ \"(app-pages-browser)/./node_modules/react-chartjs-2/dist/index.js\");\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! chart.js */ \"(app-pages-browser)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var _app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/components/LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/components/RoleBasedAccess */ \"(app-pages-browser)/./app/components/RoleBasedAccess.tsx\");\n/* harmony import */ var _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/hooks/usePermissions */ \"(app-pages-browser)/./lib/hooks/usePermissions.ts\");\n/* harmony import */ var _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/contexts/DataRefreshContext */ \"(app-pages-browser)/./app/contexts/DataRefreshContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mapping des wilayaId vers les noms des DSA\nconst wilayaNames = {\n    1: \"Adrar\",\n    2: \"Chlef\",\n    3: \"Laghouat\",\n    4: \"Oum El Bouaghi\",\n    5: \"Batna\",\n    6: \"Béjaïa\",\n    7: \"Biskra\",\n    8: \"Béchar\",\n    9: \"Blida\",\n    10: \"Bouira\",\n    11: \"Tamanrasset\",\n    12: \"Tébessa\",\n    13: \"Tlemcen\",\n    14: \"Tiaret\",\n    15: \"Tizi Ouzou\",\n    16: \"Alger\",\n    17: \"Djelfa\",\n    18: \"Jijel\",\n    19: \"Sétif\",\n    20: \"Saïda\",\n    21: \"Skikda\",\n    22: \"Sidi Bel Abbès\",\n    23: \"Annaba\",\n    24: \"Guelma\",\n    25: \"Constantine\",\n    26: \"Médéa\",\n    27: \"Mostaganem\",\n    28: \"M'Sila\",\n    29: \"Mascara\",\n    30: \"Ouargla\",\n    31: \"Oran\",\n    32: \"El Bayadh\",\n    33: \"Illizi\",\n    34: \"Bordj Bou Arréridj\",\n    35: \"Boumerdès\",\n    36: \"El Tarf\",\n    37: \"Tindouf\",\n    38: \"Tissemsilt\",\n    39: \"El Oued\",\n    40: \"Khenchela\",\n    41: \"Souk Ahras\",\n    42: \"Tipaza\",\n    43: \"Mila\",\n    44: \"Aïn Defla\",\n    45: \"Naâma\",\n    46: \"Aïn Témouchent\",\n    47: \"Ghardaïa\",\n    48: \"Relizane\",\n    49: \"Timimoun\",\n    50: \"Bordj Badji Mokhtar\",\n    51: \"Ouled Djellal\",\n    52: \"Béni Abbès\",\n    53: \"In Salah\",\n    54: \"In Guezzam\",\n    55: \"Touggourt\",\n    56: \"Djanet\",\n    57: \"El M'Ghair\",\n    58: \"El Meniaa\"\n};\nchart_js__WEBPACK_IMPORTED_MODULE_7__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_7__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.BarElement, chart_js__WEBPACK_IMPORTED_MODULE_7__.Title, chart_js__WEBPACK_IMPORTED_MODULE_7__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_7__.Legend, chart_js__WEBPACK_IMPORTED_MODULE_7__.ArcElement);\nfunction StatistiquesPage() {\n    _s();\n    const { user, isAdmin } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const [analyseData, setAnalyseData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedWilaya, setSelectedWilaya] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"statuts\");\n    // Determine if dropdown should be shown and set initial selectedWilaya\n    const showDropdown = isAdmin || !(user === null || user === void 0 ? void 0 : user.wilayaId);\n    const initialWilaya = (user === null || user === void 0 ? void 0 : user.wilayaId) && !isAdmin ? user.wilayaId.toString() : \"\";\n    // Charger les données d'analyse\n    const loadAnalyse = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const url = selectedWilaya ? \"/api/stats/analyse-complete?wilayaId=\".concat(selectedWilaya) : \"/api/stats/analyse-complete\";\n            console.log(\"📊 Chargement de l'analyse depuis:\", url);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n            if (response.success && response.data) {\n                setAnalyseData(response.data);\n                console.log(\"✅ Analyse chargée:\", response.data);\n            } else {\n                setError(response.error || \"Erreur lors du chargement de l'analyse\");\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du chargement de l'analyse:\", err);\n            setError(err.message || \"Erreur inconnue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            loadAnalyse();\n        }\n    }[\"StatistiquesPage.useEffect\"], [\n        selectedWilaya\n    ]);\n    // Set initial selectedWilaya based on user\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            if (user) {\n                setSelectedWilaya(initialWilaya);\n            }\n        }\n    }[\"StatistiquesPage.useEffect\"], [\n        user,\n        initialWilaya\n    ]);\n    // Enregistrer le callback de rafraîchissement pour les statistiques\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh)(\"statistiques-analyse-complete\", loadAnalyse, [\n        selectedWilaya\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"  mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 13\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"    mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-red-600 mb-2\",\n                            children: \"Erreur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadAnalyse,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"R\\xe9essayer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 13\n        }, this);\n    }\n    if (!analyseData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"  mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-2\",\n                            children: \"Aucune donn\\xe9e\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Aucune statistique \\xe0 afficher pour le moment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadAnalyse,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"Actualiser\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 235,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"  mx-auto px-4 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Analyse Compl\\xe8te des Dossiers et Contraintes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_4__.UserRoleBadge, {\n                                        className: \"mt-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWilaya,\n                                        onChange: (e)=>setSelectedWilaya(e.target.value),\n                                        disabled: loading,\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-48 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Toutes les DSA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 33\n                                            }, this),\n                                            Array.from({\n                                                length: 58\n                                            }, (_, i)=>{\n                                                const wilayaId = i + 1;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: wilayaId.toString(),\n                                                    children: wilayaNames[wilayaId] || \"DSA \".concat(wilayaId)\n                                                }, wilayaId, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 41\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: loadAnalyse,\n                                        disabled: loading,\n                                        className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\",\n                                        children: [\n                                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {\n                                                size: \"sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 41\n                                            }, this),\n                                            \"Actualiser\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-2 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-blue-800\",\n                                        children: \"\\uD83D\\uDD35 Total dossiers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-900\",\n                                        children: analyseData.totalCas.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-green-800\",\n                                        children: \"\\uD83D\\uDFE2 R\\xe9gularis\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-900\",\n                                        children: (()=>{\n                                            const totalRegularises = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.regularise, 0), 0);\n                                            return totalRegularises.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-yellow-800\",\n                                        children: \"\\uD83D\\uDFE1 Ajourn\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-yellow-900\",\n                                        children: (()=>{\n                                            const totalAjournes = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.ajourne, 0), 0);\n                                            return totalAjournes.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-800\",\n                                        children: \"⚪ Non examin\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: (()=>{\n                                            const totalNonExamines = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.nonExamine, 0), 0);\n                                            return totalNonExamines.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-red-800\",\n                                        children: \"\\uD83D\\uDD34 Rejet\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-900\",\n                                        children: (()=>{\n                                            const totalRejetes = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.rejete, 0), 0);\n                                            return totalRejetes.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"statuts\"),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"statuts\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: \"\\uD83D\\uDCCA Analyse des Dossiers par DSA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"contraintes\"),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"contraintes\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: \"\\uD83D\\uDD0D Analyse des Contraintes par Secteur\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"charts\"),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"charts\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: \"\\uD83D\\uDCC8 Graphiques Dynamiques\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 13\n            }, this),\n            activeTab === \"statuts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto xl:overflow-visible\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full xl:min-w-0 divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"DSA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Total dossiers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"R\\xe9gularis\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Ajourn\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Rejet\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Non examin\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Taux R\\xe9gularisation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: (()=>{\n                                            // Fusionner toutes les données par DSA\n                                            const dsaStats = new Map();\n                                            analyseData.tableauStatuts.forEach((statutData)=>{\n                                                statutData.wilayas.forEach((wilaya)=>{\n                                                    if (!dsaStats.has(wilaya.wilayaId)) dsaStats.set(wilaya.wilayaId, {\n                                                        dsaName: wilayaNames[wilaya.wilayaId] || \"DSA \".concat(wilaya.wilayaId),\n                                                        total: 0,\n                                                        regularise: 0,\n                                                        ajourne: 0,\n                                                        rejete: 0,\n                                                        nonExamine: 0\n                                                    });\n                                                    const stats = dsaStats.get(wilaya.wilayaId);\n                                                    stats.total += wilaya.total;\n                                                    stats.regularise += wilaya.regularise;\n                                                    stats.ajourne += wilaya.ajourne;\n                                                    stats.rejete += wilaya.rejete;\n                                                    stats.nonExamine += wilaya.nonExamine;\n                                                });\n                                            });\n                                            return Array.from(dsaStats.entries()).sort((param, param1)=>{\n                                                let [a] = param, [b] = param1;\n                                                return a - b;\n                                            }).map((param)=>{\n                                                let [wilayaId, stats] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                            children: stats.dsaName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold\",\n                                                            children: stats.total.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium\",\n                                                            children: stats.regularise.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-yellow-600\",\n                                                            children: stats.ajourne.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-red-600\",\n                                                            children: stats.rejete.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-600\",\n                                                            children: stats.nonExamine.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium\",\n                                                            children: stats.total > 0 ? \"\".concat(Math.round(stats.regularise / stats.total * 100), \"%\") : \"0%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 57\n                                                        }, this)\n                                                    ]\n                                                }, wilayaId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 53\n                                                }, this);\n                                            });\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 442,\n                columnNumber: 17\n            }, this),\n            activeTab === \"contraintes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Analyse des Contraintes par Structure Administrative\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 21\n                    }, this),\n                    analyseData.tableauContraintes.map((dsaData)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: wilayaNames[dsaData.wilayaId] || \"DSA \".concat(dsaData.wilayaId)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: dsaData.encrages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 italic\",\n                                        children: \"Aucune contrainte identifi\\xe9e pour cette DSA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: dsaData.encrages.map((encrage, encrageIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-l-4 border-blue-500 pl-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: encrage.secteur === \"Secteur non défini\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-md font-semibold text-yellow-800\",\n                                                                    children: \"\\uD83D\\uDCCB Dossiers sans contrainte\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-yellow-700\",\n                                                                    children: [\n                                                                        \"Total de dossiersss sans contrainte:\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold text-yellow-900\",\n                                                                            children: encrage.totalCas\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-md font-semibold text-gray-800\",\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCCB\",\n                                                                        \" \",\n                                                                        encrage.secteur\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"Structure Administrative:\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: encrage.secteur\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 69\n                                                                        }, this),\n                                                                        \" \",\n                                                                        \"| Total contraintes:\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: encrage.totalCas\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 53\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto xl:overflow-visible\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"min-w-full xl:min-w-0 divide-y divide-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Probl\\xe9matique\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Total Contraintes\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 664,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"R\\xe9gularis\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 668,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Ajourn\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Rejet\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Non examin\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Taux R\\xe9gularisation\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 681,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 65\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"bg-white divide-y divide-gray-200\",\n                                                                    children: encrage.problematiques.map((prob, probIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50 \".concat(encrage.secteur === \"Secteur non défini\" ? \"bg-yellow-50\" : \"\"),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-900\",\n                                                                                    children: prob.problematiqueName\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 704,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-semibold text-gray-900\",\n                                                                                    children: prob.count\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-green-600 font-medium\",\n                                                                                    children: prob.statuts.regularise\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 714,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-yellow-600\",\n                                                                                    children: prob.statuts.ajourne\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 721,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-red-600\",\n                                                                                    children: prob.statuts.rejete\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 728,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                    children: prob.statuts.nonExamine\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-blue-600 font-medium\",\n                                                                                    children: prob.count > 0 ? \"\".concat(Math.round(prob.statuts.regularise / prob.count * 100), \"%\") : \"0%\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 742,\n                                                                                    columnNumber: 77\n                                                                                }, this)\n                                                                            ]\n                                                                        }, probIndex, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 73\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 57\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                ]\n                                            }, encrageIndex, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 49\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, dsaData.wilayaId, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 25\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 578,\n                columnNumber: 17\n            }, this),\n            activeTab === \"charts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Graphiques Dynamiques\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"R\\xe9partition par Statut\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                height: \"500px\"\n                                            },\n                                            className: \"xl:h-[600px] 2xl:h-[700px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Doughnut, {\n                                                data: analyseData.chartStatuts,\n                                                options: {\n                                                    responsive: true,\n                                                    maintainAspectRatio: false,\n                                                    plugins: {\n                                                        legend: {\n                                                            position: \"bottom\",\n                                                            labels: {\n                                                                padding: 20,\n                                                                usePointStyle: true\n                                                            }\n                                                        },\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Distribution des dossiers par statut\",\n                                                            font: {\n                                                                size: 16\n                                                            }\n                                                        },\n                                                        tooltip: {\n                                                            callbacks: {\n                                                                label: function(context) {\n                                                                    const total = context.dataset.data.reduce((a, b)=>a + b, 0);\n                                                                    const percentage = (context.parsed / total * 100).toFixed(1);\n                                                                    return \"\".concat(context.label, \": \").concat(context.parsed.toLocaleString(), \" (\").concat(percentage, \"%)\");\n                                                                }\n                                                            }\n                                                        }\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 785,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 778,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Nombre de dossiers par DSA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                height: \"500px\"\n                                            },\n                                            className: \"xl:h-[600px] 2xl:h-[700px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Bar, {\n                                                data: analyseData.chartWilayas,\n                                                options: {\n                                                    responsive: true,\n                                                    maintainAspectRatio: false,\n                                                    plugins: {\n                                                        legend: {\n                                                            position: \"top\",\n                                                            labels: {\n                                                                usePointStyle: true,\n                                                                padding: 15\n                                                            }\n                                                        },\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Répartition des dossiers par DSA et statut\",\n                                                            font: {\n                                                                size: 16\n                                                            }\n                                                        },\n                                                        tooltip: {\n                                                            mode: \"index\",\n                                                            intersect: false,\n                                                            callbacks: {\n                                                                footer: function(tooltipItems) {\n                                                                    let total = 0;\n                                                                    tooltipItems.forEach(function(tooltipItem) {\n                                                                        total += tooltipItem.parsed.y;\n                                                                    });\n                                                                    return \"Total: \".concat(total.toLocaleString());\n                                                                }\n                                                            }\n                                                        }\n                                                    },\n                                                    scales: {\n                                                        x: {\n                                                            stacked: true,\n                                                            title: {\n                                                                display: true,\n                                                                text: \"DSA (Triées par nombre total de dossiers)\",\n                                                                font: {\n                                                                    size: 14\n                                                                }\n                                                            },\n                                                            ticks: {\n                                                                maxRotation: 45,\n                                                                minRotation: 45\n                                                            }\n                                                        },\n                                                        y: {\n                                                            stacked: true,\n                                                            beginAtZero: true,\n                                                            title: {\n                                                                display: true,\n                                                                text: \"Nombre de dossiers\",\n                                                                font: {\n                                                                    size: 14\n                                                                }\n                                                            }\n                                                        }\n                                                    },\n                                                    interaction: {\n                                                        mode: \"index\",\n                                                        intersect: false\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 848,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 841,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 776,\n                        columnNumber: 21\n                    }, this),\n                    (()=>{\n                        // Aggregate actual blocages by sector and resolution status\n                        const sectorStats = new Map();\n                        // Agréger les contraintes par secteur et statut de résolution\n                        // Chaque statut représente le nombre de contraintes avec cette résolution\n                        analyseData.tableauContraintes.forEach((dsaData)=>{\n                            dsaData.encrages.forEach((encrage)=>{\n                                const secteur = encrage.secteur;\n                                // Skip \"Secteur non défini\" as it represents cases without constraints\n                                if (secteur === \"Secteur non défini\") {\n                                    return;\n                                }\n                                // Only include sectors that actually have constraints\n                                const totalConstraints = encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.regularise + prob.statuts.ajourne + prob.statuts.rejete + prob.statuts.nonExamine, 0);\n                                // Skip if no actual constraints in this sector\n                                if (totalConstraints === 0) {\n                                    return;\n                                }\n                                if (!sectorStats.has(secteur)) {\n                                    sectorStats.set(secteur, {\n                                        regularise: 0,\n                                        ajourne: 0,\n                                        rejete: 0,\n                                        nonExamine: 0\n                                    });\n                                }\n                                const stats = sectorStats.get(secteur);\n                                // Count blocages by their resolution status\n                                // Each status count represents the number of blocages with that resolution\n                                stats.regularise += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.regularise, 0);\n                                stats.ajourne += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.ajourne, 0);\n                                stats.rejete += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.rejete, 0);\n                                stats.nonExamine += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.nonExamine, 0);\n                            });\n                        });\n                        // Trier les secteurs par nombre total de contraintes décroissant\n                        const sortedSecteurs = Array.from(sectorStats.entries()).map((param)=>{\n                            let [secteur, stats] = param;\n                            return {\n                                secteur,\n                                total: stats.regularise + stats.ajourne + stats.rejete + stats.nonExamine,\n                                ...stats\n                            };\n                        }).sort((a, b)=>b.total - a.total);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Nombre de Contraintes par Secteur selon la R\\xe9solution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1026,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1025,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"600px\"\n                                        },\n                                        className: \"xl:h-[700px] 2xl:h-[800px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Bar, {\n                                            data: {\n                                                labels: sortedSecteurs.map((item)=>item.secteur),\n                                                datasets: [\n                                                    {\n                                                        label: \"Régularisé\",\n                                                        data: sortedSecteurs.map((item)=>item.regularise),\n                                                        backgroundColor: \"#10B981\"\n                                                    },\n                                                    {\n                                                        label: \"Ajourné\",\n                                                        data: sortedSecteurs.map((item)=>item.ajourne),\n                                                        backgroundColor: \"#F59E0B\"\n                                                    },\n                                                    {\n                                                        label: \"Rejeté\",\n                                                        data: sortedSecteurs.map((item)=>item.rejete),\n                                                        backgroundColor: \"#EF4444\"\n                                                    },\n                                                    {\n                                                        label: \"Non examiné\",\n                                                        data: sortedSecteurs.map((item)=>item.nonExamine),\n                                                        backgroundColor: \"#6B7280\"\n                                                    }\n                                                ]\n                                            },\n                                            options: {\n                                                responsive: true,\n                                                maintainAspectRatio: false,\n                                                plugins: {\n                                                    legend: {\n                                                        position: \"top\",\n                                                        labels: {\n                                                            usePointStyle: true,\n                                                            padding: 20\n                                                        }\n                                                    },\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Répartition des Contraintes par secteur et statut de résolution\",\n                                                        font: {\n                                                            size: 16\n                                                        }\n                                                    },\n                                                    tooltip: {\n                                                        mode: \"index\",\n                                                        intersect: false,\n                                                        callbacks: {\n                                                            footer: function(tooltipItems) {\n                                                                let total = 0;\n                                                                tooltipItems.forEach(function(tooltipItem) {\n                                                                    total += tooltipItem.parsed.y;\n                                                                });\n                                                                return \"Total contraintes dans ce secteur: \".concat(total.toLocaleString());\n                                                            }\n                                                        }\n                                                    }\n                                                },\n                                                scales: {\n                                                    x: {\n                                                        stacked: true,\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Secteurs\",\n                                                            font: {\n                                                                size: 14\n                                                            }\n                                                        },\n                                                        ticks: {\n                                                            maxRotation: 45,\n                                                            minRotation: 45\n                                                        }\n                                                    },\n                                                    y: {\n                                                        stacked: true,\n                                                        beginAtZero: true,\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Nombre de contraintes\",\n                                                            font: {\n                                                                size: 14\n                                                            }\n                                                        }\n                                                    }\n                                                },\n                                                interaction: {\n                                                    mode: \"index\",\n                                                    intersect: false\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 29\n                        }, this);\n                    })(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Vue d'ensemble\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        height: \"600px\"\n                                    },\n                                    className: \"xl:h-[700px] 2xl:h-[800px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Bar, {\n                                        data: {\n                                            ...analyseData.chartWilayas,\n                                            labels: analyseData.chartWilayas.labels.slice(0, 58),\n                                            datasets: analyseData.chartWilayas.datasets.map((dataset)=>({\n                                                    ...dataset,\n                                                    data: dataset.data.slice(0, 58)\n                                                }))\n                                        },\n                                        options: {\n                                            indexAxis: \"y\",\n                                            responsive: true,\n                                            maintainAspectRatio: false,\n                                            plugins: {\n                                                legend: {\n                                                    position: \"top\",\n                                                    labels: {\n                                                        usePointStyle: true,\n                                                        padding: 20\n                                                    }\n                                                },\n                                                title: {\n                                                    display: true,\n                                                    text: \" Nombre de dossiers - Répartition détaillée par statut\",\n                                                    font: {\n                                                        size: 18\n                                                    }\n                                                }\n                                            },\n                                            scales: {\n                                                x: {\n                                                    stacked: true,\n                                                    beginAtZero: true,\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Nombre de dossiers\",\n                                                        font: {\n                                                            size: 16\n                                                        }\n                                                    }\n                                                },\n                                                y: {\n                                                    stacked: true,\n                                                    title: {\n                                                        display: true,\n                                                        text: \"DSA (Triées par nombre décroissant)\",\n                                                        font: {\n                                                            size: 16\n                                                        }\n                                                    },\n                                                    ticks: {\n                                                        maxRotation: 0,\n                                                        minRotation: 0,\n                                                        font: {\n                                                            size: 12\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1168,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1167,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1161,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 771,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 9\n    }, this);\n}\n_s(StatistiquesPage, \"DmjzyVXiKxnNDAs8JncvHRlatEI=\", false, function() {\n    return [\n        _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh\n    ];\n});\n_c = StatistiquesPage;\nvar _c;\n$RefreshReg$(_c, \"StatistiquesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/statistiques/page.tsx\n"));

/***/ })

});