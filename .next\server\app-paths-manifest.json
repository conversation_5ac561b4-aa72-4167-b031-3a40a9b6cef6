{"/_not-found/page": "app/_not-found/page.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/admin/debug-api/route": "app/api/admin/debug-api/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/auth/session/route": "app/api/auth/session/route.js", "/api/cas/[id]/blocages/route": "app/api/cas/[id]/blocages/route.js", "/api/blocages/[id]/route": "app/api/blocages/[id]/route.js", "/api/blocages/[id]/regularise/route": "app/api/blocages/[id]/regularise/route.js", "/api/blocages/route": "app/api/blocages/route.js", "/api/cas/[id]/kml/route": "app/api/cas/[id]/kml/route.js", "/api/cas/export-batch/route": "app/api/cas/export-batch/route.js", "/api/cas/[id]/pdf/route": "app/api/cas/[id]/pdf/route.js", "/api/cas/[id]/route": "app/api/cas/[id]/route.js", "/api/cas/export/route": "app/api/cas/export/route.js", "/api/communes/route": "app/api/communes/route.js", "/api/db-test/route": "app/api/db-test/route.js", "/api/cas/route": "app/api/cas/route.js", "/api/kml-layers/route": "app/api/kml-layers/route.js", "/api/encrages/[id]/route": "app/api/encrages/[id]/route.js", "/api/problematiques/[id]/route": "app/api/problematiques/[id]/route.js", "/api/stats/analyse-complete/route": "app/api/stats/analyse-complete/route.js", "/api/encrages/route": "app/api/encrages/route.js", "/api/secteurs/route": "app/api/secteurs/route.js", "/api/problematiques/route": "app/api/problematiques/route.js", "/api/stats/cas-par-wilaya/route": "app/api/stats/cas-par-wilaya/route.js", "/api/stats/cas-optimized/route": "app/api/stats/cas-optimized/route.js", "/api/stats/dsa-editors/route": "app/api/stats/dsa-editors/route.js", "/api/stats/cas/route": "app/api/stats/cas/route.js", "/api/stats/encrages/route": "app/api/stats/encrages/route.js", "/api/stats/evolution-blocages-regularises/route": "app/api/stats/evolution-blocages-regularises/route.js", "/api/stats/regularisation-par-secteur/route": "app/api/stats/regularisation-par-secteur/route.js", "/api/stats/resolution-optimized/route": "app/api/stats/resolution-optimized/route.js", "/api/test-prisma/route": "app/api/test-prisma/route.js", "/api/stats/resolution/route": "app/api/stats/resolution/route.js", "/api/stats/test-simple/route": "app/api/stats/test-simple/route.js", "/api/stats/wilayas-secteurs/route": "app/api/stats/wilayas-secteurs/route.js", "/cas/[id]/blocages/route": "app/cas/[id]/blocages/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/cas/[id]/communes/route": "app/cas/[id]/communes/route.js", "/api/users/messaging/route": "app/api/users/messaging/route.js", "/cas/[id]/secteurs/route": "app/cas/[id]/secteurs/route.js", "/api/users/route": "app/api/users/route.js", "/admin/performance/page": "app/admin/performance/page.js", "/page": "app/page.js", "/users/page": "app/users/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/dashboard/statistiques/page": "app/dashboard/statistiques/page.js", "/cas/add/page": "app/cas/add/page.js", "/cas/[id]/page": "app/cas/[id]/page.js", "/cas/page": "app/cas/page.js", "/dashboard/page": "app/dashboard/page.js", "/encrages/page": "app/encrages/page.js", "/dashboard/reglementation/page": "app/dashboard/reglementation/page.js", "/problematiques/page": "app/problematiques/page.js", "/dashboard/cartographie/page": "app/dashboard/cartographie/page.js", "/cas/[id]/cartographie/page": "app/cas/[id]/cartographie/page.js", "/dashboard/cas/page": "app/dashboard/cas/page.js", "/dashboard/encrages/page": "app/dashboard/encrages/page.js", "/dashboard/problematiques/page": "app/dashboard/problematiques/page.js", "/dashboard/cas/[id]/page": "app/dashboard/cas/[id]/page.js"}