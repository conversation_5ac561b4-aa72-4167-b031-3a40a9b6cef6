"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/cartographie/page",{

/***/ "(app-pages-browser)/./app/dashboard/cartographie/page.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/cartographie/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CartographiePage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var _app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/components/LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _app_components_FormError__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/components/FormError */ \"(app-pages-browser)/./app/components/FormError.tsx\");\n/* harmony import */ var _app_components_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/components/Button */ \"(app-pages-browser)/./app/components/Button.tsx\");\n/* harmony import */ var _app_components_Select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/Select */ \"(app-pages-browser)/./app/components/Select.tsx\");\n/* harmony import */ var _app_components_MapComponentV2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/MapComponentV2 */ \"(app-pages-browser)/./app/components/MapComponentV2.tsx\");\n/* harmony import */ var _app_components_CasListWithZoom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/CasListWithZoom */ \"(app-pages-browser)/./app/components/CasListWithZoom.tsx\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \nvar _s = $RefreshSig$();\nconst dynamic = \"force-dynamic\";\n\n\n\n\n\n\n\n\n\nfunction CartographiePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [casList, setCasList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [problematiques, setProblematiques] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [encrages, setEncrages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCas, setSelectedCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filtres\n    const [selectedEncrage, setSelectedEncrage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedProblematique, setSelectedProblematique] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showRegularisedOnly, setShowRegularisedOnly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // État pour les couches KML\n    const [kmlLayers, setKmlLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mapCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        36.75,\n        3.06\n    ]); // Centre par défaut (Alger)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartographiePage.useEffect\": ()=>{\n            loadInitialData();\n        }\n    }[\"CartographiePage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartographiePage.useEffect\": ()=>{\n            loadCasData();\n        }\n    }[\"CartographiePage.useEffect\"], [\n        selectedEncrage,\n        selectedProblematique,\n        showRegularisedOnly\n    ]);\n    const loadInitialData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [casResponse, problematiqueData, encrageData] = await Promise.all([\n                (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_3__.fetchApi)(\"/api/cas?withGeojson=true&includeKML=true&pageSize=1000\"),\n                (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_3__.fetchApi)(\"/api/problematiques?context=formCreation\"),\n                (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_3__.fetchApi)(\"/api/encrages\")\n            ]);\n            setCasList((casResponse === null || casResponse === void 0 ? void 0 : casResponse.data) || []);\n            setProblematiques(problematiqueData || []);\n            // /api/encrages retourne un objet paginé { data, pagination }\n            const encragesArray = Array.isArray(encrageData) ? encrageData : Array.isArray(encrageData === null || encrageData === void 0 ? void 0 : encrageData.data) ? encrageData.data : [];\n            setEncrages(encragesArray);\n        } catch (err) {\n            console.error(\"Erreur lors du chargement des données:\", err);\n            setError(\"Erreur lors du chargement des données\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadCasData = async ()=>{\n        try {\n            let url = \"/api/cas?withGeojson=true&includeKML=true&pageSize=1000\";\n            const params = new URLSearchParams();\n            if (selectedEncrage) params.append(\"encrageId\", selectedEncrage);\n            if (selectedProblematique) params.append(\"problematiqueId\", selectedProblematique);\n            if (showRegularisedOnly) params.append(\"regularisation\", \"true\");\n            if (params.toString()) {\n                url += \"&\".concat(params.toString());\n            }\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_3__.fetchApi)(url);\n            if (response) {\n                setCasList(response.data || []);\n            } else {\n                setCasList([]);\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du filtrage des cas:\", err);\n            setError(\"Erreur lors du filtrage des cas\");\n        }\n    };\n    const filteredProblematiques = selectedEncrage ? (Array.isArray(problematiques) ? problematiques : []).filter((p)=>{\n        var _p_encrage;\n        return ((_p_encrage = p.encrage) === null || _p_encrage === void 0 ? void 0 : _p_encrage.id) === selectedEncrage;\n    }) : Array.isArray(problematiques) ? problematiques : [];\n    console.log(\"casList is:\", casList);\n    // Fix the statistics calculation - casList is already an array\n    const casWithGeojson = casList.filter((cas)=>{\n        var _cas_geojson;\n        return ((_cas_geojson = cas.geojson) === null || _cas_geojson === void 0 ? void 0 : _cas_geojson.coordinates) || cas.kmlData && cas.kmlData.features;\n    });\n    // Debug: Log KML data\n    console.log(\"casList with KML:\", casList.filter((cas)=>cas.kmlData));\n    console.log(\"casList with geojson:\", casList.filter((cas)=>cas.geojson));\n    console.log(\"casWithGeojson count:\", casWithGeojson.length);\n    console.log(\"Total casList length:\", casList.length);\n    console.log(\"Sample cas item:\", casList[0]);\n    const handleCasClick = (cas)=>{\n        setSelectedCas(cas);\n    };\n    const handleViewCasDetails = (casId)=>{\n        router.push(\"/cas/\".concat(casId));\n    };\n    const handleViewCasCartographie = (casId)=>{\n        router.push(\"/cas/\".concat(casId, \"/cartographie\"));\n    };\n    const handleZoomToCas = (casId)=>{\n        // Cette fonction sera appelée par le composant MapComponent\n        // via la fonction globale window.zoomToCas\n        if ( true && window.zoomToCas) {\n            window.zoomToCas(casId);\n        }\n    };\n    const handleDownloadGeoData = (cas)=>{\n        let data;\n        let filename;\n        let mimeType;\n        if (cas.kmlData && cas.kmlData.features) {\n            // Download as KML - but kmlData is parsed JSON, need to convert back or download original\n            // For now, download as JSON\n            data = JSON.stringify(cas.kmlData, null, 2);\n            filename = \"\".concat(cas.nom, \"_geometry.json\");\n            mimeType = \"application/json\";\n        } else if (cas.geojson) {\n            data = JSON.stringify(cas.geojson, null, 2);\n            filename = \"\".concat(cas.nom, \"_geometry.geojson\");\n            mimeType = \"application/geo+json\";\n        } else {\n            alert(\"Aucune donnée géographique disponible pour ce dossier.\");\n            return;\n        }\n        const blob = new Blob([\n            data\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    // Fonctions pour gérer les couches KML\n    const handleToggleKMLLayer = (layerId)=>{\n        setKmlLayers((prev)=>prev.map((layer)=>layer.id === layerId ? {\n                    ...layer,\n                    visible: !layer.visible\n                } : layer));\n    };\n    const handleRemoveKMLLayer = (layerId)=>{\n        setKmlLayers((prev)=>prev.filter((layer)=>layer.id !== layerId));\n    };\n    const handleChangeKMLLayerColor = (layerId, color)=>{\n        setKmlLayers((prev)=>prev.map((layer)=>layer.id === layerId ? {\n                    ...layer,\n                    color\n                } : layer));\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                lineNumber: 267,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n            lineNumber: 266,\n            columnNumber: 13\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_FormError__WEBPACK_IMPORTED_MODULE_5__.FormError, {\n            message: error\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n            lineNumber: 273,\n            columnNumber: 16\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" mx-auto px-2 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-2 rounded-lg shadow-sm border mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Encrage juridique (programme)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                            value: selectedEncrage,\n                                            onChange: (e)=>{\n                                                setSelectedEncrage(e.target.value);\n                                                setSelectedProblematique(\"\"); // Reset problématique\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Tous les encrages\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 33\n                                                }, this),\n                                                (Array.isArray(encrages) ? encrages : []).map((encrage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: encrage.id,\n                                                        children: encrage.nom\n                                                    }, encrage.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 41\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Probl\\xe9matique associ\\xe9e\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                            value: selectedProblematique,\n                                            onChange: (e)=>setSelectedProblematique(e.target.value),\n                                            disabled: !selectedEncrage,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Toutes les probl\\xe9matiques\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 33\n                                                }, this),\n                                                filteredProblematiques.map((prob)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: prob.id,\n                                                        children: prob.problematique\n                                                    }, prob.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 37\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: showRegularisedOnly,\n                                                onChange: (e)=>setShowRegularisedOnly(e.target.checked),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Dossiers r\\xe9gularis\\xe9s\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-2 rounded-lg shadow-sm border mb-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: casList.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total des Dossiers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: casWithGeojson.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Dossiers g\\xe9olocalis\\xe9s\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: casList.filter((c)=>c.regularisation).length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Dossiers r\\xe9gularis\\xe9s\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                lineNumber: 278,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_MapComponentV2__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                casList: casList,\n                                selectedCas: selectedCas,\n                                setSelectedCas: setSelectedCas,\n                                onViewCasDetails: handleViewCasDetails,\n                                onViewCasCartographie: handleViewCasCartographie,\n                                center: mapCenter,\n                                zoom: 10,\n                                height: \"600px\",\n                                kmlLayers: kmlLayers,\n                                onZoomToCas: handleZoomToCas,\n                                showOnlySelected: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_CasListWithZoom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    casList: casList,\n                                    onZoomToCas: handleZoomToCas,\n                                    onViewCasDetails: handleViewCasDetails,\n                                    selectedCas: selectedCas,\n                                    onSelectCas: setSelectedCas\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: selectedCas ? \"Détails du Dossier sélectionné\" : \"Sélectionnez un Dossier sur la carte\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 25\n                                    }, this),\n                                    selectedCas ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 pt-4 border-t\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: ()=>handleViewCasDetails(selectedCas.id),\n                                                    className: \"w-full\",\n                                                    children: \"Voir tous les d\\xe9tails\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: ()=>handleViewCasCartographie(selectedCas.id),\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: \"Cartographie d\\xe9taill\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 37\n                                                }, this),\n                                                (selectedCas.geojson || selectedCas.kmlData && selectedCas.kmlData.features) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    onClick: ()=>handleDownloadGeoData(selectedCas),\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: \"T\\xe9l\\xe9charger GeoJSON/KML\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-500 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cliquez sur un marqueur sur la carte pour voir les d\\xe9tails du cas.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 33\n                                            }, this),\n                                            casWithGeojson.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2 text-sm\",\n                                                children: \"Aucun Dossier g\\xe9olocalis\\xe9 trouv\\xe9 avec les filtres actuels.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n                lineNumber: 383,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\",\n        lineNumber: 277,\n        columnNumber: 9\n    }, this);\n}\n_s(CartographiePage, \"pqU2QYKxft1t1UAQ2ZcQq0Eu05U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CartographiePage;\nvar _c;\n$RefreshReg$(_c, \"CartographiePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/cartographie/page.tsx\n"));

/***/ })

});