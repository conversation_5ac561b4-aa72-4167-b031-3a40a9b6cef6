"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "../components/Button";
import { Input } from "../components/Input";
import { Table } from "../components/Table";
import { Modal } from "../components/Modal";
import { FormError } from "../components/FormError";
import { fetchApi, apiClient } from "@/lib/api-client"; // apiClient est utilisé pour GET

// Définir les types pour une meilleure clarté
interface Encrage {
  id: string;
  nom: string;
}

interface Problematique {
  id: string;
  problematique: string;
  encrageId: string;
  encrage: Encrage; // Pour l'affichage dans le tableau
  cas: any[]; // Supposons que 'cas' est un tableau pour le comptage
}

interface UserSession { // Type pour la session utilisateur
  id: string;
  role: string;
  wilayaId?: number;
  // autres champs si nécessaire
}

export default function ProblematiquesPage() { // Suppression de 'async'
  // États pour les données
  const [problematiques, setProblematiques] = useState<Problematique[]>([]);
  const [encrages, setEncrages] = useState<Encrage[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);

  // États pour l'interface utilisateur et le formulaire
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentProblematique, setCurrentProblematique] = useState<Problematique | null>(null);
  const [formData, setFormData] = useState({
    problematique: "",
    encrageId: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const isAdmin = userRole === "ADMIN";

  const loadProblematiques = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await apiClient.get<Problematique[]>("/api/problematiques");
      setProblematiques(data);
    } catch (err) {
      setError((err as Error).message || "Erreur lors du chargement des problématiques.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadEncrages = useCallback(async () => {
    try {
      // Assurez-vous d'avoir un endpoint API pour les encrages
      const data = await apiClient.get<Encrage[]>("/api/encrages");
      setEncrages(data);
    } catch (err) {
      console.error("Erreur lors du chargement des encrages:", err);
      // Gérer l'erreur d'encrages si nécessaire, par exemple avec setError
    }
  }, []);

  const loadUserSession = useCallback(async () => {
    try {
      // Assurez-vous d'avoir un endpoint API pour obtenir la session/rôle de l'utilisateur
      // Par exemple : /api/auth/session
      const sessionData = await apiClient.get<UserSession>("/api/auth/session");
      setUserRole(sessionData.role);
    } catch (err) {
      console.error("Erreur lors du chargement de la session utilisateur:", err);
      setUserRole(null); // Ou gérer l'erreur autrement
    }
  }, []);

  useEffect(() => {
    loadUserSession(); // Charger la session en premier pour déterminer isAdmin
    loadProblematiques();
    loadEncrages();
  }, [loadUserSession, loadProblematiques, loadEncrages]);


  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
        const payload = {
            problematique: formData.problematique,
            encrageId: formData.encrageId,
        };

        if (isEditing && currentProblematique) {
            await fetchApi( // Utilisation de fetchApi comme dans votre code original pour PUT/POST
                `/api/problematiques/${currentProblematique.id}`,
                {
                    method: "PUT",
                    body: payload,
                }
            );
        } else {
            await fetchApi("/api/problematiques", {
                method: "POST",
                body: payload,
            });
        }

        setIsModalOpen(false);
        loadProblematiques();
    } catch (error) {
        setError((error as Error).message);
    } finally {
        setIsLoading(false);
    }
  }

  async function handleDelete(problematiqueToDelete: Problematique) {
    if (
        !confirm(
            `Êtes-vous sûr de vouloir supprimer la problématique : "${problematiqueToDelete.problematique}" ?`
        )
    )
        return;

    try {
        await fetchApi(`/api/problematiques/${problematiqueToDelete.id}`, {
            method: "DELETE",
        });
        loadProblematiques();
    } catch (error) {
        setError((error as Error).message);
    }
  }

  function handleEdit(problematiqueToEdit: Problematique) {
    setCurrentProblematique(problematiqueToEdit);
    setFormData({
        problematique: problematiqueToEdit.problematique,
        encrageId: problematiqueToEdit.encrageId,
    });
    setIsEditing(true);
    setIsModalOpen(true);
  }

  function handleAdd() {
    setCurrentProblematique(null);
    setFormData({
        problematique: "",
        encrageId: "",
    });
    setIsEditing(false);
    setIsModalOpen(true);
  }

  const columns = [
    {
        header: "Problématique",
        accessorKey: "problematique" as keyof Problematique,
    },
    {
        header: "Encrage",
        accessorKey: (row: Problematique) => row.encrage?.nom || 'N/A', // Accès sécurisé
    },
    {
        header: "Cas",
        accessorKey: (row: Problematique) => `${row.cas?.length || 0} cas`, // Accès sécurisé
    },
  ];

  // Le JSX principal du composant
  return (
    <div className="  mx-auto px-4 py-8">
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
            <h1 className="text-2xl font-semibold text-foreground">
                Problématiques
            </h1>
            {/* Afficher le bouton Ajouter uniquement si l'utilisateur est admin */}
            {isAdmin && (
                <Button onClick={handleAdd} title="Ajouter une problématique">
                    {/* Icône Ajouter */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                </Button>
            )}
        </div>

        {error && <FormError message={error} />}

        <Table
            data={problematiques}
            columns={columns}
            // Afficher les actions (Modifier/Supprimer) uniquement si l'utilisateur est admin
            actions={isAdmin ? (row: Problematique) => (
                <div className="flex justify-center items-center space-x-1 sm:space-x-2">
                    <button
                        onClick={() => handleEdit(row)}
                        title="Modifier la problématique"
                        className="p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150"
                    >
                        {/* Icône Modifier */}
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                        </svg>
                    </button>
                    <button
                        onClick={() => handleDelete(row)}
                        title="Supprimer la problématique"
                        className="p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150"
                    >
                        {/* Icône Supprimer */}
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                        </svg>
                    </button>
                </div>
            ) : undefined}
        />

        <Modal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            title={
                isEditing
                    ? "Modifier la problématique"
                    : "Ajouter une problématique"
            }
        >
            <form onSubmit={handleSubmit} className="space-y-4">
                <Input
                    id="problematique"
                    label="Problématique"
                    value={formData.problematique}
                    onChange={(e) =>
                        setFormData((prev) => ({
                            ...prev,
                            problematique: e.target.value,
                        }))
                    }
                    required
                />
                <div>
                    <label
                        htmlFor="encrageId"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Encrage
                    </label>
                    <select
                        id="encrageId"
                        value={formData.encrageId}
                        onChange={(e) =>
                            setFormData((prev) => ({
                                ...prev,
                                encrageId: e.target.value,
                            }))
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        required
                    >
                        <option value="">Sélectionner un encrage</option>
                        {encrages.map((encrage) => (
                            <option key={encrage.id} value={encrage.id}>
                                {encrage.nom}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="flex justify-end space-x-2">
                    <Button
                        variant="outline"
                        onClick={() => setIsModalOpen(false)}
                        type="button"
                    >
                        Annuler
                    </Button>
                    <Button type="submit" isLoading={isLoading}>
                        {isEditing ? "Modifier" : "Ajouter"}
                    </Button>
                </div>
            </form>
        </Modal>
    </div>
  );
}
