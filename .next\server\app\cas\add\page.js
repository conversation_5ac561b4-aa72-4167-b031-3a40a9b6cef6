(()=>{var e={};e.id=1404,e.ids=[1404],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9171:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(60687),s=r(82348),o=r(87056);function i({children:e,className:t,variant:r="primary",size:i="default",isLoading:l=!1,disabled:a,...u}){return(0,n.jsx)("button",{className:(0,s.QP)("rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/90 focus:ring-secondary/50",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-primary/50",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500/50"}[r],{default:"px-4 py-2",sm:"px-3 py-1.5 text-sm",icon:"p-2"}[i],t),disabled:l||a,...u,children:l?(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)(o.k,{})}):e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15463:(e,t,r)=>{"use strict";r.d(t,{l:()=>s});var n=r(60687);r(43210);let s=({label:e,id:t,name:r,value:s,onChange:o,required:i,multiple:l,children:a,className:u,error:d,...c})=>(0,n.jsxs)("div",{children:[e&&(0,n.jsxs)("label",{htmlFor:t||r,className:"block text-sm font-medium text-gray-700 mb-1",children:[e," ",i&&(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("select",{id:t||r,name:r,value:s,onChange:o,required:i,multiple:l,className:`block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md shadow-sm ${d?"border-red-500 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${u||""}`,...c,children:a}),d&&(0,n.jsx)("p",{className:"mt-2 text-sm text-red-600",children:d})]})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30036:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var n=r(49587),s=r.n(n)},33873:e=>{"use strict";e.exports=require("path")},44695:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\add\\page.tsx","default")},49587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(14985)._(r(64963));function s(e,t){var r;let s={};"function"==typeof e&&(s.loader=e);let o={...s,...t};return(0,n.default)({...o,modules:null==(r=o.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52643:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(60687),s=r(43210),o=r(82348);let i=(0,s.forwardRef)(({className:e,label:t,error:r,id:s,...i},l)=>(0,n.jsxs)("div",{className:"space-y-2",children:[t&&(0,n.jsx)("label",{htmlFor:s,className:"block text-sm font-medium text-foreground",children:t}),(0,n.jsx)("input",{id:s,ref:l,className:(0,o.QP)("block w-full rounded-md border border-input px-3 py-2 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm",r&&"border-destructive focus:border-destructive focus:ring-destructive/50",e),...i}),r&&(0,n.jsx)("p",{className:"mt-1 text-sm text-destructive",children:r})]}));i.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},56780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let n=r(81208);function s(e){let{reason:t,children:r}=e;throw Object.defineProperty(new n.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},57629:(e,t,r)=>{Promise.resolve().then(r.bind(r,44695))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return l}});let n=r(60687),s=r(51215),o=r(29294),i=r(19587);function l(e){let{moduleIds:t}=e,r=o.workAsyncStorage.getStore();if(void 0===r)return null;let l=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;l.push(...t)}}return 0===l.length?null:(0,n.jsx)(n.Fragment,{children:l.map(e=>{let t=r.assetPrefix+"/_next/"+(0,i.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,s.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(60687),s=r(43210),o=r(56780),i=r(64777);function l(e){return{default:e&&"default"in e?e.default:e}}let a={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},u=function(e){let t={...a,...e},r=(0,s.lazy)(()=>t.loader().then(l)),u=t.loading;function d(e){let l=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,a=!t.ssr||!!t.loading,d=a?s.Suspense:s.Fragment,c=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.PreloadChunks,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(d,{...a?{fallback:l}:{},children:c})}return d.displayName="LoadableComponent",d}},69266:(e,t,r)=>{"use strict";async function n(e,t={}){let{method:r="GET",body:s}=t;console.log(`Making ${r} request to ${e}`),s&&console.log("Request body:",s);let o=await fetch(e,{method:r,headers:{"Content-Type":"application/json",...t.headers},credentials:t.credentials||"include",body:s?JSON.stringify(s):void 0});if(console.log("Response status:",o.status),!o.ok){let e=`HTTP error! status: ${o.status}`,t=null;try{let r=await o.text();if(console.log("Error response text:",r),r)try{t=JSON.parse(r),e=t?.error||t?.message||r}catch(t){e=r||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===o.status)throw Error("Authentication required. Please log in again.");if(403===o.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===o.status)throw Error("Resource not found.");else if(o.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===o.status)return null;let i=await o.json();return console.log("Response data:",i),i}r.d(t,{Zq:()=>n,uE:()=>s});let s={get:(e,t)=>n(e,{...t,method:"GET"}),post:(e,t,r)=>n(e,{...r,method:"POST",body:t}),put:(e,t,r)=>n(e,{...r,method:"PUT",body:t}),patch:(e,t,r)=>n(e,{...r,method:"PATCH",body:t}),delete:(e,t,r)=>n(e,{...r,method:"DELETE",body:t})}},78089:(e,t,r)=>{"use strict";r.d(t,{f:()=>i});var n=r(60687),s=r(43210),o=r(82348);let i=(0,s.forwardRef)(({className:e,label:t,error:r,id:s,rows:i=4,...l},a)=>(0,n.jsxs)("div",{className:"space-y-2",children:[t&&(0,n.jsx)("label",{htmlFor:s,className:"block text-sm font-medium text-foreground",children:t}),(0,n.jsx)("textarea",{id:s,ref:a,rows:i,className:(0,o.QP)("block w-full rounded-md border border-input px-3 py-2 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm resize-y",r&&"border-destructive focus:border-destructive focus:ring-destructive/50",e),...l}),r&&(0,n.jsx)("p",{className:"mt-1 text-sm text-destructive",children:r})]}));i.displayName="TextArea"},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},82407:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>u});var n=r(65239),s=r(48088),o=r(88170),i=r.n(o),l=r(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let u={children:["",{children:["cas",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44695)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\add\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,92392)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\add\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cas/add/page",pathname:"/cas/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},85245:(e,t,r)=>{Promise.resolve().then(r.bind(r,85526))},85526:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>K});var n,s=r(60687),o=r(43210),i=r(16189),l=r(69266),a=r(9171),u=r(52643),d=r(78089),c=r(15463),m=r(89679),p=r(87056),f=r(96330),h=r(30036);function g(e,t){return Array.from(e.getElementsByTagName(t))}function x(e){return"#"===e[0]?e:`#${e}`}function b(e){return e?.normalize(),e?.textContent||""}function y(e,t,r){let n=e.getElementsByTagName(t),s=n.length?n[0]:null;return s&&r&&r(s),s}function v(e,t,r){let n={};if(!e)return n;let s=e.getElementsByTagName(t),o=s.length?s[0]:null;return o&&r?r(o,n):n}function N(e,t,r){let n=b(y(e,t));return n&&r&&r(n)||{}}function j(e,t,r){let n=Number.parseFloat(b(y(e,t)));if(!Number.isNaN(n))return n&&r&&r(n)||{}}function P(e,t,r){let n=Number.parseFloat(b(y(e,t)));if(!Number.isNaN(n))return r&&r(n),n}function E(e,t){let r={};for(let n of t)N(e,n,e=>{r[n]=e});return r}function O(e){return e?.nodeType===1}function S(e,t){let r=[];for(let[n,s]of t){let t=y(e,n);if(!t){let r=e.getElementsByTagNameNS("http://www.garmin.com/xmlschemas/ActivityExtension/v2",n);r.length&&(t=r[0])}let o=Number.parseFloat(b(t));Number.isNaN(o)||r.push([s,o])}return r}function A(e,t){let r={},n="stroke"===t||"fill"===t?t:`${t}-color`;return"#"===e[0]&&(e=e.substring(1)),6===e.length||3===e.length?r[n]=`#${e}`:8===e.length&&(r[`${t}-opacity`]=Number.parseInt(e.substring(0,2),16)/255,r[n]=`#${e.substring(6,8)}${e.substring(4,6)}${e.substring(2,4)}`),r}function _(e,t,r){let n={};return P(e,t,e=>{n[r]=e}),n}function T(e,t){return v(e,"color",e=>A(b(e),t))}function k(e){return v(e,"Icon",(e,t)=>(N(e,"href",e=>{t.icon=e}),t))}function I(e){return Object.assign({},v(e,"PolyStyle",(e,t)=>Object.assign(t,v(e,"color",e=>A(b(e),"fill")),N(e,"fill",e=>{if("0"===e)return{"fill-opacity":0}}),N(e,"outline",e=>{if("0"===e)return{"stroke-opacity":0}}))),v(e,"LineStyle",e=>Object.assign(T(e,"stroke"),_(e,"width","stroke-width"))),v(e,"LabelStyle",e=>Object.assign(T(e,"label"),_(e,"scale","label-scale"))),v(e,"IconStyle",e=>Object.assign(T(e,"icon"),_(e,"scale","icon-scale"),_(e,"heading","icon-heading"),v(e,"hotSpot",e=>{let t=Number.parseFloat(e.getAttribute("x")||""),r=Number.parseFloat(e.getAttribute("y")||""),n=e.getAttribute("xunits")||"",s=e.getAttribute("yunits")||"";return Number.isNaN(t)||Number.isNaN(r)?{}:{"icon-offset":[t,r],"icon-offset-units":[n,s]}}),k(e))))}let L=/\s*/g,R=/^\s*|\s*$/g,w=/\s+/;function C(e){return e.replace(L,"").split(",").map(Number.parseFloat).filter(e=>!Number.isNaN(e)).slice(0,3)}function F(e){return e.replace(R,"").split(w).map(C).filter(e=>e.length>=2)}function M(e){if(0===e.length)return e;let t=e[0],r=e[e.length-1],n=!0;for(let e=0;e<Math.max(t.length,r.length);e++)if(t[e]!==r[e]){n=!1;break}return n?e:e.concat([e[0]])}function q(e){return b(y(e,"coordinates"))}let X=e=>Number(e),D={string:e=>e,int:X,uint:X,short:X,ushort:X,float:X,double:X,bool:e=>!!e};function G(e,t){return v(e,"ExtendedData",(e,r)=>{for(let t of g(e,"Data"))r[t.getAttribute("name")||""]=b(y(t,"value"));for(let n of g(e,"SimpleData")){let e=n.getAttribute("name")||"",s=t[e]||D.string;r[e]=s(b(n))}return r})}function U(e){let t=y(e,"description");for(let e of Array.from(t?.childNodes||[]))if(4===e.nodeType)return{description:{"@type":"html",value:b(e)}};return{}}function z(e){return v(e,"TimeSpan",e=>({timespan:{begin:b(y(e,"begin")),end:b(y(e,"end"))}}))}function B(e){return v(e,"TimeStamp",e=>({timestamp:b(y(e,"when"))}))}function $(e,t){return N(e,"styleUrl",e=>t[e=x(e)]?Object.assign({styleUrl:e},t[e]):{styleUrl:e})}!function(e){e.ABSOLUTE="absolute",e.RELATIVE_TO_GROUND="relativeToGround",e.CLAMP_TO_GROUND="clampToGround",e.CLAMP_TO_SEAFLOOR="clampToSeaFloor",e.RELATIVE_TO_SEAFLOOR="relativeToSeaFloor"}(n||(n={}));let V=Math.PI/180,Q=(0,h.default)(async()=>{},{loadableGenerated:{modules:["app\\components\\GeoJsonDropzone.tsx -> react-leaflet"]},ssr:!1}),H=(0,h.default)(async()=>{},{loadableGenerated:{modules:["app\\components\\GeoJsonDropzone.tsx -> react-leaflet"]},ssr:!1}),Y=(0,h.default)(async()=>{},{loadableGenerated:{modules:["app\\components\\GeoJsonDropzone.tsx -> react-leaflet"]},ssr:!1}),J=({value:e,onChange:t})=>{let[r,i]=(0,o.useState)(null),[l,a]=(0,o.useState)(null),u=(0,o.useRef)(null),d=async e=>{a(null);try{let r=await e.text(),s=new DOMParser().parseFromString(r,"text/xml"),o=function(e,t={skipNullGeometry:!1}){return{type:"FeatureCollection",features:Array.from(function*(e,t={skipNullGeometry:!1}){let r=function(e){let t={};for(let r of g(e,"Style"))t[function(e){let t=e.getAttribute("id"),r=e.parentNode;return!t&&O(r)&&"CascadingStyle"===r.localName&&(t=r.getAttribute("kml:id")||r.getAttribute("id")),x(t||"")}(r)]=I(r);for(let r of g(e,"StyleMap")){let e=x(r.getAttribute("id")||"");N(r,"styleUrl",r=>{t[r=x(r)]&&(t[e]=t[r])})}return t}(e),s=function(e){let t={};for(let r of g(e,"SimpleField"))t[r.getAttribute("name")||""]=D[r.getAttribute("type")||""]||D.string;return t}(e);for(let n of g(e,"Placemark")){let e=function(e,t,r,n){let{coordTimes:s,geometries:o}=function e(t){let r=[],n=[];for(let s=0;s<t.childNodes.length;s++){let o=t.childNodes.item(s);if(O(o))switch(o.tagName){case"MultiGeometry":case"MultiTrack":case"gx:MultiTrack":{let t=e(o);r=r.concat(t.geometries),n=n.concat(t.coordTimes);break}case"Point":{let e=C(q(o));e.length>=2&&r.push({type:"Point",coordinates:e});break}case"LinearRing":case"LineString":{let e=F(q(o));e.length>=2&&r.push({type:"LineString",coordinates:e});break}case"Polygon":{let e=[];for(let t of g(o,"LinearRing")){let r=M(F(q(t)));r.length>=4&&e.push(r)}e.length&&r.push({type:"Polygon",coordinates:e});break}case"Track":case"gx:Track":{let e=function(e){let t=g(e,"coord");0===t.length&&(t=Array.from(e.getElementsByTagNameNS("*","coord")));let r=t.map(e=>b(e).split(" ").map(Number.parseFloat));return 0===r.length?null:{geometry:r.length>2?{type:"LineString",coordinates:r}:{type:"Point",coordinates:r[0]},times:g(e,"when").map(e=>b(e))}}(o);if(!e)break;let{times:t,geometry:s}=e;r.push(s),t.length&&n.push(t)}}}return{geometries:r,coordTimes:n}}(e),i=0===o.length?null:1===o.length?o[0]:{type:"GeometryCollection",geometries:o};if(!i&&n.skipNullGeometry)return null;let l={type:"Feature",geometry:i,properties:Object.assign(E(e,["name","address","visibility","open","phoneNumber","description"]),U(e),$(e,t),I(e),G(e,r),z(e),B(e),s.length?{coordinateProperties:{times:1===s.length?s[0]:s}}:{})};l.properties?.visibility!==void 0&&(l.properties.visibility="0"!==l.properties.visibility);let a=e.getAttribute("id");return null!==a&&""!==a&&(l.id=a),l}(n,r,s,t);e&&(yield e)}for(let n of g(e,"GroundOverlay")){let e=function(e,t,r,n){let s=y(e,"gx:LatLonQuad")?{geometry:{type:"Polygon",coordinates:[M(F(q(e)))]}}:function(e){let t=y(e,"LatLonBox");if(t){let e=P(t,"north"),r=P(t,"west"),n=P(t,"east"),s=P(t,"south"),o=P(t,"rotation");if("number"==typeof e&&"number"==typeof s&&"number"==typeof r&&"number"==typeof n){let t=[r,s,n,e],i=[[[r,e],[n,e],[n,s],[r,s],[r,e]]];return"number"==typeof o&&(i=function(e,t,r){let n=[(e[0]+e[2])/2,(e[1]+e[3])/2];return[t[0].map(e=>{let t=e[1]-n[1],s=e[0]-n[0],o=Math.sqrt(t**2+s**2),i=Math.atan2(t,s)+r*V;return[n[0]+Math.cos(i)*o,n[1]+Math.sin(i)*o]})]}(t,i,o)),{bbox:t,geometry:{type:"Polygon",coordinates:i}}}}return null}(e),o=s?.geometry||null;if(!o&&n.skipNullGeometry)return null;let i={type:"Feature",geometry:o,properties:Object.assign({"@geometry-type":"groundoverlay"},E(e,["name","address","visibility","open","phoneNumber","description"]),U(e),$(e,t),I(e),k(e),G(e,r),z(e),B(e))};s?.bbox&&(i.bbox=s.bbox),i.properties?.visibility!==void 0&&(i.properties.visibility="0"!==i.properties.visibility);let l=e.getAttribute("id");return null!==l&&""!==l&&(i.id=l),i}(n,r,s,t);e&&(yield e)}for(let o of g(e,"NetworkLink")){let e=function(e,t,r,s){let o=function(e){let t=y(e,"Region");return t?{coordinateBox:function(e){let t=y(e,"LatLonAltBox");if(t){let e=P(t,"north"),r=P(t,"west"),s=P(t,"east"),o=P(t,"south");if(function(e){switch(e?.textContent){case n.ABSOLUTE:return n.ABSOLUTE;case n.CLAMP_TO_GROUND:return n.CLAMP_TO_GROUND;case n.CLAMP_TO_SEAFLOOR:return n.CLAMP_TO_SEAFLOOR;case n.RELATIVE_TO_GROUND:return n.RELATIVE_TO_GROUND;case n.RELATIVE_TO_SEAFLOOR:return n.RELATIVE_TO_SEAFLOOR}return null}(y(t,"altitudeMode")||y(t,"gx:altitudeMode"))&&console.debug("Encountered an unsupported feature of KML for togeojson: please contact developers for support of altitude mode."),"number"==typeof e&&"number"==typeof o&&"number"==typeof r&&"number"==typeof s)return{bbox:[r,o,s,e],geometry:{type:"Polygon",coordinates:[[[r,e],[s,e],[s,o],[r,o],[r,e]]]}}}return null}(t),lod:function(e){let t=y(e,"Lod");return t?[P(t,"minLodPixels")??-1,P(t,"maxLodPixels")??-1,P(t,"minFadeExtent")??null,P(t,"maxFadeExtent")??null]:null}(e)}:null}(e),i=o?.coordinateBox?.geometry||null;if(!i&&s.skipNullGeometry)return null;let l={type:"Feature",geometry:i,properties:Object.assign({"@geometry-type":"networklink"},E(e,["name","address","visibility","open","phoneNumber","styleUrl","refreshVisibility","flyToView","description"]),U(e),$(e,t),I(e),k(e),G(e,r),z(e),B(e),function(e){let t=y(e,"Link");return t?E(t,["href","refreshMode","refreshInterval","viewRefreshMode","viewRefreshTime","viewBoundScale","viewFormat","httpQuery"]):{}}(e),o?.lod?{lod:o.lod}:{})};o?.coordinateBox?.bbox&&(l.bbox=o.coordinateBox.bbox),l.properties?.visibility!==void 0&&(l.properties.visibility="0"!==l.properties.visibility);let a=e.getAttribute("id");return null!==a&&""!==a&&(l.id=a),l}(o,r,s,t);e&&(yield e)}}(e,t))}}(s);i(o),t(o)}catch(e){a("Erreur lors de la conversion du fichier KML.")}};return(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block font-medium mb-1",children:"G\xe9om\xe9trie (KML, optionnel)"}),(0,s.jsxs)("div",{className:"border-2 border-dashed rounded p-4 text-center cursor-pointer hover:bg-gray-50",onDrop:e=>{e.preventDefault(),e.dataTransfer.files&&e.dataTransfer.files[0]&&d(e.dataTransfer.files[0])},onDragOver:e=>e.preventDefault(),onClick:()=>u.current?.click(),children:[r?(0,s.jsx)("span",{className:"text-green-600",children:"Fichier charg\xe9. Aper\xe7u ci-dessous."}):(0,s.jsx)("span",{children:"D\xe9posez un fichier KML ici ou cliquez pour s\xe9lectionner."}),(0,s.jsx)("input",{type:"file",accept:".kml",ref:u,className:"hidden",onChange:e=>{e.target.files&&e.target.files[0]&&d(e.target.files[0])}})]}),l&&(0,s.jsx)("div",{className:"text-red-600 mt-2",children:l}),r&&(0,s.jsx)("div",{className:"mt-4 h-64",children:(0,s.jsxs)(Q,{center:[33.5,-7.5],zoom:7,style:{height:"100%",width:"100%"},scrollWheelZoom:!1,children:[(0,s.jsx)(H,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),(0,s.jsx)(Y,{data:r})]})}),r&&(0,s.jsx)("button",{className:"mt-2 text-sm text-red-500 underline",type:"button",onClick:()=>{i(null),t(null)},children:"Supprimer la g\xe9om\xe9trie"}),r&&(0,s.jsx)("section",{children:(0,s.jsxs)(Q,{center:[36.75,3.06],zoom:8,style:{height:"300px",width:"100%"},children:[(0,s.jsx)(H,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),(0,s.jsx)(Y,{data:r})]})})]})};function K(){let e=(0,i.useRouter)(),[t,r]=(0,o.useState)({nom:"",genre:"",nif:"",nin:"",superficie:"",observation:"",problematiqueId:"",communeIds:[],date_depot:""}),[n,h]=(0,o.useState)([]),[g,x]=(0,o.useState)([]),[b,y]=(0,o.useState)(!1),[v,N]=(0,o.useState)(null),[j,P]=(0,o.useState)(null),E=e=>{let{name:t,value:n}=e.target,s=n;"nif"===t?s=function(e){let t=e.replace(/\D/g,"").substring(0,15);if(!t)return"";let r=[];for(let e=0;e<t.length;e+=3)r.push(t.substring(e,e+3));return r.join(".").substring(0,19)}(n):"nin"===t&&(s=function(e){let t=e.replace(/\D/g,"").substring(0,20);if(!t)return"";let r=[],n=0;for(let e=0;e<6&&!(n>=t.length);e++)r.push(t.substring(n,Math.min(n+3,t.length))),n+=3;return n<t.length&&r.push(t.substring(n,Math.min(n+2,t.length))),r.join(".").substring(0,26)}(n)),r(e=>{let r={...e,[t]:s};return"genre"===t&&(s===f.TypePersonne.PERSONNE_MORALE?r.nin="":s===f.TypePersonne.PERSONNE_PHYSIQUE&&(r.nif="")),r})},O=async r=>{if(console.log("handleSubmit: Fonction appel\xe9e !"),r.preventDefault(),N(null),y(!0),!t.genre){console.log("handleSubmit: Erreur - Genre non s\xe9lectionn\xe9."),N("Veuillez s\xe9lectionner un type (Personne Physique ou Morale)."),y(!1);return}if(!t.communeIds||0===t.communeIds.length){console.log("handleSubmit: Erreur - Aucune commune s\xe9lectionn\xe9e."),N("Veuillez s\xe9lectionner au moins une commune."),y(!1);return}if(console.log("handleSubmit: formData.communeIds avant envoi:",t.communeIds),t.genre===f.TypePersonne.PERSONNE_MORALE&&t.nif&&!/^\d{3}(\.\d{3}){4}$/.test(t.nif)){console.log("handleSubmit: Erreur - Format NIF invalide."),N("Format NIF invalide. Attendu: XXX.XXX.XXX.XXX.XXX"),y(!1);return}if(t.genre===f.TypePersonne.PERSONNE_PHYSIQUE&&t.nin&&!/^\d{3}(\.\d{3}){5}\.\d{2}$/.test(t.nin)){console.log("handleSubmit: Erreur - Format NIN invalide."),N("Format NIN invalide. Attendu: XXX.XXX.XXX.XXX.XXX.XXX.XX"),y(!1);return}let n={nom:t.nom,genre:t.genre,superficie:parseFloat(t.superficie),observation:t.observation||null,problematiqueId:t.problematiqueId,communeIds:t.communeIds,date_depot:t.date_depot?new Date(t.date_depot).toISOString():null,geojson:j||null};t.genre===f.TypePersonne.PERSONNE_MORALE?n.nif=t.nif||null:t.genre===f.TypePersonne.PERSONNE_PHYSIQUE&&(n.nin=t.nin||null),console.log("handleSubmit: Donn\xe9es envoy\xe9es \xe0 l'API:",n);try{await l.uE.post("/api/cas",n),e.push("/cas")}catch(t){console.error("handleSubmit: Erreur API re\xe7ue.");let e="Erreur lors de la cr\xe9ation du cas.";t.response?.data?.details&&Array.isArray(t.response.data.details)?(console.error("D\xe9tails de l'erreur de validation API:",t.response.data.details),e=t.response.data.details.map(e=>`Champ '${e.path.join(".")}': ${e.message}`).join("; ")):t.response?.data?.message?e=t.response.data.message:t.response?.data?.error&&(e=t.response.data.error),N(e),console.error("Erreur API compl\xe8te:",t.response?.data||t)}y(!1)};return b&&0===n.length?(0,s.jsx)(p.k,{}):(0,s.jsxs)("div",{className:" mx-auto px-4 py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8 text-gray-800",children:"Ajouter un nouveau Cas"}),(0,s.jsxs)("form",{onSubmit:O,className:"space-y-6 bg-white p-8 shadow-xl rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nom",className:"block text-sm font-medium text-gray-700",children:"Nom du Cas"}),(0,s.jsx)(u.p,{type:"text",name:"nom",id:"nom",value:t.nom,onChange:E,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"genre",className:"block text-sm font-medium text-gray-700",children:"Type"}),(0,s.jsxs)(c.l,{name:"genre",id:"genre",value:t.genre,onChange:E,required:!0,children:[(0,s.jsx)("option",{value:"",disabled:!0,children:"S\xe9lectionner un type"}),(0,s.jsx)("option",{value:f.TypePersonne.PERSONNE_PHYSIQUE,children:"Personne Physique"}),(0,s.jsx)("option",{value:f.TypePersonne.PERSONNE_MORALE,children:"Personne Morale"})]})]}),t.genre===f.TypePersonne.PERSONNE_MORALE&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nif",className:"block text-sm font-medium text-gray-700",children:"NIF (Num\xe9ro d'Identification Fiscale)"}),(0,s.jsx)(u.p,{type:"text",name:"nif",id:"nif",value:t.nif||"",onChange:E,placeholder:"Ex: 123.456.789.012.345",maxLength:19}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Saisissez les 15 chiffres, les points seront ajout\xe9s automatiquement."})]}),t.genre===f.TypePersonne.PERSONNE_PHYSIQUE&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nin",className:"block text-sm font-medium text-gray-700",children:"NIN (Num\xe9ro d'Identification National)"}),(0,s.jsx)(u.p,{type:"text",name:"nin",id:"nin",value:t.nin||"",onChange:E,placeholder:"Ex: 123.456.789.012.345.678.90",maxLength:26}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Saisissez les 20 chiffres, les points seront ajout\xe9s automatiquement."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"superficie",className:"block text-sm font-medium text-gray-700",children:"Superficie (Ha)"}),(0,s.jsx)(u.p,{type:"number",name:"superficie",id:"superficie",value:t.superficie,onChange:E,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"date_depot",className:"block text-sm font-medium text-gray-700",children:"Date de d\xe9p\xf4t du dossier"})," ",(0,s.jsx)(u.p,{type:"date",name:"date_depot",id:"date_depot",value:t.date_depot,onChange:E})," "]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"problematiqueId",className:"block text-sm font-medium text-gray-700",children:"Probl\xe9matique"}),(0,s.jsxs)(c.l,{name:"problematiqueId",id:"problematiqueId",value:t.problematiqueId,onChange:E,required:!0,children:[(0,s.jsx)("option",{value:"",disabled:!0,children:"S\xe9lectionner une probl\xe9matique"}),n.map(e=>(0,s.jsx)("option",{value:e.id,children:e.problematique},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"communeIds",className:"block text-sm font-medium text-gray-700",children:"Commune(s)"}),(0,s.jsx)(c.l,{name:"communeIds",id:"communeIds",value:t.communeIds,onChange:e=>{let t=Array.from(e.target.selectedOptions,e=>e.value);r(e=>({...e,communeIds:t}))},multiple:!0,required:!0,children:g.map(e=>(0,s.jsx)("option",{value:e.id.toString(),children:e.nom},e.id))}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Maintenez Ctrl (ou Cmd sur Mac) pour s\xe9lectionner plusieurs communes."})]}),(0,s.jsx)(J,{value:j,onChange:P}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"observation",className:"block text-sm font-medium text-gray-700",children:"Observation"}),(0,s.jsx)(d.f,{name:"observation",id:"observation",value:t.observation,onChange:E,rows:4})]}),v&&(0,s.jsx)(m.j,{message:v}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(a.$,{type:"submit",isLoading:b,disabled:b,children:b?"Cr\xe9ation en cours...":"Cr\xe9er le Cas"})})]})]})}},87056:(e,t,r)=>{"use strict";r.d(t,{k:()=>o});var n=r(60687),s=r(82348);function o({className:e,color:t="light",size:r="md"}){return(0,n.jsx)("div",{className:(0,s.QP)("border-2 rounded-full animate-spin",{light:"border-white/80 border-t-transparent",dark:"border-gray-700 border-t-transparent"}[t],{sm:"w-4 h-4",md:"w-5 h-5",lg:"w-8 h-8"}[r],e)})}},89679:(e,t,r)=>{"use strict";r.d(t,{j:()=>s});var n=r(60687);function s({message:e}){return e?(0,n.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsx)("p",{className:"text-sm text-red-700",children:e})})]})}):null}},92392:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(37413);function s({children:e}){return(0,n.jsx)("div",{className:"w-full px-[5px] py-2 max-w-screen-xl mx-auto font-inter text-[0.95rem] text-gray-800",style:{fontFamily:"Inter, Segoe UI, Arial, sans-serif",lineHeight:1.5},children:e})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,3903,5262,2348,2797],()=>r(82407));module.exports=n})();