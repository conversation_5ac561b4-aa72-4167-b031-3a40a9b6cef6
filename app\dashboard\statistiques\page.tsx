"use client";

import { useEffect, useState } from "react";
import { fetchApi } from "@/lib/api-client";
import { <PERSON>, Doughnut } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
} from "chart.js";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { UserRoleBadge } from "@/app/components/RoleBasedAccess";
import { usePermissions } from "@/lib/hooks/usePermissions";
import { useRegisterDataRefresh } from "@/app/contexts/DataRefreshContext";

// Mapping des wilayaId vers les noms des DSA
const wilayaNames: { [key: number]: string } = {
    1: "Adrar",
    2: "Chlef",
    3: "Lagh<PERSON><PERSON>",
    4: "<PERSON>um El Bouaghi",
    5: "<PERSON><PERSON>",
    6: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    7: "<PERSON><PERSON><PERSON>",
    8: "<PERSON><PERSON><PERSON><PERSON>",
    9: "<PERSON><PERSON><PERSON>",
    10: "<PERSON><PERSON><PERSON>",
    11: "<PERSON><PERSON><PERSON><PERSON>",
    12: "<PERSON><PERSON><PERSON><PERSON>",
    13: "T<PERSON><PERSON>n",
    14: "Tiar<PERSON>",
    15: "Tizi Ouz<PERSON>",
    16: "<PERSON><PERSON>",
    17: "<PERSON><PERSON><PERSON><PERSON>",
    18: "<PERSON><PERSON><PERSON>",
    19: "<PERSON><PERSON><PERSON><PERSON>",
    20: "<PERSON>ï<PERSON>",
    21: "<PERSON><PERSON><PERSON>",
    22: "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>",
    23: "<PERSON><PERSON>",
    24: "<PERSON><PERSON><PERSON>",
    25: "<PERSON>",
    26: "<PERSON><PERSON>d<PERSON><PERSON>",
    27: "<PERSON><PERSON><PERSON>",
    28: "<PERSON>'<PERSON><PERSON>",
    29: "<PERSON><PERSON><PERSON>",
    30: "<PERSON><PERSON><PERSON><PERSON>",
    31: "<PERSON><PERSON>",
    32: "El Bayadh",
    33: "Illizi",
    34: "Bordj Bou Arréridj",
    35: "Boumerdès",
    36: "El Tarf",
    37: "Tindouf",
    38: "Tissemsilt",
    39: "El Oued",
    40: "Khenchela",
    41: "Souk Ahras",
    42: "Tipaza",
    43: "Mila",
    44: "Aïn Defla",
    45: "Naâma",
    46: "Aïn Témouchent",
    47: "Ghardaïa",
    48: "Relizane",
    49: "Timimoun",
    50: "Bordj Badji Mokhtar",
    51: "Ouled Djellal",
    52: "Béni Abbès",
    53: "In Salah",
    54: "In Guezzam",
    55: "Touggourt",
    56: "Djanet",
    57: "El M'Ghair",
    58: "El Meniaa",
};

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
);

// Types pour les données d'analyse
interface StatutAnalyse {
    statut: string;
    wilayas: Array<{
        wilayaId: number;
        dsaName: string;
        total: number;
        regularise: number;
        ajourne: number;
        rejete: number;
        nonExamine: number;
    }>;
}

interface ContrainteAnalyse {
    wilayaId: number;
    dsaName: string;
    encrages: Array<{
        encrageName: string;
        secteur: string; // Ajout du secteur
        totalCas: number;
        problematiques: Array<{
            problematiqueName: string;
            count: number;
            statuts: {
                regularise: number;
                ajourne: number;
                rejete: number;
                nonExamine: number;
            };
        }>;
    }>;
}

interface AnalyseData {
    tableauStatuts: StatutAnalyse[];
    tableauContraintes: ContrainteAnalyse[];
    chartStatuts: any;
    chartWilayas: any;
    totalCas: number;
    totalWilayas: number;
    filtreWilaya: number | null;
}

export default function StatistiquesPage() {
    const { user, isAdmin } = usePermissions();
    const [analyseData, setAnalyseData] = useState<AnalyseData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedWilaya, setSelectedWilaya] = useState<string>("");
    const [activeTab, setActiveTab] = useState<
        "statuts" | "contraintes" | "charts"
    >("statuts");

    // Determine if dropdown should be shown and set initial selectedWilaya
    const showDropdown = isAdmin || !user?.wilayaId;
    const initialWilaya =
        user?.wilayaId && !isAdmin ? user.wilayaId.toString() : "";

    // Charger les données d'analyse
    const loadAnalyse = async () => {
        try {
            setLoading(true);
            setError(null);

            const url = selectedWilaya
                ? `/api/stats/analyse-complete?wilayaId=${selectedWilaya}`
                : "/api/stats/analyse-complete";

            console.log("📊 Chargement de l'analyse depuis:", url);

            const response = await fetchApi<{
                success: boolean;
                data: AnalyseData;
                error?: string;
            }>(url);

            if (response.success && response.data) {
                setAnalyseData(response.data);
                console.log("✅ Analyse chargée:", response.data);
            } else {
                setError(
                    response.error || "Erreur lors du chargement de l'analyse"
                );
            }
        } catch (err: any) {
            console.error("Erreur lors du chargement de l'analyse:", err);
            setError(err.message || "Erreur inconnue");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadAnalyse();
    }, [selectedWilaya]);

    // Set initial selectedWilaya based on user
    useEffect(() => {
        if (user) {
            setSelectedWilaya(initialWilaya);
        }
    }, [user, initialWilaya]);

    // Enregistrer le callback de rafraîchissement pour les statistiques
    useRegisterDataRefresh("statistiques-analyse-complete", loadAnalyse, [
        selectedWilaya,
    ]);

    if (loading) {
        return (
            <div className="  mx-auto px-4 py-8">
                <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="    mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold text-red-600 mb-2">
                            Erreur
                        </h2>
                        <p className="text-gray-600 mb-4">{error}</p>
                        <button
                            onClick={loadAnalyse}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Réessayer
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!analyseData) {
        return (
            <div className="  mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold mb-2">
                            Aucune donnée
                        </h2>
                        <p className="text-gray-600 mb-4">
                            Aucune statistique à afficher pour le moment.
                        </p>
                        <button
                            onClick={loadAnalyse}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Actualiser
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="  mx-auto px-4 py-4">
            {/* En-tête avec statistiques de synthèse */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8">
                <div className="flex justify-between items-start mb-3">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Analyse Complète des Dossiers et Contraintes
                        </h1>
                        <UserRoleBadge className="mt-2" />
                    </div>
                    <div className="flex gap-4">
                        {showDropdown && (
                            <select
                                value={selectedWilaya}
                                onChange={(e) =>
                                    setSelectedWilaya(e.target.value)
                                }
                                disabled={loading}
                                className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-48 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <option value="">Toutes les DSA</option>
                                {Array.from({ length: 58 }, (_, i) => {
                                    const wilayaId = i + 1;
                                    return (
                                        <option
                                            key={wilayaId}
                                            value={wilayaId.toString()}
                                        >
                                            {wilayaNames[wilayaId] ||
                                                `DSA ${wilayaId}`}
                                        </option>
                                    );
                                })}
                            </select>
                        )}
                        <button
                            onClick={loadAnalyse}
                            disabled={loading}
                            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        >
                            {loading && <LoadingSpinner size="sm" />}
                            Actualiser
                        </button>
                    </div>
                </div>

                {/* Statistiques de synthèse */}
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="text-sm font-medium text-blue-800">
                            🔵 Total dossiers
                        </h3>
                        <p className=" text-2xl font-bold text-blue-900">
                            {analyseData.totalCas.toLocaleString()}
                        </p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="text-sm font-medium text-green-800">
                            🟢 Régularisés
                        </h3>
                        <p className="text-2xl font-bold text-green-900">
                            {(() => {
                                const totalRegularises =
                                    analyseData.tableauStatuts.reduce(
                                        (sum, statut) =>
                                            sum +
                                            statut.wilayas.reduce(
                                                (wilayaSum, wilaya) =>
                                                    wilayaSum +
                                                    wilaya.regularise,
                                                0
                                            ),
                                        0
                                    );
                                return totalRegularises.toLocaleString();
                            })()}
                        </p>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                        <h3 className="text-sm font-medium text-yellow-800">
                            🟡 Ajournés
                        </h3>
                        <p className="text-2xl font-bold text-yellow-900">
                            {(() => {
                                const totalAjournes =
                                    analyseData.tableauStatuts.reduce(
                                        (sum, statut) =>
                                            sum +
                                            statut.wilayas.reduce(
                                                (wilayaSum, wilaya) =>
                                                    wilayaSum + wilaya.ajourne,
                                                0
                                            ),
                                        0
                                    );
                                return totalAjournes.toLocaleString();
                            })()}
                        </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="text-sm font-medium text-gray-800">
                            ⚪ Non examinés
                        </h3>
                        <p className="text-2xl font-bold text-gray-900">
                            {(() => {
                                const totalNonExamines =
                                    analyseData.tableauStatuts.reduce(
                                        (sum, statut) =>
                                            sum +
                                            statut.wilayas.reduce(
                                                (wilayaSum, wilaya) =>
                                                    wilayaSum +
                                                    wilaya.nonExamine,
                                                0
                                            ),
                                        0
                                    );
                                return totalNonExamines.toLocaleString();
                            })()}
                        </p>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                        <h3 className="text-sm font-medium text-red-800">
                            🔴 Rejetés
                        </h3>
                        <p className="text-2xl font-bold text-red-900">
                            {(() => {
                                const totalRejetes =
                                    analyseData.tableauStatuts.reduce(
                                        (sum, statut) =>
                                            sum +
                                            statut.wilayas.reduce(
                                                (wilayaSum, wilaya) =>
                                                    wilayaSum + wilaya.rejete,
                                                0
                                            ),
                                        0
                                    );
                                return totalRejetes.toLocaleString();
                            })()}
                        </p>
                    </div>
                </div>
            </div>

            {/* Onglets */}
            <div className="mb-3">
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                        <button
                            onClick={() => setActiveTab("statuts")}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === "statuts"
                                    ? "border-blue-500 text-blue-600"
                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }`}
                        >
                            📊 Analyse des Dossiers par DSA
                        </button>
                        <button
                            onClick={() => setActiveTab("contraintes")}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === "contraintes"
                                    ? "border-blue-500 text-blue-600"
                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }`}
                        >
                            🔍 Analyse des Contraintes par Secteur
                        </button>
                        <button
                            onClick={() => setActiveTab("charts")}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === "charts"
                                    ? "border-blue-500 text-blue-600"
                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }`}
                        >
                            📈 Graphiques Dynamiques
                        </button>
                    </nav>
                </div>
            </div>

            {/* Contenu des onglets */}
            {activeTab === "statuts" && (
                <div className="space-y-8">
                    <div className="bg-white rounded-lg shadow-md border border-gray-200">
                        <div className="px-6 py-2">
                            <div className="overflow-x-auto xl:overflow-visible">
                                <table className="min-w-full xl:min-w-0 divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                DSA
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Total dossiers
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Régularisé
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Ajourné
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Rejeté
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Non examiné
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Taux Régularisation
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {(() => {
                                            // Fusionner toutes les données par DSA
                                            const dsaStats = new Map<
                                                number,
                                                {
                                                    dsaName: string;
                                                    total: number;
                                                    regularise: number;
                                                    ajourne: number;
                                                    rejete: number;
                                                    nonExamine: number;
                                                }
                                            >();

                                            analyseData.tableauStatuts.forEach(
                                                (statutData) => {
                                                    statutData.wilayas.forEach(
                                                        (wilaya) => {
                                                            if (
                                                                !dsaStats.has(
                                                                    wilaya.wilayaId
                                                                )
                                                            )
                                                                dsaStats.set(
                                                                    wilaya.wilayaId,
                                                                    {
                                                                        dsaName:
                                                                            wilayaNames[
                                                                                wilaya
                                                                                    .wilayaId
                                                                            ] ||
                                                                            `DSA ${wilaya.wilayaId}`,
                                                                        total: 0,
                                                                        regularise: 0,
                                                                        ajourne: 0,
                                                                        rejete: 0,
                                                                        nonExamine: 0,
                                                                    }
                                                                );
                                                            const stats =
                                                                dsaStats.get(
                                                                    wilaya.wilayaId
                                                                )!;
                                                            stats.total +=
                                                                wilaya.total;
                                                            stats.regularise +=
                                                                wilaya.regularise;
                                                            stats.ajourne +=
                                                                wilaya.ajourne;
                                                            stats.rejete +=
                                                                wilaya.rejete;
                                                            stats.nonExamine +=
                                                                wilaya.nonExamine;
                                                        }
                                                    );
                                                }
                                            );

                                            return Array.from(
                                                dsaStats.entries()
                                            )
                                                .sort(([a], [b]) => a - b)
                                                .map(([wilayaId, stats]) => (
                                                    <tr key={wilayaId}>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                            {stats.dsaName}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                                                            {stats.total.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                                                            {stats.regularise.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                                            {stats.ajourne.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                                            {stats.rejete.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                                            {stats.nonExamine.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium">
                                                            {stats.total > 0
                                                                ? `${Math.round(
                                                                      (stats.regularise /
                                                                          stats.total) *
                                                                          100
                                                                  )}%`
                                                                : "0%"}
                                                        </td>
                                                    </tr>
                                                ));
                                        })()}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {activeTab === "contraintes" && (
                <div className="space-y-8">
                    <h2 className="text-2xl font-bold text-gray-900">
                        Analyse des Contraintes par Structure Administrative
                    </h2>

                    {analyseData.tableauContraintes.map((dsaData) => (
                        <div
                            key={dsaData.wilayaId}
                            className="bg-white rounded-lg shadow-md border border-gray-200"
                        >
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {wilayaNames[dsaData.wilayaId] ||
                                        `DSA ${dsaData.wilayaId}`}
                                </h3>
                            </div>
                            <div className="px-6 py-4">
                                {dsaData.encrages.length === 0 ? (
                                    <p className="text-gray-500 italic">
                                        Aucune contrainte identifiée pour cette
                                        DSA
                                    </p>
                                ) : (
                                    <div className="space-y-6">
                                        {dsaData.encrages.map(
                                            (encrage, encrageIndex) => (
                                                <div
                                                    key={encrageIndex}
                                                    className="border-l-4 border-blue-500 pl-4"
                                                >
                                                    <div className="mb-4">
                                                        {encrage.secteur ===
                                                        "Secteur non défini" ? (
                                                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                                                <h4 className="text-md font-semibold text-yellow-800">
                                                                    📋 Dossiers
                                                                    sans
                                                                    contrainte
                                                                </h4>
                                                                <p className="text-sm text-yellow-700">
                                                                    Total de
                                                                    dossiersss
                                                                    sans
                                                                    contrainte:{" "}
                                                                    <span className="font-bold text-yellow-900">
                                                                        {
                                                                            encrage.totalCas
                                                                        }
                                                                    </span>
                                                                </p>
                                                            </div>
                                                        ) : (
                                                            <>
                                                                <h4 className="text-md font-semibold text-gray-800">
                                                                    📋{" "}
                                                                    {
                                                                        encrage.secteur
                                                                    }
                                                                </h4>
                                                                <p className="text-sm text-gray-600">
                                                                    Structure
                                                                    Administrative:{" "}
                                                                    <span className="font-medium">
                                                                        {
                                                                            encrage.secteur
                                                                        }
                                                                    </span>{" "}
                                                                    | Total
                                                                    contraintes:{" "}
                                                                    <span className="font-medium text-blue-600">
                                                                        {
                                                                            encrage.totalCas
                                                                        }
                                                                    </span>
                                                                </p>
                                                            </>
                                                        )}
                                                    </div>

                                                    <div className="overflow-x-auto xl:overflow-visible">
                                                        <table className="min-w-full xl:min-w-0 divide-y divide-gray-200">
                                                            <thead className="bg-gray-50">
                                                                <tr>
                                                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                        Problématique
                                                                    </th>
                                                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                        Total
                                                                        Contraintes
                                                                    </th>
                                                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                        Régularisé
                                                                    </th>
                                                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                        Ajourné
                                                                    </th>
                                                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                        Rejeté
                                                                    </th>
                                                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                        Non
                                                                        examiné
                                                                    </th>
                                                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                        Taux
                                                                        Régularisation
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tbody className="bg-white divide-y divide-gray-200">
                                                                {encrage.problematiques.map(
                                                                    (
                                                                        prob,
                                                                        probIndex
                                                                    ) => (
                                                                        <tr
                                                                            key={
                                                                                probIndex
                                                                            }
                                                                            className={`hover:bg-gray-50 ${
                                                                                encrage.secteur ===
                                                                                "Secteur non défini"
                                                                                    ? "bg-yellow-50"
                                                                                    : ""
                                                                            }`}
                                                                        >
                                                                            <td className="px-4 py-3 text-sm text-gray-900">
                                                                                {
                                                                                    prob.problematiqueName
                                                                                }
                                                                            </td>
                                                                            <td className="px-4 py-3 text-sm font-semibold text-gray-900">
                                                                                {
                                                                                    prob.count
                                                                                }
                                                                            </td>
                                                                            <td className="px-4 py-3 text-sm text-green-600 font-medium">
                                                                                {
                                                                                    prob
                                                                                        .statuts
                                                                                        .regularise
                                                                                }
                                                                            </td>
                                                                            <td className="px-4 py-3 text-sm text-yellow-600">
                                                                                {
                                                                                    prob
                                                                                        .statuts
                                                                                        .ajourne
                                                                                }
                                                                            </td>
                                                                            <td className="px-4 py-3 text-sm text-red-600">
                                                                                {
                                                                                    prob
                                                                                        .statuts
                                                                                        .rejete
                                                                                }
                                                                            </td>
                                                                            <td className="px-4 py-3 text-sm text-gray-600">
                                                                                {
                                                                                    prob
                                                                                        .statuts
                                                                                        .nonExamine
                                                                                }
                                                                            </td>
                                                                            <td className="px-4 py-3 text-sm text-blue-600 font-medium">
                                                                                {prob.count >
                                                                                0
                                                                                    ? `${Math.round(
                                                                                          (prob
                                                                                              .statuts
                                                                                              .regularise /
                                                                                              prob.count) *
                                                                                              100
                                                                                      )}%`
                                                                                    : "0%"}
                                                                            </td>
                                                                        </tr>
                                                                    )
                                                                )}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            )
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
            {activeTab === "charts" && (
                <div className="space-y-8">
                    <h2 className="text-2xl font-bold text-gray-900">
                        Graphiques Dynamiques
                    </h2>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
                        {/* Graphique des statuts */}
                        <div className="bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-1">
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Répartition par Statut
                                </h3>
                            </div>
                            <div className="px-6 py-4">
                                <div
                                    style={{ height: "500px" }}
                                    className="xl:h-[600px] 2xl:h-[700px]"
                                >
                                    <Doughnut
                                        data={analyseData.chartStatuts}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            plugins: {
                                                legend: {
                                                    position: "bottom",
                                                    labels: {
                                                        padding: 20,
                                                        usePointStyle: true,
                                                    },
                                                },
                                                title: {
                                                    display: true,
                                                    text: "Distribution des dossiers par statut",
                                                    font: {
                                                        size: 16,
                                                    },
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        label: function (
                                                            context
                                                        ) {
                                                            const total =
                                                                context.dataset.data.reduce(
                                                                    (
                                                                        a: number,
                                                                        b: number
                                                                    ) => a + b,
                                                                    0
                                                                );
                                                            const percentage = (
                                                                (context.parsed /
                                                                    total) *
                                                                100
                                                            ).toFixed(1);
                                                            return `${
                                                                context.label
                                                            }: ${context.parsed.toLocaleString()} (${percentage}%)`;
                                                        },
                                                    },
                                                },
                                            },
                                        }}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Graphique par DSA avec statuts */}
                        <div className="bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-2">
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Nombre de dossiers par DSA
                                </h3>
                            </div>
                            <div className="px-6 py-4">
                                <div
                                    style={{ height: "500px" }}
                                    className="xl:h-[600px] 2xl:h-[700px]"
                                >
                                    <Bar
                                        data={analyseData.chartWilayas}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            plugins: {
                                                legend: {
                                                    position: "top",
                                                    labels: {
                                                        usePointStyle: true,
                                                        padding: 15,
                                                    },
                                                },
                                                title: {
                                                    display: true,
                                                    text: "Répartition des dossiers par DSA et statut",
                                                    font: {
                                                        size: 16,
                                                    },
                                                },
                                                tooltip: {
                                                    mode: "index",
                                                    intersect: false,
                                                    callbacks: {
                                                        footer: function (
                                                            tooltipItems
                                                        ) {
                                                            let total = 0;
                                                            tooltipItems.forEach(
                                                                function (
                                                                    tooltipItem
                                                                ) {
                                                                    total +=
                                                                        tooltipItem
                                                                            .parsed
                                                                            .y;
                                                                }
                                                            );
                                                            return `Total: ${total.toLocaleString()}`;
                                                        },
                                                    },
                                                },
                                            },
                                            scales: {
                                                x: {
                                                    stacked: true,
                                                    title: {
                                                        display: true,
                                                        text: "DSA (Triées par nombre total de dossiers)",
                                                        font: {
                                                            size: 14,
                                                        },
                                                    },
                                                    ticks: {
                                                        maxRotation: 45,
                                                        minRotation: 45,
                                                    },
                                                },
                                                y: {
                                                    stacked: true,
                                                    beginAtZero: true,
                                                    title: {
                                                        display: true,
                                                        text: "Nombre de dossiers",
                                                        font: {
                                                            size: 14,
                                                        },
                                                    },
                                                },
                                            },
                                            interaction: {
                                                mode: "index",
                                                intersect: false,
                                            },
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Chart: Nombre de blocages par secteur et statut */}
                    {(() => {
                        // Aggregate actual blocages by sector and resolution status
                        const sectorStats = new Map<
                            string,
                            {
                                regularise: number;
                                ajourne: number;
                                rejete: number;
                                nonExamine: number;
                            }
                        >();

                        // Agréger les contraintes par secteur et statut de résolution
                        // Chaque statut représente le nombre de contraintes avec cette résolution
                        analyseData.tableauContraintes.forEach((dsaData) => {
                            dsaData.encrages.forEach((encrage) => {
                                const secteur = encrage.secteur;

                                // Skip "Secteur non défini" as it represents cases without constraints
                                if (secteur === "Secteur non défini") {
                                    return;
                                }

                                // Only include sectors that actually have constraints
                                const totalConstraints =
                                    encrage.problematiques.reduce(
                                        (sum, prob) =>
                                            sum +
                                            prob.statuts.regularise +
                                            prob.statuts.ajourne +
                                            prob.statuts.rejete +
                                            prob.statuts.nonExamine,
                                        0
                                    );

                                // Skip if no actual constraints in this sector
                                if (totalConstraints === 0) {
                                    return;
                                }

                                if (!sectorStats.has(secteur)) {
                                    sectorStats.set(secteur, {
                                        regularise: 0,
                                        ajourne: 0,
                                        rejete: 0,
                                        nonExamine: 0,
                                    });
                                }

                                const stats = sectorStats.get(secteur)!;

                                // Count blocages by their resolution status
                                // Each status count represents the number of blocages with that resolution
                                stats.regularise +=
                                    encrage.problematiques.reduce(
                                        (sum, prob) =>
                                            sum + prob.statuts.regularise,
                                        0
                                    );
                                stats.ajourne += encrage.problematiques.reduce(
                                    (sum, prob) => sum + prob.statuts.ajourne,
                                    0
                                );
                                stats.rejete += encrage.problematiques.reduce(
                                    (sum, prob) => sum + prob.statuts.rejete,
                                    0
                                );
                                stats.nonExamine +=
                                    encrage.problematiques.reduce(
                                        (sum, prob) =>
                                            sum + prob.statuts.nonExamine,
                                        0
                                    );
                            });
                        });

                        // Trier les secteurs par nombre total de contraintes décroissant
                        const sortedSecteurs = Array.from(sectorStats.entries())
                            .map(([secteur, stats]) => ({
                                secteur,
                                total:
                                    stats.regularise +
                                    stats.ajourne +
                                    stats.rejete +
                                    stats.nonExamine,
                                ...stats,
                            }))
                            .sort((a, b) => b.total - a.total);

                        return (
                            <div className="bg-white rounded-lg shadow-md border border-gray-200">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-900">
                                        Nombre de Contraintes par Secteur selon
                                        la Résolution
                                    </h3>
                                </div>
                                <div className="px-6 py-4">
                                    <div
                                        style={{ height: "600px" }}
                                        className="xl:h-[700px] 2xl:h-[800px]"
                                    >
                                        <Bar
                                            data={{
                                                labels: sortedSecteurs.map(
                                                    (item) => item.secteur
                                                ),
                                                datasets: [
                                                    {
                                                        label: "Régularisé",
                                                        data: sortedSecteurs.map(
                                                            (item) =>
                                                                item.regularise
                                                        ),
                                                        backgroundColor:
                                                            "#10B981",
                                                    },
                                                    {
                                                        label: "Ajourné",
                                                        data: sortedSecteurs.map(
                                                            (item) =>
                                                                item.ajourne
                                                        ),
                                                        backgroundColor:
                                                            "#F59E0B",
                                                    },
                                                    {
                                                        label: "Rejeté",
                                                        data: sortedSecteurs.map(
                                                            (item) =>
                                                                item.rejete
                                                        ),
                                                        backgroundColor:
                                                            "#EF4444",
                                                    },
                                                    {
                                                        label: "Non examiné",
                                                        data: sortedSecteurs.map(
                                                            (item) =>
                                                                item.nonExamine
                                                        ),
                                                        backgroundColor:
                                                            "#6B7280",
                                                    },
                                                ],
                                            }}
                                            options={{
                                                responsive: true,
                                                maintainAspectRatio: false,
                                                plugins: {
                                                    legend: {
                                                        position: "top",
                                                        labels: {
                                                            usePointStyle: true,
                                                            padding: 20,
                                                        },
                                                    },
                                                    title: {
                                                        display: true,
                                                        text: "Répartition des Contraintes par secteur et statut de résolution",
                                                        font: {
                                                            size: 16,
                                                        },
                                                    },
                                                    tooltip: {
                                                        mode: "index",
                                                        intersect: false,
                                                        callbacks: {
                                                            footer: function (
                                                                tooltipItems
                                                            ) {
                                                                let total = 0;
                                                                tooltipItems.forEach(
                                                                    function (
                                                                        tooltipItem
                                                                    ) {
                                                                        total +=
                                                                            tooltipItem
                                                                                .parsed
                                                                                .y;
                                                                    }
                                                                );
                                                                return `Total contraintes dans ce secteur: ${total.toLocaleString()}`;
                                                            },
                                                        },
                                                    },
                                                },
                                                scales: {
                                                    x: {
                                                        stacked: true,
                                                        title: {
                                                            display: true,
                                                            text: "Secteurs",
                                                            font: {
                                                                size: 14,
                                                            },
                                                        },
                                                        ticks: {
                                                            maxRotation: 45,
                                                            minRotation: 45,
                                                        },
                                                    },
                                                    y: {
                                                        stacked: true,
                                                        beginAtZero: true,
                                                        title: {
                                                            display: true,
                                                            text: "Nombre de contraintes",
                                                            font: {
                                                                size: 14,
                                                            },
                                                        },
                                                    },
                                                },
                                                interaction: {
                                                    mode: "index",
                                                    intersect: false,
                                                },
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        );
                    })()}

                    {/* Full-width summary chart */}
                    <div className="bg-white rounded-lg shadow-md border border-gray-200">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900">
                                Vue d'ensemble
                            </h3>
                        </div>
                        <div className="px-6 py-4">
                            <div
                                style={{ height: "600px" }}
                                className="xl:h-[700px] 2xl:h-[800px]"
                            >
                                <Bar
                                    data={{
                                        ...analyseData.chartWilayas,
                                        labels: analyseData.chartWilayas.labels.slice(
                                            0,
                                            58
                                        ),
                                        datasets:
                                            analyseData.chartWilayas.datasets.map(
                                                (dataset: any) => ({
                                                    ...dataset,
                                                    data: dataset.data.slice(
                                                        0,
                                                        58
                                                    ),
                                                })
                                            ),
                                    }}
                                    options={{
                                        indexAxis: "y",
                                        responsive: true,
                                        maintainAspectRatio: false,
                                        plugins: {
                                            legend: {
                                                position: "top",
                                                labels: {
                                                    usePointStyle: true,
                                                    padding: 20,
                                                },
                                            },
                                            title: {
                                                display: true,
                                                text: " Nombre de dossiers - Répartition détaillée par statut",
                                                font: {
                                                    size: 18,
                                                },
                                            },
                                        },
                                        scales: {
                                            x: {
                                                stacked: true,
                                                beginAtZero: true,
                                                title: {
                                                    display: true,
                                                    text: "Nombre de dossiers",
                                                    font: {
                                                        size: 16,
                                                    },
                                                },
                                            },
                                            y: {
                                                stacked: true,
                                                title: {
                                                    display: true,
                                                    text: "DSA (Triées par nombre décroissant)",
                                                    font: {
                                                        size: 16,
                                                    },
                                                },
                                                ticks: {
                                                    maxRotation: 0,
                                                    minRotation: 0,
                                                    font: {
                                                        size: 12,
                                                    },
                                                },
                                            },
                                        },
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
