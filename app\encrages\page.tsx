"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "../components/Button";
import { Input } from "../components/Input";
import { Table } from "../components/Table";
import { Modal } from "../components/Modal";
import { FormError } from "../components/FormError";
import { Pagination } from "../components/Pagination";
import { FilterBar } from "../components/FilterBar";
import { useEncrages } from "../hooks/useEncrages";
import { apiClient } from "@/lib/api-client";
import { CalendarIcon, ArrowsUpDownIcon } from "@heroicons/react/24/outline";

// Définir les types
interface Encrage {
    id: string;
    nom: string;
    // Ajoutez d'autres champs si votre modèle Encrage en a
}

// Nouveau type pour les statistiques d'encrage
interface EncrageStat {
    id: string; // ou encrageId
    nom: string; // nom de l'encrage
    totalCas: number;
    casRegularises: number;
}

interface UserSession {
    id: string;
    role: string;
    wilayaId?: number;
}

export default function EncragesPage() {
    // États pour la gestion des filtres et du tri
    const [searchTerm, setSearchTerm] = useState("");
    const [sortBy, setSortBy] = useState("nom");
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
    const [dateFrom, setDateFrom] = useState("");
    const [dateTo, setDateTo] = useState("");
    const [statusFilter, setStatusFilter] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    // État pour la gestion du modal
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [currentEncrage, setCurrentEncrage] = useState<Encrage | null>(null);
    const [formData, setFormData] = useState({ nom: "" });
    const [error, setError] = useState("");

    // État pour la gestion des permissions
    const [userRole, setUserRole] = useState<string | null>(null);
    const isAdmin = userRole === "ADMIN";

    // Utilisation du hook useEncrages pour charger les données avec pagination et filtrage
    const {
        encrages,
        encrageStats,
        isLoading,
        page,
        totalPages,
        totalItems,
        goToPage,
        refresh: refreshEncrages,
    } = useEncrages({
        limit: pageSize,
        includeStats: true,
        search: searchTerm,
        sortBy,
        sortDirection,
        dateFrom,
        dateTo,
        status: statusFilter,
    });

    // Fonction pour appliquer les filtres
    const applyFilters = useCallback((filters: Record<string, any>) => {
        setSearchTerm(filters.search || "");
        setDateFrom(filters.dateFrom || "");
        setDateTo(filters.dateTo || "");
        setStatusFilter(filters.status || "");
        setCurrentPage(1); // Réinitialiser à la première page lors de l'application des filtres
    }, []);

    // Fonction pour changer le tri
    const handleSortChange = useCallback(
        (column: string) => {
            if (sortBy === column) {
                // Si on clique sur la même colonne, on inverse la direction
                setSortDirection(sortDirection === "asc" ? "desc" : "asc");
            } else {
                // Sinon, on trie par la nouvelle colonne en ordre ascendant
                setSortBy(column);
                setSortDirection("asc");
            }
        },
        [sortBy, sortDirection]
    );

    // Fonction pour changer de page
    const handlePageChange = useCallback(
        (newPage: number) => {
            setCurrentPage(newPage);
            goToPage(newPage);
        },
        [goToPage]
    );

    // Fonction pour changer la taille de la page
    const handlePageSizeChange = useCallback(
        (e: React.ChangeEvent<HTMLSelectElement>) => {
            const newSize = parseInt(e.target.value, 10);
            setPageSize(newSize);
            setCurrentPage(1); // Réinitialiser à la première page lors du changement de taille
        },
        []
    );

    const loadUserSession = useCallback(async () => {
        try {
            const sessionData = await apiClient.get<UserSession>(
                "/api/auth/session"
            );
            setUserRole(sessionData.role);
        } catch (err) {
            console.error(
                "Erreur lors du chargement de la session utilisateur:",
                err
            );
            setUserRole(null);
        }
    }, []);

    // Effet pour charger la session utilisateur au montage du composant
    useEffect(() => {
        loadUserSession();
    }, [loadUserSession]);

    // Effet pour mettre à jour la page dans le hook useEncrages lorsque currentPage change
    useEffect(() => {
        goToPage(currentPage);
    }, [currentPage, goToPage]);

    async function handleSubmit(e: React.FormEvent) {
        e.preventDefault();
        if (!isAdmin) {
            setError("Action non autorisée.");
            return;
        }
        setError("");

        try {
            if (isEditing && currentEncrage) {
                // Mettre à jour un encrage existant
                await apiClient.put<Encrage>(
                    `/api/encrages/${currentEncrage.id}`,
                    formData
                );
            } else {
                // Créer un nouvel encrage
                await apiClient.post<Encrage>("/api/encrages", formData);
            }

            // Recharger les encrages après la création/mise à jour
            refreshEncrages();
            setIsModalOpen(false);
            setFormData({ nom: "" }); // Réinitialiser le formulaire
        } catch (err) {
            setError(
                (err as Error).message ||
                    "Une erreur s'est produite. Veuillez réessayer."
            );
        }
    }

    async function handleDelete(encrageToDelete: Encrage) {
        if (!isAdmin) {
            setError("Action non autorisée.");
            return;
        }
        if (
            !confirm(
                `Êtes-vous sûr de vouloir supprimer l'encrage : "${encrageToDelete.nom}" ?`
            )
        )
            return;

        try {
            await apiClient.delete(`/api/encrages/${encrageToDelete.id}`);
            refreshEncrages();
        } catch (err) {
            setError((err as Error).message);
        }
    }

    function handleEdit(encrageToEdit: Encrage) {
        if (!isAdmin) return;
        setCurrentEncrage(encrageToEdit);
        setFormData({ nom: encrageToEdit.nom });
        setIsEditing(true);
        setIsModalOpen(true);
    }

    function handleAdd() {
        if (!isAdmin) return;
        setCurrentEncrage(null);
        setFormData({ nom: "" });
        setIsEditing(false);
        setIsModalOpen(true);
    }

    const columns = [
        {
            header: "Nom de l'encrage",
            accessorKey: "nom" as keyof Encrage,
        },
        // Ajoutez d'autres colonnes si nécessaire
    ];

    // Configuration des filtres pour le composant FilterBar
    const filterOptions = [
        {
            id: "search",
            label: "Recherche par nom",
            type: "text" as const,
            placeholder: "Rechercher un encrage...",
        },
        {
            id: "dateFrom",
            label: "Date de début",
            type: "date" as const,
        },
        {
            id: "dateTo",
            label: "Date de fin",
            type: "date" as const,
        },
        {
            id: "status",
            label: "Statut",
            type: "select" as const,
            options: [
                { value: "", label: "Tous" },
                { value: "active", label: "Actif" },
                { value: "inactive", label: "Inactif" },
            ],
        },
    ];

    return (
        <div className=" mx-auto px-4 py-8 ">
            {/* Section des Statistiques d'Encrage */}
            {encrageStats.length > 0 && (
                <div className="mb-10">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {encrageStats.map((stat) => {
                            const percentage =
                                stat.totalCas > 0
                                    ? (stat.casRegularises / stat.totalCas) *
                                      100
                                    : 0;
                            let progressBarColorClass = "bg-green-500"; // Vert par défaut

                            if (percentage === 0) {
                                progressBarColorClass = "bg-red-500";
                            } else if (percentage < 50) {
                                progressBarColorClass = "bg-red-500";
                            } else if (percentage < 100) {
                                progressBarColorClass = "bg-orange-500";
                            }

                            return (
                                <div
                                    key={stat.id}
                                    className="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between min-h-[200px] pb-6"
                                >
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-800 mb-2">
                                            {stat.nom}
                                        </h3>
                                        <div className="space-y-2">
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600">
                                                    Total des cas:
                                                </span>
                                                <span className="font-medium text-primary-600">
                                                    {stat.totalCas}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600">
                                                    Cas régularisés:
                                                </span>
                                                <span className="font-medium text-green-600">
                                                    {stat.casRegularises}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600">
                                                    En attente:
                                                </span>
                                                <span className="font-medium text-orange-600">
                                                    {stat.totalCas -
                                                        stat.casRegularises}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    {/* Progress bar toujours présente, même à 0 */}
                                    <div className="mt-6">
                                        <div className="flex justify-between mb-1">
                                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Progression
                                            </span>
                                            <span
                                                className={`text-sm font-medium ${
                                                    percentage === 0
                                                        ? "text-red-700 dark:text-red-500"
                                                        : percentage < 50
                                                        ? "text-red-700 dark:text-red-500"
                                                        : percentage < 100
                                                        ? "text-orange-700 dark:text-orange-500"
                                                        : "text-green-700 dark:text-green-500"
                                                }`}
                                            >
                                                {percentage.toFixed(0)}%
                                            </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                            <div
                                                className={`${progressBarColorClass} h-2.5 rounded-full transition-all duration-300`}
                                                style={{
                                                    width: `${percentage}%`,
                                                }}
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}
            {/* Fin Section des Statistiques d'Encrage */}

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 mt-8 gap-4">
                <h1 className="sm:flex sm:items-center text-2xl font-semibold text-foreground">
                    Gestion des Encrages
                </h1>
                {isAdmin && (
                    <Button onClick={handleAdd} title="Ajouter un encrage">
                        {/* Icône Ajouter */}
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="w-5 h-5"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M12 4.5v15m7.5-7.5h-15"
                            />
                        </svg>
                    </Button>
                )}
            </div>

            {/* Barre de filtrage */}
            <FilterBar
                filters={filterOptions}
                onFilterChange={applyFilters}
                className="mb-6"
                initialValues={{
                    search: searchTerm,
                    dateFrom,
                    dateTo,
                    status: statusFilter,
                }}
            />

            {/* Informations sur les résultats et sélecteur de taille de page */}
            <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-gray-600">
                    Affichage de {encrages.length} encrage(s) sur {totalItems}{" "}
                    au total
                </div>
                <div className="flex items-center space-x-2">
                    <label htmlFor="pageSize" className="text-sm text-gray-600">
                        Afficher par page:
                    </label>
                    <select
                        id="pageSize"
                        value={pageSize}
                        onChange={handlePageSizeChange}
                        className="border border-gray-300 rounded-md text-sm p-1"
                    >
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>

            {error && <FormError message={error} />}

            <div className="overflow-x-auto">
                <Table
                    data={encrages}
                    columns={columns}
                    actions={
                        isAdmin
                            ? (row: Encrage) => (
                                  <div className="flex justify-center items-center space-x-1 sm:space-x-2">
                                      <button
                                          onClick={() => handleEdit(row)}
                                          title="Modifier l'encrage"
                                          className="p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150"
                                      >
                                          {/* Icône Modifier */}
                                          <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              fill="none"
                                              viewBox="0 0 24 24"
                                              strokeWidth={1.5}
                                              stroke="currentColor"
                                              className="w-5 h-5"
                                          >
                                              <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                                              />
                                          </svg>
                                      </button>
                                      <button
                                          onClick={() => handleDelete(row)}
                                          title="Supprimer l'encrage"
                                          className="p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150"
                                      >
                                          {/* Icône Supprimer */}
                                          <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              fill="none"
                                              viewBox="0 0 24 24"
                                              strokeWidth={1.5}
                                              stroke="currentColor"
                                              className="w-5 h-5"
                                          >
                                              <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                              />
                                          </svg>
                                      </button>
                                  </div>
                              )
                            : undefined
                    }
                />
            </div>

            {/* Pagination */}
            <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                className="mt-6"
            />

            {isAdmin &&
                isModalOpen && ( // Le modal ne s'ouvre que si admin, ou si on veut juste l'afficher
                    <Modal
                        isOpen={isModalOpen}
                        onClose={() => setIsModalOpen(false)}
                        title={
                            isEditing
                                ? "Modifier l'encrage"
                                : "Ajouter un encrage"
                        }
                    >
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <Input
                                id="nom"
                                label="Nom de l'encrage"
                                value={formData.nom}
                                onChange={(e) =>
                                    setFormData({
                                        ...formData,
                                        nom: e.target.value,
                                    })
                                }
                                required
                            />
                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setIsModalOpen(false)}
                                    type="button"
                                >
                                    Annuler
                                </Button>
                                <Button type="submit" isLoading={isLoading}>
                                    {isEditing ? "Modifier" : "Ajouter"}
                                </Button>
                            </div>
                        </form>
                    </Modal>
                )}
        </div>
    );
}
