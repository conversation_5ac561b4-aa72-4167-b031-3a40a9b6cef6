/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cas/route";
exports.ids = ["app/api/cas/route"];
exports.modules = {

/***/ "(rsc)/./app/api/cas/route.ts":
/*!******************************!*\
  !*** ./app/api/cas/route.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./lib/permissions.ts\");\n/* harmony import */ var _lib_resolution_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/resolution-utils */ \"(rsc)/./lib/resolution-utils.ts\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_9__);\n\n\n\n\n\n // Assuming this is your auth library\n\n\n\n\n\n// Cache simple en mémoire pour les IDs filtrés par statut\n// En production, utilisez Redis ou un cache distribué\nconst statusFilterCache = new Map();\nconst CACHE_TTL = 5 * 60 * 1000; // 5 minutes\nconst pipelineAsync = (0,util__WEBPACK_IMPORTED_MODULE_8__.promisify)(stream__WEBPACK_IMPORTED_MODULE_9__.pipeline);\n// Get all cas - filtered by user role\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const encrageId = searchParams.get(\"encrageId\");\n        const problematiqueId = searchParams.get(\"problematiqueId\"); // Lire le problematiqueId\n        const withGeojson = searchParams.get(\"withGeojson\") === \"true\";\n        const includeKML = searchParams.get(\"includeKML\") === \"true\";\n        const regularisation = searchParams.get(\"regularisation\");\n        const casStatus = searchParams.get(\"casStatus\");\n        const wilayaId = searchParams.get(\"wilayaId\");\n        // Pagination parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const pageSize = parseInt(searchParams.get(\"pageSize\") || \"20\");\n        const search = searchParams.get(\"search\") || \"\";\n        // Validate pagination parameters\n        if (page < 1) return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Page must be greater than 0\"\n        }, {\n            status: 400\n        });\n        if (pageSize < 1 || pageSize > 1000) return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Page size must be between 1 and 1000\"\n        }, {\n            status: 400\n        });\n        // Get the current user from the token\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)(); // Await the cookies() call\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token); // Assuming verifyToken is async\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        // Build the where clause based on filters and user role\n        let where = {};\n        // Si problematiqueId est fourni, il a la priorité pour le filtrage direct des Cas\n        if (problematiqueId) {\n            where.problematiqueId = problematiqueId;\n        } else if (encrageId) {\n            // Sinon, si encrageId est fourni, filtre les Cas via l'encrage de leur problématique\n            where.problematique = {\n                encrageId: encrageId\n            };\n        }\n        // Filtrage par statut de régularisation\n        if (regularisation === \"true\") {\n            where.regularisation = true;\n        } else if (regularisation === \"false\") {\n            where.regularisation = false;\n        }\n        // Si withGeojson est true, ne retourner que les cas avec des coordonnées (geojson ou kml si includeKML)\n        if (withGeojson) {\n            where.OR = [\n                {\n                    geojson: {\n                        not: null\n                    }\n                },\n                ...includeKML ? [\n                    {\n                        kmlData: {\n                            not: null\n                        }\n                    }\n                ] : []\n            ];\n        }\n        // Search functionality\n        if (search) {\n            where.OR = [\n                {\n                    nom: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nif: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nin: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    communes: {\n                        some: {\n                            nom: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    }\n                }\n            ];\n        }\n        // Filtrage par wilayaId\n        if (userPayload.role === \"BASIC\" || userPayload.role === \"EDITOR\") {\n            // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement\n            if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                where.wilayaId = Number(userPayload.wilayaId);\n            }\n        } else if (userPayload.role === \"ADMIN\" || userPayload.role === \"VIEWER\") {\n            // Pour ADMIN et VIEWER, permettre le filtrage par wilayaId via paramètre\n            if (wilayaId && !isNaN(Number(wilayaId))) {\n                where.wilayaId = Number(wilayaId);\n            } else if (userPayload.role === \"VIEWER\" && userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                where.wilayaId = Number(userPayload.wilayaId);\n            }\n        }\n        // Pour le filtre par statut, nous devons utiliser une approche par batches\n        // car le statut dépend des résolutions de blocage\n        let needsStatusFiltering = casStatus && [\n            \"REGULARISE\",\n            \"AJOURNE\",\n            \"NON_EXAMINE\",\n            \"REJETE\"\n        ].includes(casStatus);\n        let totalCount;\n        let totalPages;\n        let skip;\n        let cas = [];\n        if (!needsStatusFiltering) {\n            // Cas normal - pas de filtre par statut\n            totalCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where\n            });\n            totalPages = Math.ceil(totalCount / pageSize);\n            skip = (page - 1) * pageSize;\n            cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                where,\n                select: {\n                    id: true,\n                    nom: true,\n                    nif: true,\n                    nin: true,\n                    superficie: true,\n                    observation: true,\n                    geojson: true,\n                    kmlData: true,\n                    kmlFileName: true,\n                    regularisation: true,\n                    createdAt: true,\n                    updatedAt: true,\n                    problematique: {\n                        include: {\n                            encrage: true\n                        }\n                    },\n                    user: {\n                        select: {\n                            id: true,\n                            username: true,\n                            role: true\n                        }\n                    },\n                    communes: true,\n                    blocage: {\n                        select: {\n                            resolution: true\n                        }\n                    }\n                },\n                skip,\n                take: pageSize,\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            });\n        } else {\n            // Cas avec filtre par statut - approche optimisée avec cache des IDs filtrés\n            const cacheKey = `cas_status_filter_${casStatus}_${JSON.stringify(where)}_${userPayload.id}`;\n            const now = Date.now();\n            // Vérifier le cache\n            let filteredCasIds = [];\n            let totalFilteredCount = 0;\n            let cacheHit = false;\n            const cached = statusFilterCache.get(cacheKey);\n            if (cached && cached.expiresAt > now) {\n                filteredCasIds = cached.ids;\n                totalFilteredCount = filteredCasIds.length;\n                cacheHit = true;\n            }\n            // Si pas de cache valide, recalculer\n            if (!cacheHit) {\n                const BATCH_SIZE = 1000; // Taille optimisée des batches\n                let currentSkip = 0;\n                let hasMore = true;\n                // Phase 1: Collecter tous les IDs filtrés par statut en une seule passe\n                while(hasMore){\n                    const batch = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                        where,\n                        select: {\n                            id: true,\n                            blocage: {\n                                select: {\n                                    resolution: true\n                                }\n                            }\n                        },\n                        skip: currentSkip,\n                        take: BATCH_SIZE,\n                        orderBy: {\n                            createdAt: \"desc\"\n                        }\n                    });\n                    if (batch.length === 0) {\n                        hasMore = false;\n                        break;\n                    }\n                    // Filtrer par statut et collecter les IDs\n                    const filteredIds = batch.filter((c)=>{\n                        const resolutions = c.blocage.map((b)=>b.resolution);\n                        const actualStatus = (0,_lib_resolution_utils__WEBPACK_IMPORTED_MODULE_7__.getCasStatus)(resolutions);\n                        return actualStatus === casStatus;\n                    }).map((c)=>c.id);\n                    filteredCasIds.push(...filteredIds);\n                    currentSkip += BATCH_SIZE;\n                    if (batch.length < BATCH_SIZE) {\n                        hasMore = false;\n                    }\n                }\n                totalFilteredCount = filteredCasIds.length;\n                // Mettre en cache les résultats\n                statusFilterCache.set(cacheKey, {\n                    ids: filteredCasIds,\n                    timestamp: now,\n                    expiresAt: now + CACHE_TTL\n                });\n                // Nettoyer le cache ancien (simple GC)\n                for (const [key, value] of statusFilterCache.entries()){\n                    if (value.expiresAt <= now) {\n                        statusFilterCache.delete(key);\n                    }\n                }\n            }\n            totalCount = totalFilteredCount;\n            totalPages = Math.ceil(totalCount / pageSize);\n            // Phase 2: Récupérer seulement les données pour la page demandée\n            if (totalCount > 0) {\n                const startIndex = (page - 1) * pageSize;\n                const endIndex = Math.min(startIndex + pageSize, filteredCasIds.length);\n                const pageCasIds = filteredCasIds.slice(startIndex, endIndex);\n                if (pageCasIds.length > 0) {\n                    cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                        where: {\n                            id: {\n                                in: pageCasIds\n                            }\n                        },\n                        select: {\n                            id: true,\n                            nom: true,\n                            nif: true,\n                            nin: true,\n                            superficie: true,\n                            observation: true,\n                            geojson: true,\n                            kmlData: true,\n                            kmlFileName: true,\n                            regularisation: true,\n                            createdAt: true,\n                            updatedAt: true,\n                            problematique: {\n                                include: {\n                                    encrage: true\n                                }\n                            },\n                            user: {\n                                select: {\n                                    id: true,\n                                    username: true,\n                                    role: true\n                                }\n                            },\n                            communes: true,\n                            blocage: {\n                                select: {\n                                    resolution: true\n                                }\n                            }\n                        },\n                        orderBy: {\n                            createdAt: \"desc\"\n                        }\n                    });\n                    // Maintenir l'ordre des IDs filtrés\n                    const idOrder = new Map(pageCasIds.map((id, index)=>[\n                            id,\n                            index\n                        ]));\n                    cas.sort((a, b)=>idOrder.get(a.id) - idOrder.get(b.id));\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            data: cas,\n            pagination: {\n                page,\n                pageSize,\n                totalCount,\n                totalPages,\n                hasNextPage: page < totalPages,\n                hasPrevPage: page > 1\n            }\n        });\n    } catch (error) {\n        console.error(\"ERREUR API_CAS_GET:\", error); // Log détaillé de l'erreur côté serveur\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError) {\n            // Erreurs connues de Prisma (ex: contrainte violée, enregistrement non trouvé)\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: `Erreur de base de données (Prisma): ${error.code}`,\n                message: error.message,\n                details: error.meta\n            }, {\n                status: 500\n            });\n        } else if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientValidationError) {\n            // Erreurs de validation de Prisma (ex: type de champ incorrect)\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Erreur de validation des données (Prisma).\",\n                message: error.message\n            }, {\n                status: 400\n            });\n        }\n        // Pour les autres types d'erreurs, utilisez le gestionnaire générique ou un message par défaut\n        // return handleError(error); // Si handleError est suffisant\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Erreur interne du serveur lors de la récupération des cas.\",\n            message: error instanceof Error ? error.message : \"Une erreur inconnue est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Create new cas\n// Mettre à jour le schéma Zod\nconst casSchema = zod__WEBPACK_IMPORTED_MODULE_10__.z.object({\n    nom: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Le nom est requis.\"),\n    genre: zod__WEBPACK_IMPORTED_MODULE_10__.z.nativeEnum(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne, {\n        errorMap: (issue, ctx)=>{\n            if (issue.code === zod__WEBPACK_IMPORTED_MODULE_10__.z.ZodIssueCode.invalid_enum_value) {\n                return {\n                    message: \"La valeur du genre est invalide.\"\n                };\n            }\n            return {\n                message: ctx.defaultError\n            };\n        }\n    }),\n    nif: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional().nullable(),\n    nin: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional().nullable(),\n    superficie: zod__WEBPACK_IMPORTED_MODULE_10__.z.number().positive(\"La superficie doit être un nombre positif.\"),\n    // MODIFIÉ: regularisation est maintenant optionnel et défaut à false\n    regularisation: zod__WEBPACK_IMPORTED_MODULE_10__.z.boolean().optional().default(false),\n    observation: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional().nullable(),\n    problematiqueId: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().cuid(\"L'ID de la problématique est invalide.\"),\n    communeIds: zod__WEBPACK_IMPORTED_MODULE_10__.z.array(zod__WEBPACK_IMPORTED_MODULE_10__.z.string().regex(/^\\d+$/, \"Chaque ID de commune doit être une chaîne de chiffres.\")).min(1, \"Au moins une commune doit être sélectionnée.\"),\n    date_depot: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().datetime({\n        offset: true\n    }).optional().nullable()\n}).refine((data)=>{\n    if (data.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_MORALE) {\n        return !!data.nif; // NIF requis pour PERSONNE_MORALE\n    }\n    return true;\n}, {\n    message: \"Le NIF est requis et doit être valide pour une personne morale.\",\n    path: [\n        \"nif\"\n    ]\n}).refine((data)=>{\n    if (data.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_PHYSIQUE) {\n        return !!data.nin; // NIN requis pour PERSONNE_PHYSIQUE\n    }\n    return true;\n}, {\n    message: \"Le NIN est requis et doit être valide pour une personne physique.\",\n    path: [\n        \"nin\"\n    ]\n});\n// La transformation pour genre n'est plus nécessaire ici si le frontend envoie déjà les bonnes valeurs\n// et que z.nativeEnum(TypePersonne) est utilisé.\nasync function POST(request) {\n    try {\n        // Check write permissions using the new permission system\n        const { hasPermission, user, error } = await (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_6__.requireWritePermission)();\n        if (!hasPermission || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: error || \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const validation = casSchema.safeParse(body);\n        if (!validation.success) {\n            console.error(\"Validation errors:\", validation.error.flatten());\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(validation.error.flatten(), {\n                status: 400\n            });\n        }\n        // 'userId' est maintenant pris de userPayload.id\n        // 'regularisation' est maintenant inclus dans validation.data\n        const { nom, nif, nin, genre: genreString, date_depot, superficie, regularisation, observation, problematiqueId, communeIds } = validation.data;\n        // Utiliser user pour wilayaId - ADMIN peut créer des cas pour toutes les wilayas\n        // Note: wilayaId is required in the database, so we need to provide a valid value\n        if (!user.wilayaId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"WilayaId is required for all users\"\n            }, {\n                status: 400\n            });\n        }\n        const wilayaId = user.wilayaId;\n        const newCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.create({\n            data: {\n                nom,\n                nif: genreString === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_MORALE ? nif : null,\n                nin: genreString === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_PHYSIQUE ? nin : null,\n                genre: _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne[genreString],\n                date_depot: date_depot ? new Date(date_depot).toISOString() : null,\n                superficie,\n                regularisation,\n                observation,\n                problematiqueId,\n                userId: user.id,\n                wilayaId,\n                communes: {\n                    connect: communeIds.map((id)=>({\n                            id: parseInt(id)\n                        }))\n                }\n            },\n            include: {\n                problematique: true,\n                user: true,\n                communes: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(newCas, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"POST /api/cas error:\", error);\n        // Amélioration de la gestion des erreurs Prisma\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                message: error.message,\n                code: error.code,\n                meta: error.meta\n            }, {\n                status: 400\n            });\n        }\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientValidationError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                message: error.message\n            }, {\n                status: 400\n            });\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cas/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/permissions.ts":
/*!****************************!*\
  !*** ./lib/permissions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentUserPermissions: () => (/* binding */ getCurrentUserPermissions),\n/* harmony export */   getPermissionsByRole: () => (/* binding */ getPermissionsByRole),\n/* harmony export */   requireDeletePermission: () => (/* binding */ requireDeletePermission),\n/* harmony export */   requireFileUploadPermission: () => (/* binding */ requireFileUploadPermission),\n/* harmony export */   requireMessagingPermission: () => (/* binding */ requireMessagingPermission),\n/* harmony export */   requireUserManagementPermission: () => (/* binding */ requireUserManagementPermission),\n/* harmony export */   requireWritePermission: () => (/* binding */ requireWritePermission)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n// Server-side permissions - only use in Server Components\n\n\n/**\n * Get user permissions based on their role\n */ function getPermissionsByRole(role) {\n    switch(role){\n        case \"ADMIN\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: true,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"EDITOR\":\n            return {\n                canRead: true,\n                canWrite: true,\n                canDelete: true,\n                canManageUsers: false,\n                canUploadFiles: true,\n                canSendMessages: true,\n                isReadOnly: false\n            };\n        case \"BASIC\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: true,\n                isReadOnly: true\n            };\n        case \"VIEWER\":\n            return {\n                canRead: true,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n        default:\n            return {\n                canRead: false,\n                canWrite: false,\n                canDelete: false,\n                canManageUsers: false,\n                canUploadFiles: false,\n                canSendMessages: false,\n                isReadOnly: true\n            };\n    }\n}\n/**\n * Get current user permissions from JWT token\n */ async function getCurrentUserPermissions() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return {\n                permissions: null,\n                user: null\n            };\n        }\n        const userPayload = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.verifyToken)(token);\n        if (!userPayload) {\n            return {\n                permissions: null,\n                user: null\n            };\n        }\n        const permissions = getPermissionsByRole(userPayload.role);\n        const user = {\n            id: userPayload.id,\n            role: userPayload.role,\n            username: userPayload.username,\n            email: userPayload.email,\n            wilayaId: userPayload.wilayaId\n        };\n        return {\n            permissions,\n            user\n        };\n    } catch (error) {\n        console.error(\"Error getting user permissions:\", error);\n        return {\n            permissions: null,\n            user: null\n        };\n    }\n}\n/**\n * Check if user has permission for write operations\n */ async function requireWritePermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canWrite) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: Write operations not permitted for VIEWER role\" : \"Insufficient permissions for write operations\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for delete operations\n */ async function requireDeletePermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canDelete) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: Delete operations not permitted for VIEWER role\" : \"Insufficient permissions for delete operations\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for user management\n */ async function requireUserManagementPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canManageUsers) {\n        return {\n            hasPermission: false,\n            user,\n            error: \"Only ADMIN users can manage other users\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for file uploads\n */ async function requireFileUploadPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canUploadFiles) {\n        return {\n            hasPermission: false,\n            user,\n            error: user.role === \"VIEWER\" ? \"Read-only access: File uploads not permitted for VIEWER role\" : \"Insufficient permissions for file uploads\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n/**\n * Check if user has permission for sending messages\n */ async function requireMessagingPermission() {\n    const { permissions, user } = await getCurrentUserPermissions();\n    if (!permissions || !user) {\n        return {\n            hasPermission: false,\n            user: null,\n            error: \"Authentication required\"\n        };\n    }\n    if (!permissions.canSendMessages) {\n        return {\n            hasPermission: false,\n            user,\n            error: \"Read-only access: Messaging not permitted for VIEWER role\"\n        };\n    }\n    return {\n        hasPermission: true,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/resolution-utils.ts":
/*!*********************************!*\
  !*** ./lib/resolution-utils.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterCasByStatus: () => (/* binding */ filterCasByStatus),\n/* harmony export */   getCasStatisticsByResolution: () => (/* binding */ getCasStatisticsByResolution),\n/* harmony export */   getCasStatus: () => (/* binding */ getCasStatus),\n/* harmony export */   isCasAjourne: () => (/* binding */ isCasAjourne),\n/* harmony export */   isCasNonExamine: () => (/* binding */ isCasNonExamine),\n/* harmony export */   isCasRegularise: () => (/* binding */ isCasRegularise),\n/* harmony export */   updateCasRegularisationFromResolutions: () => (/* binding */ updateCasRegularisationFromResolutions)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n/**\n * Met à jour le statut de régularisation d'un cas basé sur les résolutions de ses blocages\n * Règles:\n * - Si tous les blocages sont ACCEPTE -> regularisation = true\n * - Si au moins un blocage est AJOURNE -> regularisation = false\n * - Si tous les blocages sont ATTENTE -> regularisation = false\n */ async function updateCasRegularisationFromResolutions(casId) {\n    try {\n        // Récupérer tous les blocages du cas avec leurs résolutions\n        const blocages = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.blocage.findMany({\n            where: {\n                casId\n            },\n            select: {\n                resolution: true\n            }\n        });\n        if (blocages.length === 0) {\n            // Aucun blocage, le cas reste non régularisé\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.update({\n                where: {\n                    id: casId\n                },\n                data: {\n                    regularisation: false\n                }\n            });\n            return;\n        }\n        const resolutions = blocages.map((b)=>b.resolution);\n        // Déterminer le nouveau statut de régularisation\n        let newRegularisationStatus = false;\n        if (resolutions.every((resolution)=>resolution === \"ACCEPTE\")) {\n            // Tous acceptés -> régularisé\n            newRegularisationStatus = true;\n        } else {\n            // Au moins un ajourné ou en attente -> non régularisé\n            newRegularisationStatus = false;\n        }\n        // Mettre à jour le cas\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.update({\n            where: {\n                id: casId\n            },\n            data: {\n                regularisation: newRegularisationStatus\n            }\n        });\n        console.log(`Cas ${casId} mis à jour: regularisation = ${newRegularisationStatus}`);\n    } catch (error) {\n        console.error(`Erreur lors de la mise à jour du cas ${casId}:`, error);\n        throw error;\n    }\n}\n/**\n * Calcule les statistiques des cas basées sur les résolutions des blocages\n * Filtre par wilaya selon le rôle de l'utilisateur\n */ async function getCasStatisticsByResolution(userPayload) {\n    try {\n        // Construire les conditions de filtrage\n        const where = {};\n        // Filtrage par wilayaId selon le rôle\n        if (userPayload) {\n            if (userPayload.role === \"BASIC\" || userPayload.role === \"EDITOR\") {\n                // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement\n                if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                    where.wilayaId = Number(userPayload.wilayaId);\n                }\n            } else if (userPayload.role === \"ADMIN\" || userPayload.role === \"VIEWER\") {\n            // Pour ADMIN et VIEWER, pas de filtrage par défaut (accès à toutes les wilayas)\n            // Le filtrage peut être ajouté via des paramètres si nécessaire\n            }\n        }\n        console.log(\"📊 getCasStatisticsByResolution - Utilisation de requête SQL optimisée...\");\n        console.time(\"resolution-stats-optimized\");\n        // Version optimisée avec requête SQL brute pour éviter les limites\n        let whereClauseSQL = \"\";\n        let params = [];\n        if (where.wilayaId) {\n            whereClauseSQL = 'WHERE c.\"wilayaId\" = $1';\n            params = [\n                where.wilayaId\n            ];\n        }\n        let casAjournes = 0;\n        let casNonExamines = 0;\n        let casRegularises = 0;\n        let casNonRegularises = 0;\n        let casRejetes = 0;\n        let totalCas = 0;\n        try {\n            // Utiliser une approche plus simple et fiable basée sur la logique getCasStatus\n            const cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.findMany({\n                where: where,\n                include: {\n                    blocage: {\n                        select: {\n                            resolution: true\n                        }\n                    }\n                }\n            });\n            totalCas = cas.length;\n            // Calculer les statistiques en utilisant la logique officielle\n            cas.forEach((c)=>{\n                const resolutions = c.blocage.map((b)=>b.resolution);\n                const status = getCasStatus(resolutions);\n                switch(status){\n                    case \"REGULARISE\":\n                        casRegularises++;\n                        break;\n                    case \"AJOURNE\":\n                        casAjournes++;\n                        break;\n                    case \"REJETE\":\n                        casRejetes++;\n                        break;\n                    case \"NON_EXAMINE\":\n                        casNonExamines++;\n                        break;\n                }\n            });\n            // Les cas non régularisés sont tous sauf les régularisés\n            casNonRegularises = totalCas - casRegularises;\n            console.log(`📊 ${totalCas} cas traités avec requête optimisée`);\n        } catch (sqlError) {\n            console.error(\"Erreur SQL, fallback vers requête simple:\", sqlError);\n            // Fallback vers requête simple avec limite\n            totalCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where\n            });\n            // Estimations simples\n            casRegularises = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                where: {\n                    ...where,\n                    regularisation: true\n                }\n            });\n            casNonExamines = totalCas - casRegularises;\n        }\n        console.timeEnd(\"resolution-stats-optimized\");\n        return {\n            total: totalCas,\n            regularises: casRegularises,\n            nonRegularises: casNonRegularises,\n            ajournes: casAjournes,\n            nonExamines: casNonExamines,\n            rejetes: casRejetes\n        };\n    } catch (error) {\n        console.error(\"Erreur lors du calcul des statistiques:\", error);\n        throw error;\n    }\n}\n/**\n * Détermine si un cas est ajourné (au moins un blocage ajourné)\n */ function isCasAjourne(resolutions) {\n    return resolutions.some((resolution)=>resolution === \"AJOURNE\");\n}\n/**\n * Détermine si un cas est non examiné (tous les blocages en attente ou aucun blocage)\n */ function isCasNonExamine(resolutions) {\n    return resolutions.length === 0 || resolutions.every((resolution)=>resolution === \"ATTENTE\");\n}\n/**\n * Détermine si un cas est régularisé (tous les blocages acceptés)\n */ function isCasRegularise(resolutions) {\n    return resolutions.length > 0 && resolutions.every((resolution)=>resolution === \"ACCEPTE\");\n}\n/**\n * Détermine le statut d'un cas basé sur ses résolutions\n * IMPORTANT: Cette fonction définit la logique de priorité officielle\n * Utilisez cette fonction dans tous les filtres pour maintenir la cohérence\n */ function getCasStatus(resolutions) {\n    if (resolutions.length === 0) {\n        return \"NON_EXAMINE\"; // Cas sans blocage\n    } else if (resolutions.every((r)=>r === \"ATTENTE\")) {\n        return \"NON_EXAMINE\"; // Tous en attente\n    } else if (resolutions.some((r)=>r === \"REJETE\")) {\n        return \"REJETE\"; // Au moins un rejeté (priorité la plus haute)\n    } else if (resolutions.some((r)=>r === \"AJOURNE\")) {\n        return \"AJOURNE\"; // Au moins un ajourné\n    } else if (resolutions.every((r)=>r === \"ACCEPTE\")) {\n        return \"REGULARISE\"; // Tous acceptés\n    } else {\n        return \"NON_EXAMINE\"; // Cas par défaut\n    }\n}\n/**\n * Filtre les cas par statut en utilisant la logique de priorité officielle\n * Utilisez cette fonction dans toutes les APIs pour maintenir la cohérence\n */ function filterCasByStatus(cas, targetStatus) {\n    if (![\n        \"REGULARISE\",\n        \"AJOURNE\",\n        \"NON_EXAMINE\",\n        \"REJETE\"\n    ].includes(targetStatus)) {\n        return cas; // Pas de filtrage si statut invalide\n    }\n    return cas.filter((c)=>{\n        const resolutions = c.blocage.map((b)=>b.resolution);\n        const actualStatus = getCasStatus(resolutions);\n        return actualStatus === targetStatus;\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcmVzb2x1dGlvbi11dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQztBQUl0Qzs7Ozs7O0NBTUMsR0FDTSxlQUFlQyx1Q0FDbEJDLEtBQWE7SUFFYixJQUFJO1FBQ0EsNERBQTREO1FBQzVELE1BQU1DLFdBQVcsTUFBTUgsK0NBQU1BLENBQUNJLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDO1lBQzNDQyxPQUFPO2dCQUFFSjtZQUFNO1lBQ2ZLLFFBQVE7Z0JBQUVDLFlBQVk7WUFBSztRQUMvQjtRQUVBLElBQUlMLFNBQVNNLE1BQU0sS0FBSyxHQUFHO1lBQ3ZCLDZDQUE2QztZQUM3QyxNQUFNVCwrQ0FBTUEsQ0FBQ1UsR0FBRyxDQUFDQyxNQUFNLENBQUM7Z0JBQ3BCTCxPQUFPO29CQUFFTSxJQUFJVjtnQkFBTTtnQkFDbkJXLE1BQU07b0JBQUVDLGdCQUFnQjtnQkFBTTtZQUNsQztZQUNBO1FBQ0o7UUFFQSxNQUFNQyxjQUFjWixTQUFTYSxHQUFHLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRVQsVUFBVTtRQUVwRCxpREFBaUQ7UUFDakQsSUFBSVUsMEJBQTBCO1FBRTlCLElBQUlILFlBQVlJLEtBQUssQ0FBQyxDQUFDWCxhQUFlQSxlQUFlLFlBQVk7WUFDN0QsOEJBQThCO1lBQzlCVSwwQkFBMEI7UUFDOUIsT0FBTztZQUNILHNEQUFzRDtZQUN0REEsMEJBQTBCO1FBQzlCO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1sQiwrQ0FBTUEsQ0FBQ1UsR0FBRyxDQUFDQyxNQUFNLENBQUM7WUFDcEJMLE9BQU87Z0JBQUVNLElBQUlWO1lBQU07WUFDbkJXLE1BQU07Z0JBQUVDLGdCQUFnQkk7WUFBd0I7UUFDcEQ7UUFFQUUsUUFBUUMsR0FBRyxDQUNQLENBQUMsSUFBSSxFQUFFbkIsTUFBTSw4QkFBOEIsRUFBRWdCLHlCQUF5QjtJQUU5RSxFQUFFLE9BQU9JLE9BQU87UUFDWkYsUUFBUUUsS0FBSyxDQUFDLENBQUMscUNBQXFDLEVBQUVwQixNQUFNLENBQUMsQ0FBQyxFQUFFb0I7UUFDaEUsTUFBTUE7SUFDVjtBQUNKO0FBRUE7OztDQUdDLEdBQ00sZUFBZUMsNkJBQTZCQyxXQUFpQjtJQUNoRSxJQUFJO1FBQ0Esd0NBQXdDO1FBQ3hDLE1BQU1sQixRQUFhLENBQUM7UUFFcEIsc0NBQXNDO1FBQ3RDLElBQUlrQixhQUFhO1lBQ2IsSUFBSUEsWUFBWUMsSUFBSSxLQUFLLFdBQVdELFlBQVlDLElBQUksS0FBSyxVQUFVO2dCQUMvRCw2REFBNkQ7Z0JBQzdELElBQ0lELFlBQVlFLFFBQVEsSUFDcEIsQ0FBQ0MsTUFBTUMsT0FBT0osWUFBWUUsUUFBUSxJQUNwQztvQkFDRXBCLE1BQU1vQixRQUFRLEdBQUdFLE9BQU9KLFlBQVlFLFFBQVE7Z0JBQ2hEO1lBQ0osT0FBTyxJQUNIRixZQUFZQyxJQUFJLEtBQUssV0FDckJELFlBQVlDLElBQUksS0FBSyxVQUN2QjtZQUNFLGdGQUFnRjtZQUNoRixnRUFBZ0U7WUFDcEU7UUFDSjtRQUVBTCxRQUFRQyxHQUFHLENBQ1A7UUFFSkQsUUFBUVMsSUFBSSxDQUFDO1FBRWIsbUVBQW1FO1FBQ25FLElBQUlDLGlCQUFpQjtRQUNyQixJQUFJQyxTQUFnQixFQUFFO1FBRXRCLElBQUl6QixNQUFNb0IsUUFBUSxFQUFFO1lBQ2hCSSxpQkFBaUI7WUFDakJDLFNBQVM7Z0JBQUN6QixNQUFNb0IsUUFBUTthQUFDO1FBQzdCO1FBRUEsSUFBSU0sY0FBYztRQUNsQixJQUFJQyxpQkFBaUI7UUFDckIsSUFBSUMsaUJBQWlCO1FBQ3JCLElBQUlDLG9CQUFvQjtRQUN4QixJQUFJQyxhQUFhO1FBQ2pCLElBQUlDLFdBQVc7UUFFZixJQUFJO1lBQ0EsZ0ZBQWdGO1lBQ2hGLE1BQU0zQixNQUFNLE1BQU1WLCtDQUFNQSxDQUFDVSxHQUFHLENBQUNMLFFBQVEsQ0FBQztnQkFDbENDLE9BQU9BO2dCQUNQZ0MsU0FBUztvQkFDTGxDLFNBQVM7d0JBQ0xHLFFBQVE7NEJBQ0pDLFlBQVk7d0JBQ2hCO29CQUNKO2dCQUNKO1lBQ0o7WUFFQTZCLFdBQVczQixJQUFJRCxNQUFNO1lBRXJCLCtEQUErRDtZQUMvREMsSUFBSTZCLE9BQU8sQ0FBQyxDQUFDQztnQkFDVCxNQUFNekIsY0FBY3lCLEVBQUVwQyxPQUFPLENBQUNZLEdBQUcsQ0FDN0IsQ0FBQ0MsSUFBTUEsRUFBRVQsVUFBVTtnQkFFdkIsTUFBTWlDLFNBQVNDLGFBQWEzQjtnQkFFNUIsT0FBUTBCO29CQUNKLEtBQUs7d0JBQ0RQO3dCQUNBO29CQUNKLEtBQUs7d0JBQ0RGO3dCQUNBO29CQUNKLEtBQUs7d0JBQ0RJO3dCQUNBO29CQUNKLEtBQUs7d0JBQ0RIO3dCQUNBO2dCQUNSO1lBQ0o7WUFFQSx5REFBeUQ7WUFDekRFLG9CQUFvQkUsV0FBV0g7WUFDL0JkLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRWdCLFNBQVMsbUNBQW1DLENBQUM7UUFDbkUsRUFBRSxPQUFPTSxVQUFVO1lBQ2Z2QixRQUFRRSxLQUFLLENBQ1QsNkNBQ0FxQjtZQUdKLDJDQUEyQztZQUMzQ04sV0FBVyxNQUFNckMsK0NBQU1BLENBQUNVLEdBQUcsQ0FBQ2tDLEtBQUssQ0FBQztnQkFBRXRDO1lBQU07WUFFMUMsc0JBQXNCO1lBQ3RCNEIsaUJBQWlCLE1BQU1sQywrQ0FBTUEsQ0FBQ1UsR0FBRyxDQUFDa0MsS0FBSyxDQUFDO2dCQUNwQ3RDLE9BQU87b0JBQUUsR0FBR0EsS0FBSztvQkFBRVEsZ0JBQWdCO2dCQUFLO1lBQzVDO1lBRUFtQixpQkFBaUJJLFdBQVdIO1FBQ2hDO1FBRUFkLFFBQVF5QixPQUFPLENBQUM7UUFFaEIsT0FBTztZQUNIQyxPQUFPVDtZQUNQVSxhQUFhYjtZQUNiYyxnQkFBZ0JiO1lBQ2hCYyxVQUFVakI7WUFDVmtCLGFBQWFqQjtZQUNia0IsU0FBU2Y7UUFDYjtJQUNKLEVBQUUsT0FBT2QsT0FBTztRQUNaRixRQUFRRSxLQUFLLENBQUMsMkNBQTJDQTtRQUN6RCxNQUFNQTtJQUNWO0FBQ0o7QUFFQTs7Q0FFQyxHQUNNLFNBQVM4QixhQUFhckMsV0FBeUI7SUFDbEQsT0FBT0EsWUFBWXNDLElBQUksQ0FBQyxDQUFDN0MsYUFBZUEsZUFBZTtBQUMzRDtBQUVBOztDQUVDLEdBQ00sU0FBUzhDLGdCQUFnQnZDLFdBQXlCO0lBQ3JELE9BQ0lBLFlBQVlOLE1BQU0sS0FBSyxLQUN2Qk0sWUFBWUksS0FBSyxDQUFDLENBQUNYLGFBQWVBLGVBQWU7QUFFekQ7QUFFQTs7Q0FFQyxHQUNNLFNBQVMrQyxnQkFBZ0J4QyxXQUF5QjtJQUNyRCxPQUNJQSxZQUFZTixNQUFNLEdBQUcsS0FDckJNLFlBQVlJLEtBQUssQ0FBQyxDQUFDWCxhQUFlQSxlQUFlO0FBRXpEO0FBRUE7Ozs7Q0FJQyxHQUNNLFNBQVNrQyxhQUNaM0IsV0FBeUI7SUFFekIsSUFBSUEsWUFBWU4sTUFBTSxLQUFLLEdBQUc7UUFDMUIsT0FBTyxlQUFlLG1CQUFtQjtJQUM3QyxPQUFPLElBQUlNLFlBQVlJLEtBQUssQ0FBQyxDQUFDcUMsSUFBTUEsTUFBTSxZQUFZO1FBQ2xELE9BQU8sZUFBZSxrQkFBa0I7SUFDNUMsT0FBTyxJQUFJekMsWUFBWXNDLElBQUksQ0FBQyxDQUFDRyxJQUFNQSxNQUFNLFdBQVc7UUFDaEQsT0FBTyxVQUFVLDhDQUE4QztJQUNuRSxPQUFPLElBQUl6QyxZQUFZc0MsSUFBSSxDQUFDLENBQUNHLElBQU1BLE1BQU0sWUFBWTtRQUNqRCxPQUFPLFdBQVcsc0JBQXNCO0lBQzVDLE9BQU8sSUFBSXpDLFlBQVlJLEtBQUssQ0FBQyxDQUFDcUMsSUFBTUEsTUFBTSxZQUFZO1FBQ2xELE9BQU8sY0FBYyxnQkFBZ0I7SUFDekMsT0FBTztRQUNILE9BQU8sZUFBZSxpQkFBaUI7SUFDM0M7QUFDSjtBQUVBOzs7Q0FHQyxHQUNNLFNBQVNDLGtCQUNaL0MsR0FBc0QsRUFDdERnRCxZQUFvQjtJQUVwQixJQUFJLENBQUM7UUFBQztRQUFjO1FBQVc7UUFBZTtLQUFTLENBQUNDLFFBQVEsQ0FBQ0QsZUFBZTtRQUM1RSxPQUFPaEQsS0FBSyxxQ0FBcUM7SUFDckQ7SUFFQSxPQUFPQSxJQUFJa0QsTUFBTSxDQUFDLENBQUNwQjtRQUNmLE1BQU16QixjQUFjeUIsRUFBRXBDLE9BQU8sQ0FBQ1ksR0FBRyxDQUFDLENBQUNDLElBQU1BLEVBQUVULFVBQVU7UUFDckQsTUFBTXFELGVBQWVuQixhQUFhM0I7UUFDbEMsT0FBTzhDLGlCQUFpQkg7SUFDNUI7QUFDSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxSb3VsYVxcRGVza3RvcFxcQVBQTElDQVRJT05TXFxhc3NhaW5pc3NlbWVudFY1XFxsaWJcXHJlc29sdXRpb24tdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJpc21hIH0gZnJvbSBcIkAvbGliL3ByaXNtYVwiO1xuXG5leHBvcnQgdHlwZSBSZXNvbHV0aW9uID0gXCJBVFRFTlRFXCIgfCBcIkFDQ0VQVEVcIiB8IFwiQUpPVVJORVwiIHwgXCJSRUpFVEVcIjtcblxuLyoqXG4gKiBNZXQgw6Agam91ciBsZSBzdGF0dXQgZGUgcsOpZ3VsYXJpc2F0aW9uIGQndW4gY2FzIGJhc8OpIHN1ciBsZXMgcsOpc29sdXRpb25zIGRlIHNlcyBibG9jYWdlc1xuICogUsOoZ2xlczpcbiAqIC0gU2kgdG91cyBsZXMgYmxvY2FnZXMgc29udCBBQ0NFUFRFIC0+IHJlZ3VsYXJpc2F0aW9uID0gdHJ1ZVxuICogLSBTaSBhdSBtb2lucyB1biBibG9jYWdlIGVzdCBBSk9VUk5FIC0+IHJlZ3VsYXJpc2F0aW9uID0gZmFsc2VcbiAqIC0gU2kgdG91cyBsZXMgYmxvY2FnZXMgc29udCBBVFRFTlRFIC0+IHJlZ3VsYXJpc2F0aW9uID0gZmFsc2VcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZUNhc1JlZ3VsYXJpc2F0aW9uRnJvbVJlc29sdXRpb25zKFxuICAgIGNhc0lkOiBzdHJpbmdcbik6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICAgIC8vIFLDqWN1cMOpcmVyIHRvdXMgbGVzIGJsb2NhZ2VzIGR1IGNhcyBhdmVjIGxldXJzIHLDqXNvbHV0aW9uc1xuICAgICAgICBjb25zdCBibG9jYWdlcyA9IGF3YWl0IHByaXNtYS5ibG9jYWdlLmZpbmRNYW55KHtcbiAgICAgICAgICAgIHdoZXJlOiB7IGNhc0lkIH0sXG4gICAgICAgICAgICBzZWxlY3Q6IHsgcmVzb2x1dGlvbjogdHJ1ZSB9LFxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAoYmxvY2FnZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICAvLyBBdWN1biBibG9jYWdlLCBsZSBjYXMgcmVzdGUgbm9uIHLDqWd1bGFyaXPDqVxuICAgICAgICAgICAgYXdhaXQgcHJpc21hLmNhcy51cGRhdGUoe1xuICAgICAgICAgICAgICAgIHdoZXJlOiB7IGlkOiBjYXNJZCB9LFxuICAgICAgICAgICAgICAgIGRhdGE6IHsgcmVndWxhcmlzYXRpb246IGZhbHNlIH0sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHJlc29sdXRpb25zID0gYmxvY2FnZXMubWFwKChiKSA9PiBiLnJlc29sdXRpb24gYXMgUmVzb2x1dGlvbik7XG5cbiAgICAgICAgLy8gRMOpdGVybWluZXIgbGUgbm91dmVhdSBzdGF0dXQgZGUgcsOpZ3VsYXJpc2F0aW9uXG4gICAgICAgIGxldCBuZXdSZWd1bGFyaXNhdGlvblN0YXR1cyA9IGZhbHNlO1xuXG4gICAgICAgIGlmIChyZXNvbHV0aW9ucy5ldmVyeSgocmVzb2x1dGlvbikgPT4gcmVzb2x1dGlvbiA9PT0gXCJBQ0NFUFRFXCIpKSB7XG4gICAgICAgICAgICAvLyBUb3VzIGFjY2VwdMOpcyAtPiByw6lndWxhcmlzw6lcbiAgICAgICAgICAgIG5ld1JlZ3VsYXJpc2F0aW9uU3RhdHVzID0gdHJ1ZTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIEF1IG1vaW5zIHVuIGFqb3VybsOpIG91IGVuIGF0dGVudGUgLT4gbm9uIHLDqWd1bGFyaXPDqVxuICAgICAgICAgICAgbmV3UmVndWxhcmlzYXRpb25TdGF0dXMgPSBmYWxzZTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIE1ldHRyZSDDoCBqb3VyIGxlIGNhc1xuICAgICAgICBhd2FpdCBwcmlzbWEuY2FzLnVwZGF0ZSh7XG4gICAgICAgICAgICB3aGVyZTogeyBpZDogY2FzSWQgfSxcbiAgICAgICAgICAgIGRhdGE6IHsgcmVndWxhcmlzYXRpb246IG5ld1JlZ3VsYXJpc2F0aW9uU3RhdHVzIH0sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgYENhcyAke2Nhc0lkfSBtaXMgw6Agam91cjogcmVndWxhcmlzYXRpb24gPSAke25ld1JlZ3VsYXJpc2F0aW9uU3RhdHVzfWBcbiAgICAgICAgKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJldXIgbG9ycyBkZSBsYSBtaXNlIMOgIGpvdXIgZHUgY2FzICR7Y2FzSWR9OmAsIGVycm9yKTtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxufVxuXG4vKipcbiAqIENhbGN1bGUgbGVzIHN0YXRpc3RpcXVlcyBkZXMgY2FzIGJhc8OpZXMgc3VyIGxlcyByw6lzb2x1dGlvbnMgZGVzIGJsb2NhZ2VzXG4gKiBGaWx0cmUgcGFyIHdpbGF5YSBzZWxvbiBsZSByw7RsZSBkZSBsJ3V0aWxpc2F0ZXVyXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRDYXNTdGF0aXN0aWNzQnlSZXNvbHV0aW9uKHVzZXJQYXlsb2FkPzogYW55KSB7XG4gICAgdHJ5IHtcbiAgICAgICAgLy8gQ29uc3RydWlyZSBsZXMgY29uZGl0aW9ucyBkZSBmaWx0cmFnZVxuICAgICAgICBjb25zdCB3aGVyZTogYW55ID0ge307XG5cbiAgICAgICAgLy8gRmlsdHJhZ2UgcGFyIHdpbGF5YUlkIHNlbG9uIGxlIHLDtGxlXG4gICAgICAgIGlmICh1c2VyUGF5bG9hZCkge1xuICAgICAgICAgICAgaWYgKHVzZXJQYXlsb2FkLnJvbGUgPT09IFwiQkFTSUNcIiB8fCB1c2VyUGF5bG9hZC5yb2xlID09PSBcIkVESVRPUlwiKSB7XG4gICAgICAgICAgICAgICAgLy8gUG91ciBCQVNJQyBldCBFRElUT1IsIGZpbHRyZXIgcGFyIGxldXIgd2lsYXlhSWQgdW5pcXVlbWVudFxuICAgICAgICAgICAgICAgIGlmIChcbiAgICAgICAgICAgICAgICAgICAgdXNlclBheWxvYWQud2lsYXlhSWQgJiZcbiAgICAgICAgICAgICAgICAgICAgIWlzTmFOKE51bWJlcih1c2VyUGF5bG9hZC53aWxheWFJZCkpXG4gICAgICAgICAgICAgICAgKSB7XG4gICAgICAgICAgICAgICAgICAgIHdoZXJlLndpbGF5YUlkID0gTnVtYmVyKHVzZXJQYXlsb2FkLndpbGF5YUlkKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2UgaWYgKFxuICAgICAgICAgICAgICAgIHVzZXJQYXlsb2FkLnJvbGUgPT09IFwiQURNSU5cIiB8fFxuICAgICAgICAgICAgICAgIHVzZXJQYXlsb2FkLnJvbGUgPT09IFwiVklFV0VSXCJcbiAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAgIC8vIFBvdXIgQURNSU4gZXQgVklFV0VSLCBwYXMgZGUgZmlsdHJhZ2UgcGFyIGTDqWZhdXQgKGFjY8OocyDDoCB0b3V0ZXMgbGVzIHdpbGF5YXMpXG4gICAgICAgICAgICAgICAgLy8gTGUgZmlsdHJhZ2UgcGV1dCDDqnRyZSBham91dMOpIHZpYSBkZXMgcGFyYW3DqHRyZXMgc2kgbsOpY2Vzc2FpcmVcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgXCLwn5OKIGdldENhc1N0YXRpc3RpY3NCeVJlc29sdXRpb24gLSBVdGlsaXNhdGlvbiBkZSByZXF1w6p0ZSBTUUwgb3B0aW1pc8OpZS4uLlwiXG4gICAgICAgICk7XG4gICAgICAgIGNvbnNvbGUudGltZShcInJlc29sdXRpb24tc3RhdHMtb3B0aW1pemVkXCIpO1xuXG4gICAgICAgIC8vIFZlcnNpb24gb3B0aW1pc8OpZSBhdmVjIHJlcXXDqnRlIFNRTCBicnV0ZSBwb3VyIMOpdml0ZXIgbGVzIGxpbWl0ZXNcbiAgICAgICAgbGV0IHdoZXJlQ2xhdXNlU1FMID0gXCJcIjtcbiAgICAgICAgbGV0IHBhcmFtczogYW55W10gPSBbXTtcblxuICAgICAgICBpZiAod2hlcmUud2lsYXlhSWQpIHtcbiAgICAgICAgICAgIHdoZXJlQ2xhdXNlU1FMID0gJ1dIRVJFIGMuXCJ3aWxheWFJZFwiID0gJDEnO1xuICAgICAgICAgICAgcGFyYW1zID0gW3doZXJlLndpbGF5YUlkXTtcbiAgICAgICAgfVxuXG4gICAgICAgIGxldCBjYXNBam91cm5lcyA9IDA7XG4gICAgICAgIGxldCBjYXNOb25FeGFtaW5lcyA9IDA7XG4gICAgICAgIGxldCBjYXNSZWd1bGFyaXNlcyA9IDA7XG4gICAgICAgIGxldCBjYXNOb25SZWd1bGFyaXNlcyA9IDA7XG4gICAgICAgIGxldCBjYXNSZWpldGVzID0gMDtcbiAgICAgICAgbGV0IHRvdGFsQ2FzID0gMDtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gVXRpbGlzZXIgdW5lIGFwcHJvY2hlIHBsdXMgc2ltcGxlIGV0IGZpYWJsZSBiYXPDqWUgc3VyIGxhIGxvZ2lxdWUgZ2V0Q2FzU3RhdHVzXG4gICAgICAgICAgICBjb25zdCBjYXMgPSBhd2FpdCBwcmlzbWEuY2FzLmZpbmRNYW55KHtcbiAgICAgICAgICAgICAgICB3aGVyZTogd2hlcmUsXG4gICAgICAgICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgICAgICAgICBibG9jYWdlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNvbHV0aW9uOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIHRvdGFsQ2FzID0gY2FzLmxlbmd0aDtcblxuICAgICAgICAgICAgLy8gQ2FsY3VsZXIgbGVzIHN0YXRpc3RpcXVlcyBlbiB1dGlsaXNhbnQgbGEgbG9naXF1ZSBvZmZpY2llbGxlXG4gICAgICAgICAgICBjYXMuZm9yRWFjaCgoYykgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc29sdXRpb25zID0gYy5ibG9jYWdlLm1hcChcbiAgICAgICAgICAgICAgICAgICAgKGIpID0+IGIucmVzb2x1dGlvbiBhcyBSZXNvbHV0aW9uXG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICBjb25zdCBzdGF0dXMgPSBnZXRDYXNTdGF0dXMocmVzb2x1dGlvbnMpO1xuXG4gICAgICAgICAgICAgICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBcIlJFR1VMQVJJU0VcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc1JlZ3VsYXJpc2VzKys7XG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBcIkFKT1VSTkVcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc0Fqb3VybmVzKys7XG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBcIlJFSkVURVwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgY2FzUmVqZXRlcysrO1xuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgXCJOT05fRVhBTUlORVwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgY2FzTm9uRXhhbWluZXMrKztcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyBMZXMgY2FzIG5vbiByw6lndWxhcmlzw6lzIHNvbnQgdG91cyBzYXVmIGxlcyByw6lndWxhcmlzw6lzXG4gICAgICAgICAgICBjYXNOb25SZWd1bGFyaXNlcyA9IHRvdGFsQ2FzIC0gY2FzUmVndWxhcmlzZXM7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+TiiAke3RvdGFsQ2FzfSBjYXMgdHJhaXTDqXMgYXZlYyByZXF1w6p0ZSBvcHRpbWlzw6llYCk7XG4gICAgICAgIH0gY2F0Y2ggKHNxbEVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICAgIFwiRXJyZXVyIFNRTCwgZmFsbGJhY2sgdmVycyByZXF1w6p0ZSBzaW1wbGU6XCIsXG4gICAgICAgICAgICAgICAgc3FsRXJyb3JcbiAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgIC8vIEZhbGxiYWNrIHZlcnMgcmVxdcOqdGUgc2ltcGxlIGF2ZWMgbGltaXRlXG4gICAgICAgICAgICB0b3RhbENhcyA9IGF3YWl0IHByaXNtYS5jYXMuY291bnQoeyB3aGVyZSB9KTtcblxuICAgICAgICAgICAgLy8gRXN0aW1hdGlvbnMgc2ltcGxlc1xuICAgICAgICAgICAgY2FzUmVndWxhcmlzZXMgPSBhd2FpdCBwcmlzbWEuY2FzLmNvdW50KHtcbiAgICAgICAgICAgICAgICB3aGVyZTogeyAuLi53aGVyZSwgcmVndWxhcmlzYXRpb246IHRydWUgfSxcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICBjYXNOb25FeGFtaW5lcyA9IHRvdGFsQ2FzIC0gY2FzUmVndWxhcmlzZXM7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLnRpbWVFbmQoXCJyZXNvbHV0aW9uLXN0YXRzLW9wdGltaXplZFwiKTtcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdG90YWw6IHRvdGFsQ2FzLFxuICAgICAgICAgICAgcmVndWxhcmlzZXM6IGNhc1JlZ3VsYXJpc2VzLFxuICAgICAgICAgICAgbm9uUmVndWxhcmlzZXM6IGNhc05vblJlZ3VsYXJpc2VzLFxuICAgICAgICAgICAgYWpvdXJuZXM6IGNhc0Fqb3VybmVzLFxuICAgICAgICAgICAgbm9uRXhhbWluZXM6IGNhc05vbkV4YW1pbmVzLFxuICAgICAgICAgICAgcmVqZXRlczogY2FzUmVqZXRlcyxcbiAgICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyZXVyIGxvcnMgZHUgY2FsY3VsIGRlcyBzdGF0aXN0aXF1ZXM6XCIsIGVycm9yKTtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxufVxuXG4vKipcbiAqIETDqXRlcm1pbmUgc2kgdW4gY2FzIGVzdCBham91cm7DqSAoYXUgbW9pbnMgdW4gYmxvY2FnZSBham91cm7DqSlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzQ2FzQWpvdXJuZShyZXNvbHV0aW9uczogUmVzb2x1dGlvbltdKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHJlc29sdXRpb25zLnNvbWUoKHJlc29sdXRpb24pID0+IHJlc29sdXRpb24gPT09IFwiQUpPVVJORVwiKTtcbn1cblxuLyoqXG4gKiBEw6l0ZXJtaW5lIHNpIHVuIGNhcyBlc3Qgbm9uIGV4YW1pbsOpICh0b3VzIGxlcyBibG9jYWdlcyBlbiBhdHRlbnRlIG91IGF1Y3VuIGJsb2NhZ2UpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0Nhc05vbkV4YW1pbmUocmVzb2x1dGlvbnM6IFJlc29sdXRpb25bXSk6IGJvb2xlYW4ge1xuICAgIHJldHVybiAoXG4gICAgICAgIHJlc29sdXRpb25zLmxlbmd0aCA9PT0gMCB8fFxuICAgICAgICByZXNvbHV0aW9ucy5ldmVyeSgocmVzb2x1dGlvbikgPT4gcmVzb2x1dGlvbiA9PT0gXCJBVFRFTlRFXCIpXG4gICAgKTtcbn1cblxuLyoqXG4gKiBEw6l0ZXJtaW5lIHNpIHVuIGNhcyBlc3QgcsOpZ3VsYXJpc8OpICh0b3VzIGxlcyBibG9jYWdlcyBhY2NlcHTDqXMpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0Nhc1JlZ3VsYXJpc2UocmVzb2x1dGlvbnM6IFJlc29sdXRpb25bXSk6IGJvb2xlYW4ge1xuICAgIHJldHVybiAoXG4gICAgICAgIHJlc29sdXRpb25zLmxlbmd0aCA+IDAgJiZcbiAgICAgICAgcmVzb2x1dGlvbnMuZXZlcnkoKHJlc29sdXRpb24pID0+IHJlc29sdXRpb24gPT09IFwiQUNDRVBURVwiKVxuICAgICk7XG59XG5cbi8qKlxuICogRMOpdGVybWluZSBsZSBzdGF0dXQgZCd1biBjYXMgYmFzw6kgc3VyIHNlcyByw6lzb2x1dGlvbnNcbiAqIElNUE9SVEFOVDogQ2V0dGUgZm9uY3Rpb24gZMOpZmluaXQgbGEgbG9naXF1ZSBkZSBwcmlvcml0w6kgb2ZmaWNpZWxsZVxuICogVXRpbGlzZXogY2V0dGUgZm9uY3Rpb24gZGFucyB0b3VzIGxlcyBmaWx0cmVzIHBvdXIgbWFpbnRlbmlyIGxhIGNvaMOpcmVuY2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldENhc1N0YXR1cyhcbiAgICByZXNvbHV0aW9uczogUmVzb2x1dGlvbltdXG4pOiBcIlJFR1VMQVJJU0VcIiB8IFwiQUpPVVJORVwiIHwgXCJOT05fRVhBTUlORVwiIHwgXCJSRUpFVEVcIiB7XG4gICAgaWYgKHJlc29sdXRpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gXCJOT05fRVhBTUlORVwiOyAvLyBDYXMgc2FucyBibG9jYWdlXG4gICAgfSBlbHNlIGlmIChyZXNvbHV0aW9ucy5ldmVyeSgocikgPT4gciA9PT0gXCJBVFRFTlRFXCIpKSB7XG4gICAgICAgIHJldHVybiBcIk5PTl9FWEFNSU5FXCI7IC8vIFRvdXMgZW4gYXR0ZW50ZVxuICAgIH0gZWxzZSBpZiAocmVzb2x1dGlvbnMuc29tZSgocikgPT4gciA9PT0gXCJSRUpFVEVcIikpIHtcbiAgICAgICAgcmV0dXJuIFwiUkVKRVRFXCI7IC8vIEF1IG1vaW5zIHVuIHJlamV0w6kgKHByaW9yaXTDqSBsYSBwbHVzIGhhdXRlKVxuICAgIH0gZWxzZSBpZiAocmVzb2x1dGlvbnMuc29tZSgocikgPT4gciA9PT0gXCJBSk9VUk5FXCIpKSB7XG4gICAgICAgIHJldHVybiBcIkFKT1VSTkVcIjsgLy8gQXUgbW9pbnMgdW4gYWpvdXJuw6lcbiAgICB9IGVsc2UgaWYgKHJlc29sdXRpb25zLmV2ZXJ5KChyKSA9PiByID09PSBcIkFDQ0VQVEVcIikpIHtcbiAgICAgICAgcmV0dXJuIFwiUkVHVUxBUklTRVwiOyAvLyBUb3VzIGFjY2VwdMOpc1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBcIk5PTl9FWEFNSU5FXCI7IC8vIENhcyBwYXIgZMOpZmF1dFxuICAgIH1cbn1cblxuLyoqXG4gKiBGaWx0cmUgbGVzIGNhcyBwYXIgc3RhdHV0IGVuIHV0aWxpc2FudCBsYSBsb2dpcXVlIGRlIHByaW9yaXTDqSBvZmZpY2llbGxlXG4gKiBVdGlsaXNleiBjZXR0ZSBmb25jdGlvbiBkYW5zIHRvdXRlcyBsZXMgQVBJcyBwb3VyIG1haW50ZW5pciBsYSBjb2jDqXJlbmNlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmaWx0ZXJDYXNCeVN0YXR1cyhcbiAgICBjYXM6IEFycmF5PHsgYmxvY2FnZTogQXJyYXk8eyByZXNvbHV0aW9uOiBzdHJpbmcgfT4gfT4sXG4gICAgdGFyZ2V0U3RhdHVzOiBzdHJpbmdcbik6IEFycmF5PHsgYmxvY2FnZTogQXJyYXk8eyByZXNvbHV0aW9uOiBzdHJpbmcgfT4gfT4ge1xuICAgIGlmICghW1wiUkVHVUxBUklTRVwiLCBcIkFKT1VSTkVcIiwgXCJOT05fRVhBTUlORVwiLCBcIlJFSkVURVwiXS5pbmNsdWRlcyh0YXJnZXRTdGF0dXMpKSB7XG4gICAgICAgIHJldHVybiBjYXM7IC8vIFBhcyBkZSBmaWx0cmFnZSBzaSBzdGF0dXQgaW52YWxpZGVcbiAgICB9XG5cbiAgICByZXR1cm4gY2FzLmZpbHRlcigoYykgPT4ge1xuICAgICAgICBjb25zdCByZXNvbHV0aW9ucyA9IGMuYmxvY2FnZS5tYXAoKGIpID0+IGIucmVzb2x1dGlvbiBhcyBSZXNvbHV0aW9uKTtcbiAgICAgICAgY29uc3QgYWN0dWFsU3RhdHVzID0gZ2V0Q2FzU3RhdHVzKHJlc29sdXRpb25zKTtcbiAgICAgICAgcmV0dXJuIGFjdHVhbFN0YXR1cyA9PT0gdGFyZ2V0U3RhdHVzO1xuICAgIH0pO1xufVxuIl0sIm5hbWVzIjpbInByaXNtYSIsInVwZGF0ZUNhc1JlZ3VsYXJpc2F0aW9uRnJvbVJlc29sdXRpb25zIiwiY2FzSWQiLCJibG9jYWdlcyIsImJsb2NhZ2UiLCJmaW5kTWFueSIsIndoZXJlIiwic2VsZWN0IiwicmVzb2x1dGlvbiIsImxlbmd0aCIsImNhcyIsInVwZGF0ZSIsImlkIiwiZGF0YSIsInJlZ3VsYXJpc2F0aW9uIiwicmVzb2x1dGlvbnMiLCJtYXAiLCJiIiwibmV3UmVndWxhcmlzYXRpb25TdGF0dXMiLCJldmVyeSIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsImdldENhc1N0YXRpc3RpY3NCeVJlc29sdXRpb24iLCJ1c2VyUGF5bG9hZCIsInJvbGUiLCJ3aWxheWFJZCIsImlzTmFOIiwiTnVtYmVyIiwidGltZSIsIndoZXJlQ2xhdXNlU1FMIiwicGFyYW1zIiwiY2FzQWpvdXJuZXMiLCJjYXNOb25FeGFtaW5lcyIsImNhc1JlZ3VsYXJpc2VzIiwiY2FzTm9uUmVndWxhcmlzZXMiLCJjYXNSZWpldGVzIiwidG90YWxDYXMiLCJpbmNsdWRlIiwiZm9yRWFjaCIsImMiLCJzdGF0dXMiLCJnZXRDYXNTdGF0dXMiLCJzcWxFcnJvciIsImNvdW50IiwidGltZUVuZCIsInRvdGFsIiwicmVndWxhcmlzZXMiLCJub25SZWd1bGFyaXNlcyIsImFqb3VybmVzIiwibm9uRXhhbWluZXMiLCJyZWpldGVzIiwiaXNDYXNBam91cm5lIiwic29tZSIsImlzQ2FzTm9uRXhhbWluZSIsImlzQ2FzUmVndWxhcmlzZSIsInIiLCJmaWx0ZXJDYXNCeVN0YXR1cyIsInRhcmdldFN0YXR1cyIsImluY2x1ZGVzIiwiZmlsdGVyIiwiYWN0dWFsU3RhdHVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/resolution-utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cas/route.ts */ \"(rsc)/./app/api/cas/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cas/route\",\n        pathname: \"/api/cas\",\n        filename: \"route\",\n        bundlePath: \"app/api/cas/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\cas\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Froute&page=%2Fapi%2Fcas%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();