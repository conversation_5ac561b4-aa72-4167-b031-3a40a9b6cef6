(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2553],{3620:(e,t,a)=>{Promise.resolve().then(a.bind(a,8920))},6280:(e,t,a)=>{"use strict";a.d(t,{W:()=>n});var s=a(2115),r=a(98);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{limit:t=50,includeStats:a=!1,search:n="",includeProblematiques:o=!1,sortBy:i="nom",sortDirection:l="asc",dateFrom:c="",dateTo:u="",status:d=""}=e,[m,p]=(0,s.useState)([]),[g,h]=(0,s.useState)([]),[b,x]=(0,s.useState)(!1),[v,f]=(0,s.useState)(null),[j,y]=(0,s.useState)(1),[S,q]=(0,s.useState)(1),[w,k]=(0,s.useState)(0),N="encrages-".concat(t,"-").concat(a,"-").concat(n,"-").concat(o,"-").concat(j,"-").concat(i,"-").concat(l,"-").concat(c,"-").concat(u,"-").concat(d),E=(0,s.useCallback)(async()=>{let e=sessionStorage.getItem(N);if(e)try{let t=JSON.parse(e),r=t.timestamp;if(Date.now()-r<3e5){var s,m;p(t.data),a&&h(t.stats||[]),q((null==(s=t.pagination)?void 0:s.totalPages)||1),k((null==(m=t.pagination)?void 0:m.total)||0);return}}catch(e){console.error("Erreur lors de la lecture du cache:",e)}x(!0),f(null);try{let e=new URLSearchParams;e.append("page",j.toString()),e.append("limit",t.toString()),e.append("includeStats",a.toString()),e.append("includeProblematiques",o.toString()),n&&e.append("search",n),i&&e.append("sortBy",i),l&&e.append("sortDirection",l),c&&e.append("dateFrom",c),u&&e.append("dateTo",u),d&&e.append("status",d);let s="/api/encrages?".concat(e.toString()),m=await r.uE.get(s);if(p(m.data),q(m.pagination.totalPages),k(m.pagination.total),a){let e=m.data.filter(e=>e.casStats).map(e=>({id:e.id,nom:e.nom,totalCas:e.casStats.total,casRegularises:e.casStats.regularises}));h(e),sessionStorage.setItem(N,JSON.stringify({data:m.data,stats:e,pagination:m.pagination,timestamp:Date.now()}))}else sessionStorage.setItem(N,JSON.stringify({data:m.data,pagination:m.pagination,timestamp:Date.now()}))}catch(e){f(e.message||"Erreur lors du chargement des encrages"),console.error("Erreur lors du chargement des encrages:",e)}finally{x(!1)}},[N,o,a,t,j,n]);return(0,s.useEffect)(()=>{E()},[E]),{encrages:m,encrageStats:g,isLoading:b,error:v,page:j,totalPages:S,totalItems:w,goToPage:(0,s.useCallback)(e=>{y(e)},[]),refresh:E}}},8920:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(5155),r=a(2115),n=a(767),o=a(345),i=a(3157),l=a(2166),c=a(3109),u=a(98),d=a(6280);function m(){let[e,t]=(0,r.useState)([]),{encrages:a,isLoading:m}=(0,d.W)({limit:100}),[p,g]=(0,r.useState)(null),[h,b]=(0,r.useState)(!1),[x,v]=(0,r.useState)(!1),[f,j]=(0,r.useState)(null),[y,S]=(0,r.useState)({problematique:"",encrageId:""}),[q,w]=(0,r.useState)(!1),[k,N]=(0,r.useState)(""),E="ADMIN"===p,C=(0,r.useCallback)(async()=>{w(!0);try{let e=await u.uE.get("/api/problematiques");t(e)}catch(e){N(e.message||"Erreur lors du chargement des probl\xe9matiques.")}finally{w(!1)}},[]),L=(0,r.useCallback)(async()=>{try{let e=await u.uE.get("/api/auth/session");g(e.role)}catch(e){console.error("Erreur lors du chargement de la session utilisateur:",e),g(null)}},[]);async function I(e){e.preventDefault(),N(""),w(!0);try{let e={problematique:y.problematique,encrageId:y.encrageId};x&&f?await (0,u.Zq)("/api/problematiques/".concat(f.id),{method:"PUT",body:e}):await (0,u.Zq)("/api/problematiques",{method:"POST",body:e}),b(!1),C()}catch(e){N(e.message)}finally{w(!1)}}async function P(e){if(confirm('\xcates-vous s\xfbr de vouloir supprimer la probl\xe9matique : "'.concat(e.problematique,'" ?')))try{await (0,u.Zq)("/api/problematiques/".concat(e.id),{method:"DELETE"}),C()}catch(e){N(e.message)}}(0,r.useEffect)(()=>{L(),C()},[L,C]);let A=[{header:"Probl\xe9matique",accessorKey:"problematique",cell:e=>(0,s.jsx)("div",{title:e.problematique,className:"text-base font-medium text-gray-700 line-clamp-3",children:e.problematique})},{header:"Encrage",accessorKey:e=>{var t;return(null==(t=e.encrage)?void 0:t.nom)||"N/A"}},{header:"Cas",accessorKey:e=>{var t;return"".concat((null==(t=e._count)?void 0:t.cas)||0," cas")}}];return(0,s.jsxs)("div",{className:" mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between mb-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-foreground",children:"Probl\xe9matiques"}),E&&(0,s.jsx)(n.Button,{onClick:function(){j(null),S({problematique:"",encrageId:""}),v(!1),b(!0)},title:"Ajouter une probl\xe9matique",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"})})})]}),k&&(0,s.jsx)(c.FormError,{message:k}),(0,s.jsx)(i.X,{data:e,columns:A,actions:E?e=>(0,s.jsxs)("div",{className:"flex justify-center items-center space-x-1 sm:space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{j(e),S({problematique:e.problematique,encrageId:e.encrageId}),v(!0),b(!0)},title:"Modifier la probl\xe9matique",className:"p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"})})}),(0,s.jsx)("button",{onClick:()=>P(e),title:"Supprimer la probl\xe9matique",className:"p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}):void 0}),(0,s.jsx)(l.Modal,{isOpen:h,onClose:()=>b(!1),title:x?"Modifier la probl\xe9matique":"Ajouter une probl\xe9matique",children:(0,s.jsxs)("form",{onSubmit:I,className:"space-y-4",children:[(0,s.jsx)(o.Input,{id:"problematique",label:"Probl\xe9matique",value:y.problematique,onChange:e=>S(t=>({...t,problematique:e.target.value})),required:!0}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"encrageId",className:"block text-sm font-medium text-gray-700",children:"Encrage"}),(0,s.jsxs)("select",{id:"encrageId",value:y.encrageId,onChange:e=>S(t=>({...t,encrageId:e.target.value})),className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm",required:!0,children:[(0,s.jsx)("option",{value:"",children:"S\xe9lectionner un encrage"}),a.map(e=>(0,s.jsx)("option",{value:e.id,children:e.nom},e.id))]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(n.Button,{variant:"outline",onClick:()=>b(!1),type:"button",children:"Annuler"}),(0,s.jsx)(n.Button,{type:"submit",isLoading:q,children:x?"Modifier":"Ajouter"})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9688,9741,8441,1684,7358],()=>t(3620)),_N_E=e.O()}]);