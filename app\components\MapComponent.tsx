"use client";

import { useEffect, useState } from "react";
import { LeafletIcons } from "./LeafletIcons";
import { LayersControl } from "react-leaflet";
import wilayaData from "@/app/data/wilaya.json";
import communesData from "@/app/data/communes.json";

interface Cas {
    id: string;
    nom: string;
    superficie: number;
    observation?: string;
    geojson?: any;
    regularisation: boolean;
    communes: Array<{
        id: string;
        nom: string;
    }>;
    problematique?: {
        id: string;
        problematique: string;
        encrage?: {
            id: string;
            nom: string;
        };
    };
}

interface MapComponentProps {
    casList: Cas[];
    selectedCas: Cas | null;
    setSelectedCas: (cas: Cas | null) => void;
    onViewCasDetails?: (casId: string) => void;
    onViewCasCartographie?: (casId: string) => void;
    center?: [number, number];
    zoom?: number;
    height?: string;
}

export default function MapComponent({
    casList,
    selectedCas,
    setSelectedCas,
    onViewCasDetails,
    onViewCasCartographie,
    center = [36.75, 3.06],
    zoom = 10,
    height = "600px",
}: MapComponentProps) {
    const [isClient, setIsClient] = useState(false);
    const [mapKey, setMapKey] = useState(0);
    const [Map, setMap] = useState<any>(null);
    const [TileLayer, setTileLayer] = useState<any>(null);
    const [Marker, setMarker] = useState<any>(null);
    const [Popup, setPopup] = useState<any>(null);
    const [GeoJSON, setGeoJSON] = useState<any>(null);

    useEffect(() => {
        setIsClient(true);

        // Charger les composants Leaflet côté client uniquement
        const loadLeafletComponents = async () => {
            try {
                const leaflet = await import("react-leaflet");
                setMap(() => leaflet.Map);
                setTileLayer(() => leaflet.TileLayer);
                setMarker(() => leaflet.Marker);
                setPopup(() => leaflet.Popup);
                setGeoJSON(() => leaflet.GeoJSON);
            } catch (error) {
                console.error("Erreur lors du chargement de Leaflet:", error);
            }
        };

        loadLeafletComponents();
    }, []);

    // Forcer la réinitialisation de la carte quand les données changent
    useEffect(() => {
        if (isClient && Map) {
            setMapKey((prev) => prev + 1);
        }
    }, [casList.length, center[0], center[1], isClient, Map]);

    const casWithGeojson = casList.filter((cas) => cas.geojson?.coordinates);

    const handleCasClick = (cas: Cas) => {
        setSelectedCas(cas);
    };

    if (!isClient || !Map || !TileLayer || !Marker || !Popup) {
        return (
            <div
                className="flex items-center justify-center bg-gray-100 rounded-lg"
                style={{ height }}
            >
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Chargement de la carte...</p>
                </div>
            </div>
        );
    }

    return (
        <div style={{ height, width: "100%" }}>
            <LeafletIcons />
            <Map
                key={mapKey}
                center={center}
                zoom={zoom}
                style={{ height: "100%", width: "100%" }}
                className="z-0"
            >
                <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />

                <LayersControl position="topright">
                    {/* Couche des wilayas */}
                    <LayersControl.Overlay name="Wilayas" checked>
                        <GeoJSON
                            data={wilayaData as any}
                            style={{
                                color: "#ff7800",
                                weight: 2,
                                opacity: 0.8,
                                fillOpacity: 0.1,
                            }}
                            onEachFeature={(feature: any, layer: any) => {
                                if (feature.properties) {
                                    const popupContent = `
                                        <div style="min-width: 200px;">
                                            <h4 style="font-weight: bold; margin-bottom: 8px;">Wilaya ${feature.properties.id}</h4>
                                            <p style="margin: 4px 0;"><strong>Nom français:</strong> ${feature.properties.name_fr}</p>
                                            <p style="margin: 4px 0;"><strong>Nom arabe:</strong> ${feature.properties.name_ar}</p>
                                        </div>
                                    `;
                                    layer.bindPopup(popupContent);
                                }
                            }}
                        />
                    </LayersControl.Overlay>

                    {/* Couche des communes */}
                    <LayersControl.Overlay name="Communes">
                        <GeoJSON
                            data={communesData as any}
                            style={{
                                color: "#0066cc",
                                weight: 1,
                                opacity: 0.7,
                                fillOpacity: 0.05,
                            }}
                            onEachFeature={(feature: any, layer: any) => {
                                if (feature.properties) {
                                    const popupContent = `
                                        <div style="min-width: 200px;">
                                            <h4 style="font-weight: bold; margin-bottom: 8px;">Commune</h4>
                                            <p style="margin: 4px 0;"><strong>Nom français:</strong> ${
                                                feature.properties.name_fr
                                            }</p>
                                            <p style="margin: 4px 0;"><strong>Nom arabe:</strong> ${
                                                feature.properties.name_ar
                                            }</p>
                                            <p style="margin: 4px 0;"><strong>Superficie:</strong> ${feature.properties.SHAPE_Area?.toFixed(
                                                2
                                            )} km²</p>
                                        </div>
                                    `;
                                    layer.bindPopup(popupContent);
                                }
                            }}
                        />
                    </LayersControl.Overlay>
                </LayersControl>

                {casWithGeojson.map((cas) => (
                    <Marker
                        key={cas.id}
                        position={[
                            cas.geojson.coordinates[1],
                            cas.geojson.coordinates[0],
                        ]}
                        eventHandlers={{
                            click: () => handleCasClick(cas),
                        }}
                    >
                        <Popup>
                            <div className="p-2 min-w-[200px]">
                                <h3 className="font-bold text-lg mb-2">
                                    {cas.nom}
                                </h3>
                                <div className="space-y-1 text-sm">
                                    <p>
                                        <strong>Superficie:</strong>{" "}
                                        {cas.superficie} Ha
                                    </p>
                                    <p>
                                        <strong>Problématique:</strong>{" "}
                                        {cas.problematique?.problematique}
                                    </p>
                                    <p>
                                        <strong>Communes:</strong>{" "}
                                        {cas.communes
                                            .map((c) => c.nom)
                                            .join(", ")}
                                    </p>
                                    <p>
                                        <strong>Statut:</strong>
                                        <span
                                            className={`ml-1 px-2 py-1 rounded text-xs ${
                                                cas.regularisation
                                                    ? "bg-green-100 text-green-800"
                                                    : "bg-red-100 text-red-800"
                                            }`}
                                        >
                                            {cas.regularisation
                                                ? "Régularisé"
                                                : "Non régularisé"}
                                        </span>
                                    </p>
                                </div>
                                {(onViewCasDetails ||
                                    onViewCasCartographie) && (
                                    <div className="mt-3 space-y-2">
                                        {onViewCasDetails && (
                                            <button
                                                onClick={() =>
                                                    onViewCasDetails(cas.id)
                                                }
                                                className="w-full text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded transition-colors"
                                            >
                                                Voir détails
                                            </button>
                                        )}
                                        {onViewCasCartographie && (
                                            <button
                                                onClick={() =>
                                                    onViewCasCartographie(
                                                        cas.id
                                                    )
                                                }
                                                className="w-full text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors"
                                            >
                                                Cartographie détaillée
                                            </button>
                                        )}
                                    </div>
                                )}
                            </div>
                        </Popup>
                    </Marker>
                ))}

                {/* Affichage du GeoJSON pour le cas sélectionné si disponible */}
                {selectedCas &&
                    selectedCas.geojson &&
                    selectedCas.geojson.type &&
                    GeoJSON && (
                        <GeoJSON
                            data={selectedCas.geojson}
                            style={{
                                color: "#3B82F6",
                                weight: 2,
                                fillColor: "#3B82F6",
                                fillOpacity: 0.2,
                            }}
                        />
                    )}
            </Map>
        </div>
    );
}
