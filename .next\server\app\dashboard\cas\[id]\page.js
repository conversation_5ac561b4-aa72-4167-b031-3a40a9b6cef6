(()=>{var e={};e.id=8443,e.ids=[8443],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6446:(e,t,r)=>{Promise.resolve().then(r.bind(r,1377))},10215:(e,t,r)=>{"use strict";function s({children:e}){return e}r.r(t),r.d(t,{default:()=>s})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11009:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["dashboard",{children:["cas",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29484)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cas\\[id]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,10215)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cas\\[id]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,70815)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cas\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cas\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/cas/[id]/page",pathname:"/dashboard/cas/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},15654:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),i=r(47726);function n({children:e}){let{isCollapsed:t}=(0,i.c)();return(0,s.jsx)("div",{className:"space-y-0 pt-1 md:pt-1 transition-all duration-300 w-full",style:{minHeight:"100vh"},children:e})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27395:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>v});var s=r(60687),i=r(85814),n=r.n(i),o=r(16189),a=r(47726),d=r(20816),l=r(55510),c=r(10799),u=r(72871),m=r(45994),h=r(87061),p=r(37132),x=r(43655),b=r(66524),f=r(91028);function v({user:e}){let t=(0,o.usePathname)(),{isCollapsed:r,setIsCollapsed:i}=(0,a.c)(),v=[{name:"Tableau de bord",href:"/dashboard",icon:d.A},{name:"Gestion Dossiers",href:"/dashboard/cas",icon:l.A},{name:"Cartographie",href:"/dashboard/cartographie",icon:c.A},{name:"R\xe9glementation",href:"/dashboard/reglementation",icon:u.A},{name:"Statistiques",href:"/dashboard/statistiques",icon:m.A},...e?.role==="ADMIN"?[{name:"Utilisateurs",href:"/users",icon:h.A}]:[]];return e?(0,s.jsx)("div",{className:`h-[calc(100vh-2rem)] bg-gradient-to-b   from-sky-900 to-indigo-900/90 text-white flex flex-col transition-all duration-300 shadow-2xl rounded-r-3xl backdrop-blur-md border-r border-indigo-200/30 z-30 ${r?"w-16":"w-56"}`,children:(0,s.jsxs)("div",{className:"flex flex-col h-full ",children:[(0,s.jsx)("div",{className:"p-1 font-extrabold text-xl tracking-wide border-b border-indigo-700 flex items-center gap-2",children:!r&&(0,s.jsx)("span",{className:"bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent drop-shadow-lg",children:"Assainissement"})}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-indigo-800",children:[!r&&(0,s.jsx)("div",{className:"text-base font-semibold text-white",children:"Menu"}),(0,s.jsx)("button",{onClick:()=>i(!r),className:"p-2 rounded-md text-indigo-200 hover:bg-indigo-700 hover:text-white focus:outline-none",children:r?(0,s.jsx)(p.A,{className:"w-7 h-7"}):(0,s.jsx)(x.A,{className:"w-7 h-7"})})]}),(0,s.jsxs)("div",{className:"flex-grow p-0 overflow-y-auto",children:[(0,s.jsxs)("div",{className:`mb-4 pb-2 border-b border-indigo-800 ${r?"hidden":"block"}`,children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"h-12 w-12 rounded-full bg-gradient-to-r from-sky-400 to-indigo-500 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})}),!r&&(0,s.jsxs)("div",{className:"ml-2",children:[(0,s.jsx)("div",{className:"font-semibold text-white text-sm",children:e?.username||"Utilisateur"}),(0,s.jsxs)("div",{className:"text-[11px] text-indigo-200 flex items-center",children:[(0,s.jsx)("span",{className:`inline-block w-2 h-2 rounded-full mr-1.5 ${e?.role==="ADMIN"?"bg-red-400":e?.role==="EDITOR"?"bg-green-400":e?.role==="VIEWER"?"bg-gray-400":"bg-blue-400"}`}),e?.role||"BASIC",e?.role==="VIEWER"&&(0,s.jsx)(b.A,{className:"h-3 w-3 ml-1 text-orange-300",title:"Mode lecture seule"})]})]})]})}),r&&(0,s.jsx)("div",{className:"mt-2 flex flex-col items-center space-y-2",children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-gradient-to-r from-sky-40.0 to-indigo-500 flex items-center justify-center text-white font-bold text-sm shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})})})]}),(0,s.jsx)("nav",{children:(0,s.jsx)("ul",{className:"space-y-2",children:v.map(e=>{let i="/dashboard"===e.href?"/dashboard"===t:t===e.href||t.startsWith(`${e.href}/`),o=e.icon;return(0,s.jsx)("li",{children:(0,s.jsxs)(n(),{href:e.href,title:e.name,className:`flex items-center p-2 rounded-xl transition-colors duration-150 text-base font-medium gap-2 ${i?"bg-gradient-to-r from-sky-500 to-indigo-500 text-white shadow-md":"text-indigo-100 hover:bg-indigo-700 hover:text-white"} ${r?"justify-center":""}`,children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(o,{className:`w-7 h-7 ${!r?"mr-2":""} drop-shadow-lg`})}),!r&&(0,s.jsx)("div",{className:"flex items-center justify-between flex-1",children:(0,s.jsx)("span",{children:e.name})})]})},e.name)})})})]}),(0,s.jsx)("div",{className:`p-2 border-t border-indigo-800  ${r?"flex justify-center":""}`,children:(0,s.jsxs)("button",{onClick:()=>window.location.href="/api/auth/logout",title:"Se d\xe9connecter",className:`w-full flex items-center p-1 text-base rounded-xl transition-colors duration-150 ${r?"justify-center":""} text-indigo-100 hover:text-white hover:bg-indigo-700 font-semibold gap-2`,children:[(0,s.jsx)(f.A,{className:`w-6 h-6 ${!r?"mr-2":""}`}),!r&&"Se d\xe9connecter"]})})]})}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29228:(e,t,r)=>{Promise.resolve().then(r.bind(r,27395)),Promise.resolve().then(r.bind(r,47726)),Promise.resolve().then(r.bind(r,15654))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29484:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413),i=r(39109);function n(e){return(0,s.jsx)(i.default,{...e})}},33873:e=>{"use strict";e.exports=require("path")},36710:(e,t,r)=>{Promise.resolve().then(r.bind(r,39109))},42380:(e,t,r)=>{Promise.resolve().then(r.bind(r,97021)),Promise.resolve().then(r.bind(r,98440)),Promise.resolve().then(r.bind(r,58508))},47726:(e,t,r)=>{"use strict";r.d(t,{SidebarProvider:()=>a,c:()=>o});var s=r(60687),i=r(43210);let n=(0,i.createContext)({isCollapsed:!1,setIsCollapsed:e=>{}}),o=()=>(0,i.useContext)(n);function a({children:e}){let[t,r]=(0,i.useState)(!1);return(0,s.jsx)(n.Provider,{value:{isCollapsed:t,setIsCollapsed:r},children:e})}},55511:e=>{"use strict";e.exports=require("crypto")},58508:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\MainContentClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\MainContentClient.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70815:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413);function i({children:e}){return(0,s.jsx)("main",{className:"min-h-screen w-full px-0 py-2 max-w-full font-inter text-[0.95rem] text-gray-800 bg-gray-50",style:{fontFamily:"Inter, Segoe UI, Arial, sans-serif",lineHeight:1.5},children:e})}},79428:e=>{"use strict";e.exports=require("buffer")},83249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(37413),i=r(41098),n=r(39916),o=r(98440),a=r(97021),d=r(58508);async function l({children:e}){let t=await (0,i.wz)();return t||(0,n.redirect)("/login"),(0,s.jsx)(o.SidebarProvider,{children:(0,s.jsxs)("div",{className:"flex min-h-screen bg-gradient-to-br  from-red-50 via-indigo-50 to-purple-100 font-inter w-full",children:[(0,s.jsx)("aside",{className:"transition-all duration-300 shadow-2xl  ",children:(0,s.jsx)(a.Sidebar,{user:t})}),(0,s.jsx)("main",{className:"flex-1 min-w-0 px-1 md:px-1 py-1 md:py-1",children:(0,s.jsx)("div",{className:"bg-white rounded-3xl shadow-2xl p-1 md:p-1 border border-gray-100 min-h-[80vh]",children:(0,s.jsx)(d.default,{children:e})})})]})})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},97021:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\Sidebar.tsx","Sidebar")},98440:(e,t,r)=>{"use strict";r.d(t,{SidebarProvider:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","useSidebar");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","SidebarProvider")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,3903,5262,2348,2082,2797,9097],()=>r(11009));module.exports=s})();