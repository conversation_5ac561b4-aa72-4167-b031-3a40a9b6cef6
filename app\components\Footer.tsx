"use client";

import { useState, useEffect } from "react";

export function Footer() {
    const [currentYear, setCurrentYear] = useState<number>(
        new Date().getFullYear()
    );

    useEffect(() => {
        // Mettre à jour l'année au cas où l'application reste ouverte longtemps
        const updateYear = () => {
            setCurrentYear(new Date().getFullYear());
        };

        // Vérifier l'année chaque jour
        const interval = setInterval(updateYear, 24 * 60 * 60 * 1000);

        return () => clearInterval(interval);
    }, []);

    return (
        <footer className="bg-gradient-to-r from-slate-800 to-slate-900 text-white mt-auto border-t border-slate-700">
            <div className=" mx-auto px-4 py-6 md:py-8">
                {/* Contenu principal du footer */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-2">
                    {/* Section Copyright */}
                    <div className="flex flex-col space-y-4">
                        <div className="flex items-center space-x-2">
                            <div>
                                {/* <h3 className="text-lg font-bold text-white">
                                    Assainissement Agricole
                                </h3> */}
                                <p className="text-blue-300 text-md font-medium">
                                    Ministère de l'Agriculture et du
                                    developpement rural et de la Pêche
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <div>
                                <span className="text-slate-200 text-sm font-medium">
                                    DIRECTION GENERALE DE L’INVESTISSEMENT ET DU
                                    FONCIER AGRICOLES
                                </span>
                                <p className="text-slate-400 text-xs">
                                    République Algérienne
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Section Contact */}

                    <div className="flex flex-col space-y-4">
                        <h4 className="text-lg font-semibold text-white border-b border-slate-600 pb-2 flex items-center space-x-2">
                            <svg
                                className="w-4 h-4 text-blue-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                                />
                            </svg>

                            <span>DIRECTION CENTRALE </span>
                        </h4>
                        <div className="space-y-3">
                            <div className="flex items-center space-x-3">
                                <div>
                                    <span className="text-slate-200 text-sm font-medium">
                                        DIRECTION DE L'ORGANISATION FONCIERE ET
                                        DE LA MISE EN VALEUR DES TERRES
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/* Section Liens rapides */}
                    <div className="flex flex-col space-y-4">
                        <h4 className="text-lg font-semibold text-white border-b border-slate-600 pb-2 flex items-center space-x-2">
                            <svg
                                className="w-5 h-5 text-blue-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                />
                            </svg>
                            <span>Contact</span>
                        </h4>
                        <div className="space-y-3">
                            <div className="flex items-center space-x-3 group">
                                <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center group-hover:bg-blue-600 transition-colors duration-200">
                                    <svg
                                        className="w-4 h-4 text-blue-400 group-hover:text-white transition-colors duration-200"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                        />
                                    </svg>
                                </div>
                                <div>
                                    <a
                                        href="mailto:<EMAIL>"
                                        className="text-slate-300 hover:text-blue-400 transition-colors duration-200 text-sm font-medium"
                                    >
                                        <EMAIL>
                                    </a>
                                    <p className="text-slate-400 text-xs">
                                        Support technique
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Ligne de séparation */}
                <div className="border-t border-slate-600 mt-6 md:mt-8 pt-4 md:pt-6">
                    <div className="flex flex-col md:flex-row justify-between items-center space-y-3 md:space-y-0">
                        {/* Copyright */}
                        <div className="flex items-center space-x-3 text-slate-400 text-sm">
                            <div className="w-6 h-6 bg-slate-700 rounded-full flex items-center justify-center">
                                <svg
                                    className="w-3 h-3 text-blue-400"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </div>
                            <span>
                                © {currentYear} MADRP. Tous droits réservés.
                            </span>
                        </div>

                        {/* Version et informations techniques */}
                        <div className="flex items-center space-x-4 text-slate-400 text-xs">
                            <div className="flex items-center space-x-2">
                                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span>Version 1.0</span>
                            </div>
                            <span className="hidden sm:inline">•</span>
                            <div className="hidden sm:flex items-center space-x-1">
                                <svg
                                    className="w-3 h-3 text-slate-500"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                                <span>Développé avec Next.js</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
}
