(()=>{var e={};e.id=5513,e.ids=[5513],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7995:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h,dynamic:()=>u});var t=r(60687),a=r(43210),i=r(16189);r(69266);var o=r(87056),n=r(89679),l=r(9171),d=r(15463),c=r(19763);function m({casList:e,onZoomToCas:s,onViewCasDetails:r,selectedCas:a,onSelectCas:i}){return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Liste des Dossiers (",Array.isArray(e)?e.length:0,")"]}),(0,t.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:Array.isArray(e)&&e.map(e=>(0,t.jsx)("div",{className:`border rounded-lg p-3 transition-colors cursor-pointer ${a?.id===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>i&&i(e),children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"font-medium text-sm truncate cursor-pointer hover:text-blue-600",onClick:()=>s(e.id),children:e.nom}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.superficie," Ha •"," ",e.communes?.map(e=>e.nom).join(", "),e.user&&` • DSA: ${e.user.username}`]})]})})},e.id))}),(!Array.isArray(e)||0===e.length)&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("svg",{className:"w-12 h-12 mx-auto mb-3 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,t.jsx)("p",{children:"Aucun cas trouv\xe9 avec les filtres actuels"})]})]})}let u="force-dynamic";function h(){let e=(0,i.useRouter)(),[s,r]=(0,a.useState)([]),[u,h]=(0,a.useState)([]),[x,p]=(0,a.useState)([]),[g,b]=(0,a.useState)(null),[f,v]=(0,a.useState)(!0),[j,y]=(0,a.useState)(null),[N,C]=(0,a.useState)(""),[w,A]=(0,a.useState)(""),[k,P]=(0,a.useState)(!1),[S,I]=(0,a.useState)([]),[D]=(0,a.useState)([36.75,3.06]),R=N?(Array.isArray(u)?u:[]).filter(e=>e.encrage?.id===N):Array.isArray(u)?u:[];console.log("casList is:",s);let L=s.filter(e=>e.geojson?.coordinates||e.kmlData&&e.kmlData.features);console.log("casList with KML:",s.filter(e=>e.kmlData)),console.log("casList with geojson:",s.filter(e=>e.geojson)),console.log("casWithGeojson count:",L.length),console.log("Total casList length:",s.length),console.log("Sample cas item:",s[0]);let q=s=>{e.push(`/cas/${s}`)},T=s=>{e.push(`/cas/${s}/cartographie`)},$=e=>{},O=e=>{let s,r,t;if(e.kmlData&&e.kmlData.features)s=JSON.stringify(e.kmlData,null,2),r=`${e.nom}_geometry.json`,t="application/json";else{if(!e.geojson)return void alert("Aucune donn\xe9e g\xe9ographique disponible pour ce dossier.");s=JSON.stringify(e.geojson,null,2),r=`${e.nom}_geometry.geojson`,t="application/geo+json"}let a=new Blob([s],{type:t}),i=URL.createObjectURL(a),o=document.createElement("a");o.href=i,o.download=r,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(i)};return f?(0,t.jsx)("div",{className:"flex justify-center items-center h-96",children:(0,t.jsx)(o.k,{})}):j?(0,t.jsx)(n.j,{message:j}):(0,t.jsxs)("div",{className:" mx-auto px-2 py-4",children:[(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("div",{className:"bg-white p-2 rounded-lg shadow-sm border mb-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Encrage juridique (programme)"}),(0,t.jsxs)(d.l,{value:N,onChange:e=>{C(e.target.value),A("")},children:[(0,t.jsx)("option",{value:"",children:"Tous les encrages"}),(Array.isArray(x)?x:[]).map(e=>(0,t.jsx)("option",{value:e.id,children:e.nom},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Probl\xe9matique associ\xe9e"}),(0,t.jsxs)(d.l,{value:w,onChange:e=>A(e.target.value),disabled:!N,children:[(0,t.jsx)("option",{value:"",children:"Toutes les probl\xe9matiques"}),R.map(e=>(0,t.jsx)("option",{value:e.id,children:e.problematique},e.id))]})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",checked:k,onChange:e=>P(e.target.checked),className:"mr-2"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Dossiers r\xe9gularis\xe9s"})]})})]})}),(0,t.jsx)("div",{className:"bg-white p-2 rounded-lg shadow-sm border mb-1",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s.length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Total des Dossiers"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:L.length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Dossiers g\xe9olocalis\xe9s"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:s.filter(e=>e.regularisation).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Dossiers r\xe9gularis\xe9s"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,t.jsx)(c.A,{casList:s,selectedCas:g,setSelectedCas:b,onViewCasDetails:q,onViewCasCartographie:T,center:D,zoom:10,height:"600px",kmlLayers:S,onZoomToCas:$,showOnlySelected:!0})})}),(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-3",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-2",children:(0,t.jsx)(m,{casList:s,onZoomToCas:$,onViewCasDetails:q,selectedCas:g,onSelectCas:b})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:g?"D\xe9tails du Dossier s\xe9lectionn\xe9":"S\xe9lectionnez un Dossier sur la carte"}),g?(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2 pt-4 border-t",children:[(0,t.jsx)(l.$,{onClick:()=>q(g.id),className:"w-full",children:"Voir tous les d\xe9tails"}),(0,t.jsx)(l.$,{onClick:()=>T(g.id),variant:"outline",className:"w-full",children:"Cartographie d\xe9taill\xe9e"}),(g.geojson||g.kmlData&&g.kmlData.features)&&(0,t.jsx)(l.$,{onClick:()=>O(g),variant:"outline",className:"w-full",children:"T\xe9l\xe9charger GeoJSON/KML"})]})}):(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)("p",{children:"Cliquez sur un marqueur sur la carte pour voir les d\xe9tails du cas."}),0===L.length&&(0,t.jsx)("p",{className:"mt-2 text-sm",children:"Aucun Dossier g\xe9olocalis\xe9 trouv\xe9 avec les filtres actuels."})]})]})]})]})]})}},9171:(e,s,r)=>{"use strict";r.d(s,{$:()=>o});var t=r(60687),a=r(82348),i=r(87056);function o({children:e,className:s,variant:r="primary",size:o="default",isLoading:n=!1,disabled:l,...d}){return(0,t.jsx)("button",{className:(0,a.QP)("rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/90 focus:ring-secondary/50",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-primary/50",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500/50"}[r],{default:"px-4 py-2",sm:"px-3 py-1.5 text-sm",icon:"p-2"}[o],s),disabled:n||l,...d,children:n?(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsx)(i.k,{})}):e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12334:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i,dynamic:()=>a});var t=r(12907);let a=(0,t.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cartographie\\page.tsx","dynamic"),i=(0,t.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\cartographie\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cartographie\\page.tsx","default")},15463:(e,s,r)=>{"use strict";r.d(s,{l:()=>a});var t=r(60687);r(43210);let a=({label:e,id:s,name:r,value:a,onChange:i,required:o,multiple:n,children:l,className:d,error:c,...m})=>(0,t.jsxs)("div",{children:[e&&(0,t.jsxs)("label",{htmlFor:s||r,className:"block text-sm font-medium text-gray-700 mb-1",children:[e," ",o&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("select",{id:s||r,name:r,value:a,onChange:i,required:o,multiple:n,className:`block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md shadow-sm ${c?"border-red-500 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${d||""}`,...m,children:l}),c&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-600",children:c})]})},15654:(e,s,r)=>{"use strict";r.d(s,{default:()=>i});var t=r(60687),a=r(47726);function i({children:e}){let{isCollapsed:s}=(0,a.c)();return(0,t.jsx)("div",{className:"space-y-0 pt-1 md:pt-1 transition-all duration-300 w-full",style:{minHeight:"100vh"},children:e})}},17424:(e,s,r)=>{Promise.resolve().then(r.bind(r,7995))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27395:(e,s,r)=>{"use strict";r.d(s,{Sidebar:()=>f});var t=r(60687),a=r(85814),i=r.n(a),o=r(16189),n=r(47726),l=r(20816),d=r(55510),c=r(10799),m=r(72871),u=r(45994),h=r(87061),x=r(37132),p=r(43655),g=r(66524),b=r(91028);function f({user:e}){let s=(0,o.usePathname)(),{isCollapsed:r,setIsCollapsed:a}=(0,n.c)(),f=[{name:"Tableau de bord",href:"/dashboard",icon:l.A},{name:"Gestion Dossiers",href:"/dashboard/cas",icon:d.A},{name:"Cartographie",href:"/dashboard/cartographie",icon:c.A},{name:"R\xe9glementation",href:"/dashboard/reglementation",icon:m.A},{name:"Statistiques",href:"/dashboard/statistiques",icon:u.A},...e?.role==="ADMIN"?[{name:"Utilisateurs",href:"/users",icon:h.A}]:[]];return e?(0,t.jsx)("div",{className:`h-[calc(100vh-2rem)] bg-gradient-to-b   from-sky-900 to-indigo-900/90 text-white flex flex-col transition-all duration-300 shadow-2xl rounded-r-3xl backdrop-blur-md border-r border-indigo-200/30 z-30 ${r?"w-16":"w-56"}`,children:(0,t.jsxs)("div",{className:"flex flex-col h-full ",children:[(0,t.jsx)("div",{className:"p-1 font-extrabold text-xl tracking-wide border-b border-indigo-700 flex items-center gap-2",children:!r&&(0,t.jsx)("span",{className:"bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent drop-shadow-lg",children:"Assainissement"})}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-indigo-800",children:[!r&&(0,t.jsx)("div",{className:"text-base font-semibold text-white",children:"Menu"}),(0,t.jsx)("button",{onClick:()=>a(!r),className:"p-2 rounded-md text-indigo-200 hover:bg-indigo-700 hover:text-white focus:outline-none",children:r?(0,t.jsx)(x.A,{className:"w-7 h-7"}):(0,t.jsx)(p.A,{className:"w-7 h-7"})})]}),(0,t.jsxs)("div",{className:"flex-grow p-0 overflow-y-auto",children:[(0,t.jsxs)("div",{className:`mb-4 pb-2 border-b border-indigo-800 ${r?"hidden":"block"}`,children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)("div",{className:"h-12 w-12 rounded-full bg-gradient-to-r from-sky-400 to-indigo-500 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})}),!r&&(0,t.jsxs)("div",{className:"ml-2",children:[(0,t.jsx)("div",{className:"font-semibold text-white text-sm",children:e?.username||"Utilisateur"}),(0,t.jsxs)("div",{className:"text-[11px] text-indigo-200 flex items-center",children:[(0,t.jsx)("span",{className:`inline-block w-2 h-2 rounded-full mr-1.5 ${e?.role==="ADMIN"?"bg-red-400":e?.role==="EDITOR"?"bg-green-400":e?.role==="VIEWER"?"bg-gray-400":"bg-blue-400"}`}),e?.role||"BASIC",e?.role==="VIEWER"&&(0,t.jsx)(g.A,{className:"h-3 w-3 ml-1 text-orange-300",title:"Mode lecture seule"})]})]})]})}),r&&(0,t.jsx)("div",{className:"mt-2 flex flex-col items-center space-y-2",children:(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gradient-to-r from-sky-40.0 to-indigo-500 flex items-center justify-center text-white font-bold text-sm shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})})})]}),(0,t.jsx)("nav",{children:(0,t.jsx)("ul",{className:"space-y-2",children:f.map(e=>{let a="/dashboard"===e.href?"/dashboard"===s:s===e.href||s.startsWith(`${e.href}/`),o=e.icon;return(0,t.jsx)("li",{children:(0,t.jsxs)(i(),{href:e.href,title:e.name,className:`flex items-center p-2 rounded-xl transition-colors duration-150 text-base font-medium gap-2 ${a?"bg-gradient-to-r from-sky-500 to-indigo-500 text-white shadow-md":"text-indigo-100 hover:bg-indigo-700 hover:text-white"} ${r?"justify-center":""}`,children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(o,{className:`w-7 h-7 ${!r?"mr-2":""} drop-shadow-lg`})}),!r&&(0,t.jsx)("div",{className:"flex items-center justify-between flex-1",children:(0,t.jsx)("span",{children:e.name})})]})},e.name)})})})]}),(0,t.jsx)("div",{className:`p-2 border-t border-indigo-800  ${r?"flex justify-center":""}`,children:(0,t.jsxs)("button",{onClick:()=>window.location.href="/api/auth/logout",title:"Se d\xe9connecter",className:`w-full flex items-center p-1 text-base rounded-xl transition-colors duration-150 ${r?"justify-center":""} text-indigo-100 hover:text-white hover:bg-indigo-700 font-semibold gap-2`,children:[(0,t.jsx)(b.A,{className:`w-6 h-6 ${!r?"mr-2":""}`}),!r&&"Se d\xe9connecter"]})})]})}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29228:(e,s,r)=>{Promise.resolve().then(r.bind(r,27395)),Promise.resolve().then(r.bind(r,47726)),Promise.resolve().then(r.bind(r,15654))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40633:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(s,l);let d={children:["",{children:["dashboard",{children:["cartographie",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,12334)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cartographie\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\cartographie\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/cartographie/page",pathname:"/dashboard/cartographie",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},42380:(e,s,r)=>{Promise.resolve().then(r.bind(r,97021)),Promise.resolve().then(r.bind(r,98440)),Promise.resolve().then(r.bind(r,58508))},47726:(e,s,r)=>{"use strict";r.d(s,{SidebarProvider:()=>n,c:()=>o});var t=r(60687),a=r(43210);let i=(0,a.createContext)({isCollapsed:!1,setIsCollapsed:e=>{}}),o=()=>(0,a.useContext)(i);function n({children:e}){let[s,r]=(0,a.useState)(!1);return(0,t.jsx)(i.Provider,{value:{isCollapsed:s,setIsCollapsed:r},children:e})}},55511:e=>{"use strict";e.exports=require("crypto")},58508:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\MainContentClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\MainContentClient.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},82576:(e,s,r)=>{Promise.resolve().then(r.bind(r,12334))},83249:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(37413),a=r(41098),i=r(39916),o=r(98440),n=r(97021),l=r(58508);async function d({children:e}){let s=await (0,a.wz)();return s||(0,i.redirect)("/login"),(0,t.jsx)(o.SidebarProvider,{children:(0,t.jsxs)("div",{className:"flex min-h-screen bg-gradient-to-br  from-red-50 via-indigo-50 to-purple-100 font-inter w-full",children:[(0,t.jsx)("aside",{className:"transition-all duration-300 shadow-2xl  ",children:(0,t.jsx)(n.Sidebar,{user:s})}),(0,t.jsx)("main",{className:"flex-1 min-w-0 px-1 md:px-1 py-1 md:py-1",children:(0,t.jsx)("div",{className:"bg-white rounded-3xl shadow-2xl p-1 md:p-1 border border-gray-100 min-h-[80vh]",children:(0,t.jsx)(l.default,{children:e})})})]})})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},97021:(e,s,r)=>{"use strict";r.d(s,{Sidebar:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\Sidebar.tsx","Sidebar")},98440:(e,s,r)=>{"use strict";r.d(s,{SidebarProvider:()=>a});var t=r(12907);(0,t.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","useSidebar");let a=(0,t.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","SidebarProvider")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[7719,3903,5262,2348,2082,2797,2997],()=>r(40633));module.exports=t})();