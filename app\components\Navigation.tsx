"use client";

import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { Button } from "./Button";
import { useState, useEffect } from "react";

interface User {
    email: string;
    username: string;
    role: string; // Changed to accept any string role
}

interface NavigationProps {
    user?: User | null;
}

interface NavLinkProps {
    href: string;
    children: React.ReactNode;
}

function NavLink({ href, children }: NavLinkProps) {
    const pathname = usePathname();
    const isActive = pathname === href;

    return (
        <Link
            href={href}
            className={`px-3 py-2 rounded-md text-sm font-medium ${
                isActive
                    ? "bg-gray-900 text-white"
                    : "text-gray-300 hover:bg-gray-700 hover:text-white"
            }`}
        >
            {children}
        </Link>
    );
}

// Composant client pour le bouton de déconnexion
function LogoutButton() {
    // Méthode simple et directe pour la déconnexion
    function handleLogout() {
        // Redirection directe vers la page de déconnexion
        window.location.href = "/api/auth/logout";
    }

    return (
        <button
            onClick={handleLogout}
            className="flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors shadow-sm border border-gray-200"
        >
            <svg
                className="w-6 h-6 mr-1.5 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
            </svg>
            {/* <span className="font-medium text-sm">Se déconnecter</span> */}
        </button>
    );
}

export function Navigation({ user }: NavigationProps) {
    const router = useRouter();
    const pathname = usePathname();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    console.log("user", user);

    // Always show navigation bar on all pages

    return (
        <nav className="bg-white shadow-md border-b border-gray-100 fixed top-0 left-0 right-0 z-20">
            <div className="max-w-screen-4xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
                <div className="flex items-center justify-between h-16 sm:h-20">
                    {/* Bloc logo + titre */}
                    <Link href="/" className="flex items-center gap-2 sm:gap-4 group min-w-0 flex-shrink">
                        <img
                            src="/fan.gif"
                            alt="Logo Ministère de l'Agriculture"
                            className="h-12 w-12 sm:h-16 sm:w-16 lg:h-20 lg:w-20 bg-transparent transition-transform duration-200 flex-shrink-0"
                        />
                        <span className="text-lg sm:text-2xl lg:text-3xl font-extrabold text-primary-700 tracking-tight group-hover:text-primary-600 transition-colors duration-200 truncate">
                            <span className="hidden sm:inline">Assainissement du Foncier Agricole</span>
                            <span className="sm:hidden">Assainissement</span>
                        </span>
                    </Link>

                    {/* Menu de navigation - Mobile menu button */}
                    <div className="flex items-center gap-2 sm:gap-4">
                        {/* Mobile menu button - only show if we have navigation links */}
                        {false && ( // Temporarily disabled since nav links are commented out
                            <button
                                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                className="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
                                aria-expanded="false"
                            >
                                <span className="sr-only">Ouvrir le menu principal</span>
                                <svg
                                    className={`${isMobileMenuOpen ? 'hidden' : 'block'} h-6 w-6`}
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    aria-hidden="true"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                                <svg
                                    className={`${isMobileMenuOpen ? 'block' : 'hidden'} h-6 w-6`}
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    aria-hidden="true"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        )}

                        {/* Espace utilisateur ou boutons */}
                        {user ? (
                            <div className="flex items-center gap-2 sm:gap-3">
                                <span className="hidden sm:block text-sm text-gray-600 truncate max-w-[120px] lg:max-w-none">
                                    <span className="hidden md:inline">Bonjour, </span>
                                    <strong className="truncate">{user.username}</strong>
                                </span>
                                <LogoutButton />
                            </div>
                        ) : null}

                        <Link
                            href="/"
                            className="flex items-center gap-2 sm:gap-4 group flex-shrink-0"
                        >
                            <img
                                src="/logo_v2_madr.png"
                                alt="Logo Ministère de l'Agriculture"
                                className="h-10 w-10 sm:h-12 sm:w-12 lg:h-16 lg:w-16 rounded-full bg-white border border-gray-200 shadow group-hover:scale-105 transition-transform duration-200"
                            />
                        </Link>
                    </div>
                </div>

                {/* Mobile menu - only show if we have navigation links */}
                {false && isMobileMenuOpen && ( // Temporarily disabled since nav links are commented out
                    <div className="md:hidden border-t border-gray-200">
                        <div className="px-2 pt-2 pb-3 space-y-1 bg-white">
                            <NavLink href="/reglementation">Réglementation</NavLink>
                            {user && (
                                <>
                                    <NavLink href="/dashboard">Dashboard</NavLink>
                                    <NavLink href="/cas">Cas</NavLink>
                                    <NavLink href="/cartographie">Cartographie</NavLink>
                                    <NavLink href="/problematiques">Problématiques</NavLink>
                                    <NavLink href="/encrages">Encrages</NavLink>
                                    {user?.role === "ADMIN" && (
                                        <NavLink href="/users">Utilisateurs</NavLink>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </nav>
    );
}
