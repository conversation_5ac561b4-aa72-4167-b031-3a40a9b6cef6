(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5537],{1442:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(2115);let r=s.forwardRef(function(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},6280:(e,t,a)=>{"use strict";a.d(t,{W:()=>l});var s=a(2115),r=a(98);function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{limit:t=50,includeStats:a=!1,search:l="",includeProblematiques:n=!1,sortBy:i="nom",sortDirection:c="asc",dateFrom:o="",dateTo:d="",status:u=""}=e,[m,x]=(0,s.useState)([]),[h,g]=(0,s.useState)([]),[p,f]=(0,s.useState)(!1),[j,v]=(0,s.useState)(null),[b,y]=(0,s.useState)(1),[w,N]=(0,s.useState)(1),[k,S]=(0,s.useState)(0),C="encrages-".concat(t,"-").concat(a,"-").concat(l,"-").concat(n,"-").concat(b,"-").concat(i,"-").concat(c,"-").concat(o,"-").concat(d,"-").concat(u),E=(0,s.useCallback)(async()=>{let e=sessionStorage.getItem(C);if(e)try{let t=JSON.parse(e),r=t.timestamp;if(Date.now()-r<3e5){var s,m;x(t.data),a&&g(t.stats||[]),N((null==(s=t.pagination)?void 0:s.totalPages)||1),S((null==(m=t.pagination)?void 0:m.total)||0);return}}catch(e){console.error("Erreur lors de la lecture du cache:",e)}f(!0),v(null);try{let e=new URLSearchParams;e.append("page",b.toString()),e.append("limit",t.toString()),e.append("includeStats",a.toString()),e.append("includeProblematiques",n.toString()),l&&e.append("search",l),i&&e.append("sortBy",i),c&&e.append("sortDirection",c),o&&e.append("dateFrom",o),d&&e.append("dateTo",d),u&&e.append("status",u);let s="/api/encrages?".concat(e.toString()),m=await r.uE.get(s);if(x(m.data),N(m.pagination.totalPages),S(m.pagination.total),a){let e=m.data.filter(e=>e.casStats).map(e=>({id:e.id,nom:e.nom,totalCas:e.casStats.total,casRegularises:e.casStats.regularises}));g(e),sessionStorage.setItem(C,JSON.stringify({data:m.data,stats:e,pagination:m.pagination,timestamp:Date.now()}))}else sessionStorage.setItem(C,JSON.stringify({data:m.data,pagination:m.pagination,timestamp:Date.now()}))}catch(e){v(e.message||"Erreur lors du chargement des encrages"),console.error("Erreur lors du chargement des encrages:",e)}finally{f(!1)}},[C,n,a,t,b,l]);return(0,s.useEffect)(()=>{E()},[E]),{encrages:m,encrageStats:h,isLoading:p,error:j,page:b,totalPages:w,totalItems:k,goToPage:(0,s.useCallback)(e=>{y(e)},[]),refresh:E}}},6781:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(5155),r=a(2115),l=a(767),n=a(345),i=a(3157),c=a(2166),o=a(3109),d=a(6903),u=a(1442);function m(e){let{currentPage:t,totalPages:a,onPageChange:l,className:n=""}=e;return a<=1?null:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mt-4 ".concat(n),children:[(0,s.jsx)("button",{onClick:()=>t>1&&l(t-1),disabled:1===t,className:"p-2 rounded-md ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"aria-label":"Page pr\xe9c\xe9dente",children:(0,s.jsx)(d.A,{className:"h-5 w-5"})}),(()=>{let e=[];if(a<=5)for(let t=1;t<=a;t++)e.push(t);else{e.push(1);let s=Math.max(2,t-1),r=Math.min(a-1,t+1);t<=3?r=4:t>=a-2&&(s=a-3),s>2&&e.push("...");for(let t=s;t<=r;t++)e.push(t);r<a-1&&e.push("..."),e.push(a)}return e})().map((e,a)=>(0,s.jsx)(r.Fragment,{children:"..."===e?(0,s.jsx)("span",{className:"px-3 py-2",children:"..."}):(0,s.jsx)("button",{onClick:()=>"number"==typeof e&&l(e),className:"px-3 py-1 rounded-md ".concat(t===e?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100"),"aria-current":t===e?"page":void 0,"aria-label":"Page ".concat(e),children:e})},a)),(0,s.jsx)("button",{onClick:()=>t<a&&l(t+1),disabled:t===a,className:"p-2 rounded-md ".concat(t===a?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"aria-label":"Page suivante",children:(0,s.jsx)(u.A,{className:"h-5 w-5"})})]})}var x=a(7583),h=a(6280),g=a(98);function p(){let[e,t]=(0,r.useState)(""),[a,d]=(0,r.useState)("nom"),[u,p]=(0,r.useState)("asc"),[f,j]=(0,r.useState)(""),[v,b]=(0,r.useState)(""),[y,w]=(0,r.useState)(""),[N,k]=(0,r.useState)(1),[S,C]=(0,r.useState)(10),[E,L]=(0,r.useState)(!1),[A,M]=(0,r.useState)(!1),[P,B]=(0,r.useState)(null),[F,R]=(0,r.useState)({nom:""}),[O,I]=(0,r.useState)(""),[D,W]=(0,r.useState)(null),T="ADMIN"===D,{encrages:z,encrageStats:_,isLoading:q,page:V,totalPages:H,totalItems:J,goToPage:Q,refresh:U}=(0,h.W)({limit:S,includeStats:!0,search:e,sortBy:a,sortDirection:u,dateFrom:f,dateTo:v,status:y}),G=(0,r.useCallback)(e=>{t(e.search||""),j(e.dateFrom||""),b(e.dateTo||""),w(e.status||""),k(1)},[]);(0,r.useCallback)(e=>{a===e?p("asc"===u?"desc":"asc"):(d(e),p("asc"))},[a,u]);let K=(0,r.useCallback)(e=>{k(e),Q(e)},[Q]),X=(0,r.useCallback)(e=>{C(parseInt(e.target.value,10)),k(1)},[]),Z=(0,r.useCallback)(async()=>{try{let e=await g.uE.get("/api/auth/session");W(e.role)}catch(e){console.error("Erreur lors du chargement de la session utilisateur:",e),W(null)}},[]);async function Y(e){if(e.preventDefault(),!T)return void I("Action non autoris\xe9e.");I("");try{A&&P?await g.uE.put("/api/encrages/".concat(P.id),F):await g.uE.post("/api/encrages",F),U(),L(!1),R({nom:""})}catch(e){I(e.message||"Une erreur s'est produite. Veuillez r\xe9essayer.")}}async function $(e){if(!T)return void I("Action non autoris\xe9e.");if(confirm("\xcates-vous s\xfbr de vouloir supprimer l'encrage : \"".concat(e.nom,'" ?')))try{await g.uE.delete("/api/encrages/".concat(e.id)),U()}catch(e){I(e.message)}}return(0,r.useEffect)(()=>{Z()},[Z]),(0,r.useEffect)(()=>{Q(N)},[N,Q]),(0,s.jsxs)("div",{className:" mx-auto px-4 py-8 ",children:[_.length>0&&(0,s.jsx)("div",{className:"mb-10",children:(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:_.map(e=>{let t=e.totalCas>0?e.casRegularises/e.totalCas*100:0,a="bg-green-500";return 0===t||t<50?a="bg-red-500":t<100&&(a="bg-orange-500"),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex flex-col justify-between min-h-[200px] pb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.nom}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Total des cas:"}),(0,s.jsx)("span",{className:"font-medium text-primary-600",children:e.totalCas})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Cas r\xe9gularis\xe9s:"}),(0,s.jsx)("span",{className:"font-medium text-green-600",children:e.casRegularises})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"En attente:"}),(0,s.jsx)("span",{className:"font-medium text-orange-600",children:e.totalCas-e.casRegularises})]})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Progression"}),(0,s.jsxs)("span",{className:"text-sm font-medium ".concat(0===t||t<50?"text-red-700 dark:text-red-500":t<100?"text-orange-700 dark:text-orange-500":"text-green-700 dark:text-green-500"),children:[t.toFixed(0),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700",children:(0,s.jsx)("div",{className:"".concat(a," h-2.5 rounded-full transition-all duration-300"),style:{width:"".concat(t,"%")}})})]})]},e.id)})})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 mt-8 gap-4",children:[(0,s.jsx)("h1",{className:"sm:flex sm:items-center text-2xl font-semibold text-foreground",children:"Gestion des Encrages"}),T&&(0,s.jsx)(l.Button,{onClick:function(){T&&(B(null),R({nom:""}),M(!1),L(!0))},title:"Ajouter un encrage",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"})})})]}),(0,s.jsx)(x.Q,{filters:[{id:"search",label:"Recherche par nom",type:"text",placeholder:"Rechercher un encrage..."},{id:"dateFrom",label:"Date de d\xe9but",type:"date"},{id:"dateTo",label:"Date de fin",type:"date"},{id:"status",label:"Statut",type:"select",options:[{value:"",label:"Tous"},{value:"active",label:"Actif"},{value:"inactive",label:"Inactif"}]}],onFilterChange:G,className:"mb-6",initialValues:{search:e,dateFrom:f,dateTo:v,status:y}}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["Affichage de ",z.length," encrage(s) sur ",J," ","au total"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("label",{htmlFor:"pageSize",className:"text-sm text-gray-600",children:"Afficher par page:"}),(0,s.jsxs)("select",{id:"pageSize",value:S,onChange:X,className:"border border-gray-300 rounded-md text-sm p-1",children:[(0,s.jsx)("option",{value:"5",children:"5"}),(0,s.jsx)("option",{value:"10",children:"10"}),(0,s.jsx)("option",{value:"20",children:"20"}),(0,s.jsx)("option",{value:"50",children:"50"})]})]})]}),O&&(0,s.jsx)(o.FormError,{message:O}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)(i.X,{data:z,columns:[{header:"Nom de l'encrage",accessorKey:"nom"}],actions:T?e=>(0,s.jsxs)("div",{className:"flex justify-center items-center space-x-1 sm:space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{T&&(B(e),R({nom:e.nom}),M(!0),L(!0))},title:"Modifier l'encrage",className:"p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"})})}),(0,s.jsx)("button",{onClick:()=>$(e),title:"Supprimer l'encrage",className:"p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}):void 0})}),(0,s.jsx)(m,{currentPage:N,totalPages:H,onPageChange:K,className:"mt-6"}),T&&E&&(0,s.jsx)(c.Modal,{isOpen:E,onClose:()=>L(!1),title:A?"Modifier l'encrage":"Ajouter un encrage",children:(0,s.jsxs)("form",{onSubmit:Y,className:"space-y-4",children:[(0,s.jsx)(n.Input,{id:"nom",label:"Nom de l'encrage",value:F.nom,onChange:e=>R({...F,nom:e.target.value}),required:!0}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(l.Button,{variant:"outline",onClick:()=>L(!1),type:"button",children:"Annuler"}),(0,s.jsx)(l.Button,{type:"submit",isLoading:q,children:A?"Modifier":"Ajouter"})]})]})})]})}},6903:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(2115);let r=s.forwardRef(function(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))})},7055:(e,t,a)=>{"use strict";a.d(t,{l:()=>r});var s=a(5155);a(2115);let r=e=>{let{label:t,id:a,name:r,value:l,onChange:n,required:i,multiple:c,children:o,className:d,error:u,...m}=e;return(0,s.jsxs)("div",{children:[t&&(0,s.jsxs)("label",{htmlFor:a||r,className:"block text-sm font-medium text-gray-700 mb-1",children:[t," ",i&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("select",{id:a||r,name:r,value:l,onChange:n,required:i,multiple:c,className:"".concat("block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md shadow-sm"," ").concat(u?"border-red-500 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"," ").concat(d||""),...m,children:o}),u&&(0,s.jsx)("p",{className:"mt-2 text-sm text-red-600",children:u})]})}},7583:(e,t,a)=>{"use strict";a.d(t,{Q:()=>o});var s=a(5155),r=a(2115),l=a(345),n=a(7055),i=a(767);let c=r.forwardRef(function(e,t){let{title:a,titleId:s,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},l),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))});function o(e){let{filters:t,onFilterChange:a,className:o="",initialValues:d={}}=e,[u,m]=(0,r.useState)(d),[x,h]=(0,r.useState)(!0),g=(e,t)=>{m({...u,[e]:t})};return(0,s.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200 ".concat(o),children:[(0,s.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-700 flex items-center",children:[(0,s.jsx)(c,{className:"h-5 w-5 mr-2"}),"Filtres de Recherche"]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4",children:t.map(e=>(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("label",{htmlFor:e.id,className:"block text-sm font-medium text-gray-700 mb-1",children:e.label}),"text"===e.type&&(0,s.jsx)(l.Input,{id:e.id,type:"text",value:u[e.id]||"",onChange:t=>g(e.id,t.target.value),placeholder:e.placeholder,className:"w-full"}),"select"===e.type&&e.options&&(0,s.jsxs)(n.l,{id:e.id,value:u[e.id]||"",onChange:t=>g(e.id,t.target.value),className:"w-full",children:[(0,s.jsx)("option",{value:"",children:"Tous"}),e.options.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}),"date"===e.type&&(0,s.jsx)(l.Input,{id:e.id,type:"date",value:u[e.id]||"",onChange:t=>g(e.id,t.target.value),className:"w-full"}),"checkbox"===e.type&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:e.id,type:"checkbox",checked:!!u[e.id],onChange:t=>g(e.id,t.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:e.id,className:"ml-2 block text-sm text-gray-700",children:e.placeholder||e.label})]})]},e.id))}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,s.jsx)(i.Button,{onClick:()=>{let e=Object.keys(u).reduce((e,t)=>(e[t]="",e),{});m(e),a(e)},variant:"secondary",size:"sm",children:"R\xe9initialiser"}),(0,s.jsx)(i.Button,{onClick:()=>{a(u)},variant:"primary",size:"sm",children:"Appliquer"})]})]})}},9935:(e,t,a)=>{Promise.resolve().then(a.bind(a,6781))}},e=>{var t=t=>e(e.s=t);e.O(0,[9688,9741,8441,1684,7358],()=>t(9935)),_N_E=e.O()}]);