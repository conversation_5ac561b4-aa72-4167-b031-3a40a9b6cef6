"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@/app/components/Button"; // Ajustez le chemin si nécessaire
import { Input } from "@/app/components/Input"; // Ajustez le chemin si nécessaire
import { Table } from "@/app/components/Table"; // Ajustez le chemin si nécessaire
import { Modal } from "@/app/components/Modal"; // Ajustez le chemin si nécessaire
import { FormError } from "@/app/components/FormError"; // Ajustez le chemin si nécessaire
import { fetchApi, apiClient } from "@/lib/api-client";
import { useEncrages } from "@/app/hooks/useEncrages";

// Définir les types
interface Encrage {
    id: string;
    nom: string;
    // Ajoutez d'autres champs si votre modèle Encrage en a
}

// Type pour les statistiques d'encrage
interface EncrageStat {
    id: string; // ou encrageId
    nom: string; // nom de l'encrage
    totalCas: number;
    casRegularises: number;
}

interface UserSession {
    id: string;
    role: string;
    wilayaId?: number;
}

export default function EncragesPage() {
    // Utiliser notre hook personnalisé pour les encrages
    const {
        encrages,
        encrageStats,
        isLoading,
        error: encragesError,
        page,
        totalPages,
        goToPage,
        refresh: refreshEncrages,
    } = useEncrages({
        limit: 50,
        includeStats: true,
    });

    const [userRole, setUserRole] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [currentEncrage, setCurrentEncrage] = useState<Encrage | null>(null);
    const [formData, setFormData] = useState({ nom: "" });
    const [error, setError] = useState("");

    const isAdmin = userRole === "ADMIN";

    const loadUserSession = useCallback(async () => {
        try {
            const sessionData = await apiClient.get<UserSession>(
                "/api/auth/session"
            );
            setUserRole(sessionData.role);
        } catch (err) {
            console.error(
                "Erreur lors du chargement de la session utilisateur:",
                err
            );
            setUserRole(null);
        }
    }, []);

    useEffect(() => {
        loadUserSession();
    }, [loadUserSession]);

    // Mettre à jour l'erreur si notre hook renvoie une erreur
    useEffect(() => {
        if (encragesError) {
            setError(encragesError);
        }
    }, [encragesError]);

    async function handleSubmit(e: React.FormEvent) {
        e.preventDefault();
        if (!isAdmin) {
            setError("Action non autorisée.");
            return;
        }
        setError("");

        try {
            const payload = { nom: formData.nom };

            if (isEditing && currentEncrage) {
                await fetchApi(`/api/encrages/${currentEncrage.id}`, {
                    method: "PUT",
                    body: payload,
                });
            } else {
                await fetchApi("/api/encrages", {
                    method: "POST",
                    body: payload,
                });
            }
            setIsModalOpen(false);
            refreshEncrages();
        } catch (err) {
            setError((err as Error).message);
        } finally {
            // Loading state is managed by the useEncrages hook
        }
    }

    async function handleDelete(encrageToDelete: Encrage) {
        if (!isAdmin) {
            setError("Action non autorisée.");
            return;
        }
        if (
            !confirm(
                `Êtes-vous sûr de vouloir supprimer l'encrage : "${encrageToDelete.nom}" ?`
            )
        )
            return;

        try {
            await fetchApi(`/api/encrages/${encrageToDelete.id}`, {
                method: "DELETE",
            });
            refreshEncrages();
        } catch (err) {
            setError((err as Error).message);
        }
    }

    function handleEdit(encrageToEdit: Encrage) {
        if (!isAdmin) return;
        setCurrentEncrage(encrageToEdit);
        setFormData({ nom: encrageToEdit.nom });
        setIsEditing(true);
        setIsModalOpen(true);
    }

    function handleAdd() {
        if (!isAdmin) return;
        setCurrentEncrage(null);
        setFormData({ nom: "" });
        setIsEditing(false);
        setIsModalOpen(true);
    }

    const columns = [
        {
            header: "Nom de l'encrage",
            accessorKey: "nom" as keyof Encrage,
        },
        // Ajoutez d'autres colonnes si nécessaire
    ];

    return (
        <div className=" mx-auto px-4 py-8 ">
            {/* Section des Statistiques d'Encrage */}
            {encrageStats.length > 0 && (
                <div className="mb-10">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {encrageStats.map((stat) => {
                            const percentage =
                                stat.totalCas > 0
                                    ? (stat.casRegularises / stat.totalCas) *
                                      100
                                    : 0;
                            let progressBarColorClass = "bg-green-500"; // Vert par défaut

                            if (percentage === 0) {
                                progressBarColorClass = "bg-red-500";
                            } else if (percentage < 50) {
                                progressBarColorClass = "bg-red-500";
                            } else if (percentage < 100) {
                                progressBarColorClass = "bg-orange-500";
                            }

                            return (
                                <div
                                    key={stat.id}
                                    className="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between min-h-[200px] pb-6"
                                >
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-800 mb-2">
                                            {stat.nom}
                                        </h3>
                                        <div className="space-y-2">
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600">
                                                    Total des cas:
                                                </span>
                                                <span className="font-medium text-primary-600">
                                                    {stat.totalCas}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600">
                                                    Cas régularisés:
                                                </span>
                                                <span className="font-medium text-green-600">
                                                    {stat.casRegularises}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600">
                                                    En attente:
                                                </span>
                                                <span className="font-medium text-orange-600">
                                                    {stat.totalCas -
                                                        stat.casRegularises}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    {/* Progress bar toujours présente, même à 0 */}
                                    <div className="mt-6">
                                        <div className="flex justify-between mb-1">
                                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Progression
                                            </span>
                                            <span
                                                className={`text-sm font-medium ${
                                                    percentage === 0
                                                        ? "text-red-700 dark:text-red-500"
                                                        : percentage < 50
                                                        ? "text-red-700 dark:text-red-500"
                                                        : percentage < 100
                                                        ? "text-orange-700 dark:text-orange-500"
                                                        : "text-green-700 dark:text-green-500"
                                                }`}
                                            >
                                                {percentage.toFixed(0)}%
                                            </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                            <div
                                                className={`${progressBarColorClass} h-2.5 rounded-full transition-all duration-300`}
                                                style={{
                                                    width: `${percentage}%`,
                                                }}
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}
            {/* Fin Section des Statistiques d'Encrage */}

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 mt-8 gap-4">
                <h1 className="sm:flex sm:items-center text-2xl font-semibold text-foreground">
                    Gestion des Encrages
                </h1>
                {isAdmin && (
                    <Button onClick={handleAdd} title="Ajouter un encrage">
                        {/* Icône Ajouter */}
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="w-5 h-5"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M12 4.5v15m7.5-7.5h-15"
                            />
                        </svg>
                    </Button>
                )}
            </div>

            {error && <FormError message={error} />}

            <div className="overflow-x-auto">
                <Table
                    data={encrages}
                    columns={columns}
                    actions={
                        isAdmin
                            ? (row: Encrage) => (
                                  <div className="flex justify-center items-center space-x-1 sm:space-x-2">
                                      <button
                                          onClick={() => handleEdit(row)}
                                          title="Modifier l'encrage"
                                          className="p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150"
                                      >
                                          {/* Icône Modifier */}
                                          <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              fill="none"
                                              viewBox="0 0 24 24"
                                              strokeWidth={1.5}
                                              stroke="currentColor"
                                              className="w-5 h-5"
                                          >
                                              <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                                              />
                                          </svg>
                                      </button>
                                      <button
                                          onClick={() => handleDelete(row)}
                                          title="Supprimer l'encrage"
                                          className="p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150"
                                      >
                                          {/* Icône Supprimer */}
                                          <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              fill="none"
                                              viewBox="0 0 24 24"
                                              strokeWidth={1.5}
                                              stroke="currentColor"
                                              className="w-5 h-5"
                                          >
                                              <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                              />
                                          </svg>
                                      </button>
                                  </div>
                              )
                            : undefined
                    }
                />

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="flex justify-center items-center p-4 border-t">
                        <Button
                            variant="outline"
                            onClick={() => goToPage(page - 1)}
                            disabled={page === 1}
                        >
                            Précédent
                        </Button>
                        <span className="mx-4">
                            Page {page} sur {totalPages}
                        </span>
                        <Button
                            variant="outline"
                            onClick={() => goToPage(page + 1)}
                            disabled={page === totalPages}
                        >
                            Suivant
                        </Button>
                    </div>
                )}
            </div>

            {isAdmin &&
                isModalOpen && ( // Le modal ne s'ouvre que si admin, ou si on veut juste l'afficher
                    <Modal
                        isOpen={isModalOpen}
                        onClose={() => setIsModalOpen(false)}
                        title={
                            isEditing
                                ? "Modifier l'encrage"
                                : "Ajouter un encrage"
                        }
                    >
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <Input
                                id="nom"
                                label="Nom de l'encrage"
                                value={formData.nom}
                                onChange={(e) =>
                                    setFormData({
                                        ...formData,
                                        nom: e.target.value,
                                    })
                                }
                                required
                            />
                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setIsModalOpen(false)}
                                    type="button"
                                >
                                    Annuler
                                </Button>
                                <Button type="submit" isLoading={isLoading}>
                                    {isEditing ? "Modifier" : "Ajouter"}
                                </Button>
                            </div>
                        </form>
                    </Modal>
                )}
        </div>
    );
}
