import "./globals.css";
import type { Metadata } from "next";
import { Navigation } from "./components/Navigation";
import { Footer } from "./components/Footer";
import AnnouncementBanner from "./components/AnnouncementBanner";
import { cookies } from "next/headers";
import { verifyToken } from "../lib/auth";
import { DataRefreshProvider } from "./contexts/DataRefreshContext";
// import { AuthProvider } from '@/contexts/AuthProvider'; // Removed import
// import { ThemeProvider } from '@/contexts/ThemeProvider'; // Removed import
// import { redirect } from 'next/navigation'; // Not used in this file, can be removed if not needed elsewhere

// NOTE: AuthProvider and ThemeProvider are used below.
// Please ensure they are correctly imported. For example:
// import { AuthProvider } from '@/contexts/AuthProvider'; // Example path
// import { ThemeProvider } from '@/contexts/ThemeProvider'; // Example path
// If they are not yet created or imported, you will need to add them.

import { Poppins } from "next/font/google"; // Exemple avec Poppins

const poppins = Poppins({
    subsets: ["latin"],
    weight: ["300", "400", "500", "600", "700"], // Spécifiez les graisses nécessaires
});

export const metadata: Metadata = {
    title: "Application Assainissement ",
    description: "Gestion des cas d'assainissement",
};

async function getUser() {
    const cookieStore = await cookies(); // Await the cookies() call
    const token = cookieStore.get("token")?.value;
    if (!token) return null;

    // Ensure verifyToken can handle null or malformed tokens gracefully
    try {
        const decoded = await verifyToken(token); // Assuming verifyToken is async
        return decoded;
    } catch (error) {
        console.error("Token verification failed:", error);
        return null;
    }
}

// Single RootLayout definition
export default async function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const user = await getUser();

    return (
        <html lang="fr">
            <body
                className={`${poppins.className} bg-slate-50 text-slate-800 min-h-screen flex flex-col w-full`}
            >
                <DataRefreshProvider>
                    <Navigation user={user} />
                    <AnnouncementBanner />
                    <main className="max-w-screen-4xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 2xl:px-12 py-2 mt-24 sm:mt-32 flex-grow w-full">
                        {children}
                    </main>
                    {user && <Footer />}
                </DataRefreshProvider>
            </body>
        </html>
    );
}
// All duplicated content below this line has been removed.
