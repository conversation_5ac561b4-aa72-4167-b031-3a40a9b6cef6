(()=>{var e={};e.id=8591,e.ids=[8591],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17960:(e,s,t)=>{Promise.resolve().then(t.bind(t,50632))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32038:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(60687),a=t(43210),l=t(69266);function n(){let[e,s]=(0,a.useState)(!1),[t,n]=(0,a.useState)(!1),[o,i]=(0,a.useState)([]),[c,d]=(0,a.useState)(null),[u,x]=(0,a.useState)(null),[m,g]=(0,a.useState)(null),[p,h]=(0,a.useState)(null),[b,f]=(0,a.useState)(5e4),[y,j]=(0,a.useState)(!1),N=async()=>{j(!0),h(null);try{console.log("\uD83E\uDDEA Test des APIs de statistiques...");let e=await (0,l.Zq)("/api/admin/test-stats-apis");if(e.success){console.log("✅ Test APIs statistiques r\xe9ussi:",e.results);let s=e.results,t=s.filter(e=>e.success).length;s.filter(e=>!e.success).length;let r=`✅ Test APIs statistiques termin\xe9 !

`;r+=`📊 ${t}/${s.length} APIs fonctionnelles
📈 Donn\xe9es de base: ${e.baseCounts.cas} cas, ${e.baseCounts.blocages} blocages
⏱️ Dur\xe9e: ${e.performance.duration}ms

D\xe9tails par API:
`,s.forEach(e=>{e.success?r+=`✅ ${e.api}: ${e.dataCount} \xe9l\xe9ments
`:r+=`❌ ${e.api}: ${e.error}
`}),alert(r),e.baseCounts&&x(e.baseCounts)}else h(`Test \xe9chou\xe9: ${e.error}
D\xe9tails: ${e.details||"Aucun d\xe9tail"}`)}catch(e){console.error("Erreur lors du test:",e),h(`Test APIs statistiques \xe9chou\xe9: ${e.message}`)}finally{j(!1)}},v=async()=>{j(!0),h(null);try{console.log("\uD83E\uDDEA Test des statistiques simples...");let e=await (0,l.Zq)("/api/test-simple-stats");if(e.success){console.log("✅ Test statistiques simples r\xe9ussi:",e.stats);let s=e.stats;alert(`✅ Test statistiques simples r\xe9ussi !

📊 Total cas: ${s.totalCas.toLocaleString()}
✅ R\xe9gularis\xe9s: ${s.regularises.toLocaleString()}
🚧 Total blocages: ${s.totalBlocages.toLocaleString()}
⏱️ Dur\xe9e: ${e.performance.duration}ms
⏰ ${e.performance.timestamp}`),s&&x({cas:s.totalCas,blocages:s.totalBlocages,secteurs:0,problematiques:0,users:0})}else h(`Test \xe9chou\xe9: ${e.error}
D\xe9tails: ${e.details||"Aucun d\xe9tail"}`)}catch(e){console.error("Erreur lors du test:",e),h(`Test statistiques \xe9chou\xe9: ${e.message}`)}finally{j(!1)}},T=async()=>{j(!0),h(null);try{console.log("\uD83D\uDD0D Lancement du diagnostic complet...");let e=await (0,l.Zq)("/api/admin/debug-api");if(e.success){console.log("✅ Diagnostic complet r\xe9ussi:",e.results);let s=e.results,t=s.database.counts;alert(`✅ Diagnostic complet r\xe9ussi !

👤 Utilisateur: ${s.user.username} (${s.user.role})
🗄️ Base de donn\xe9es: Connect\xe9e
📊 Donn\xe9es: ${t.cas.toLocaleString()} cas, ${t.blocages.toLocaleString()} blocages
🔗 Tests: JOIN OK, SQL brut OK
⏰ ${e.timestamp}`),s.database.counts&&x(s.database.counts)}else{let s=`❌ Diagnostic \xe9chou\xe9 \xe0 l'\xe9tape: ${e.step}

Erreur: ${e.error}
D\xe9tails: ${e.details||"Aucun d\xe9tail"}`;console.error("❌ Diagnostic \xe9chou\xe9:",e),h(s),alert(s)}}catch(s){console.error("Erreur lors du diagnostic:",s);let e=`💥 Erreur fatale lors du diagnostic:

${s.message}`;h(e),alert(e)}finally{j(!1)}},w=async()=>{j(!0),h(null);try{console.log("\uD83E\uDDEA Test de g\xe9n\xe9ration r\xe9elle (100 cas)...");let e=await (0,l.Zq)("/api/admin/generate-real-data",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({testCount:100})});e.success?(x(e.totals),console.log("✅ G\xe9n\xe9ration r\xe9elle r\xe9ussie:",e.message),alert(`✅ G\xe9n\xe9ration r\xe9elle r\xe9ussie !

📊 ${e.created.cas} cas cr\xe9\xe9s
🚧 ${e.created.blocages} blocages cr\xe9\xe9s
📈 Total: ${e.totals.cas.toLocaleString()} cas dans la base`)):h(`G\xe9n\xe9ration \xe9chou\xe9e: ${e.message||"Erreur inconnue"}`)}catch(e){console.error("Erreur lors de la g\xe9n\xe9ration:",e),h(`Test de g\xe9n\xe9ration \xe9chou\xe9: ${e.message}`)}finally{j(!1)}},C=async()=>{j(!0),h(null);try{console.log("\uD83D\uDD0D Test de connexion...");let e=await (0,l.Zq)("/api/admin/simple-test");e.success?(console.log("✅ Connexion r\xe9ussie:",e.message),alert(`✅ Test de connexion r\xe9ussi !

👤 Utilisateur: ${e.user.username} (${e.user.role})
⏰ Timestamp: ${e.timestamp}`)):h(`Test \xe9chou\xe9: ${e.message||"Erreur inconnue"}`)}catch(e){console.error("Erreur lors du test:",e),h(`Test de connexion \xe9chou\xe9: ${e.message}`)}finally{j(!1)}},D=async()=>{j(!0),h(null);try{console.log("\uD83E\uDDEA Test de g\xe9n\xe9ration (10 cas)...");let e=await (0,l.Zq)("/api/admin/simple-test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({testCount:10,action:"test-generation"})});e.success?(console.log("✅ Test POST r\xe9ussi:",e.message),alert(`✅ Test POST r\xe9ussi !

� Utilisateur: ${e.user}
📦 Donn\xe9es re\xe7ues: ${JSON.stringify(e.received)}
⏰ ${e.timestamp}`)):h(`Test \xe9chou\xe9: ${e.message||"Erreur inconnue"}`)}catch(e){console.error("Erreur lors du test:",e),h(`Test de g\xe9n\xe9ration \xe9chou\xe9: ${e.message}`)}finally{j(!1)}},$=async()=>{n(!0),h(null);try{console.log(`🚀 G\xe9n\xe9ration de ${b.toLocaleString()} cas de test...`);let e=await (0,l.Zq)("/api/admin/generate-test-data",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({targetCas:b})});e.success&&(x(e.totals),console.log(`✅ G\xe9n\xe9ration termin\xe9e: ${e.created.cas} cas, ${e.created.blocages} blocages cr\xe9\xe9s`),alert(`🎉 G\xe9n\xe9ration r\xe9ussie !

📊 ${e.created.cas.toLocaleString()} cas cr\xe9\xe9s
🚧 ${e.created.blocages.toLocaleString()} blocages cr\xe9\xe9s
📦 ${e.batches} batches trait\xe9s`))}catch(s){console.error("Erreur lors de la g\xe9n\xe9ration:",s),console.error("D\xe9tails de l'erreur:",{message:s.message,status:s.status,response:s.response});let e="Erreur lors de la g\xe9n\xe9ration des donn\xe9es de test";s.message?.includes("401")?e="Erreur d'authentification. Veuillez vous reconnecter.":s.message?.includes("403")?e="Acc\xe8s refus\xe9. Vous devez \xeatre administrateur.":s.message?.includes("400")?e="Param\xe8tres invalides. V\xe9rifiez le nombre de cas.":s.message&&(e=s.message),h(e)}finally{n(!1)}},P=async(e="all")=>{s(!0),h(null),i([]),d(null);try{console.log(`🚀 Lancement des tests de performance: ${e}`);let s=await (0,l.Zq)(`/api/admin/performance-test?type=${e}`);i(s.results),d(s.summary),x(s.database),g(s.analysis),console.log(`✅ Tests termin\xe9s: ${s.summary.successfulTests}/${s.summary.totalTests} r\xe9ussis`)}catch(e){console.error("Erreur lors des tests:",e),h(e.message||"Erreur lors des tests de performance")}finally{s(!1)}},S=e=>e<1e3?"text-green-600":e<5e3?"text-yellow-600":e<1e4?"text-orange-600":"text-red-600",q=e=>e<1e3?"\uD83D\uDE80":e<5e3?"⚡":e<1e4?"⚠️":"\uD83D\uDC0C";return(0,r.jsx)("div",{className:" mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"\uD83D\uDE80 Tests de Performance"}),u&&(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-blue-900 mb-4",children:"\uD83D\uDCCA Base de donn\xe9es"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.cas.toLocaleString()}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"Cas"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.blocages.toLocaleString()}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"Blocages"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.secteurs}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"Secteurs"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.problematiques}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"Probl\xe9matiques"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.users}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"Utilisateurs"})]})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-yellow-900 mb-4",children:"\uD83D\uDD0D Tests de diagnostic"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 mb-4",children:[(0,r.jsx)("button",{onClick:C,disabled:y||t||e,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:y?"\uD83D\uDD04 Test...":"\uD83D\uDD0D Test Auth"}),(0,r.jsx)("button",{onClick:D,disabled:y||t||e,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:y?"\uD83D\uDD04 Test...":"\uD83E\uDDEA Test API POST"}),(0,r.jsx)("button",{onClick:w,disabled:y||t||e,className:"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:y?"\uD83D\uDD04 Test...":"\uD83D\uDE80 Test G\xe9n\xe9ration (100 cas)"}),(0,r.jsx)("button",{onClick:N,disabled:y||t||e,className:"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:y?"\uD83D\uDD04 Test...":"\uD83D\uDCCA Test APIs Stats"}),(0,r.jsx)("button",{onClick:v,disabled:y||t||e,className:"bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:y?"\uD83D\uDD04 Test...":"\uD83E\uDDEA Test Stats Simples"}),(0,r.jsx)("button",{onClick:T,disabled:y||t||e,className:"bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:y?"\uD83D\uDD04 Test...":"\uD83D\uDD0D Diagnostic Complet"})]}),(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:"Utilisez ces tests pour diagnostiquer les probl\xe8mes avant la g\xe9n\xe9ration compl\xe8te."})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83D\uDCCA G\xe9n\xe9ration de donn\xe9es de test"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Nombre de cas \xe0 g\xe9n\xe9rer:"}),(0,r.jsx)("input",{type:"number",value:b,onChange:e=>f(parseInt(e.target.value)||5e4),min:"1000",max:"100000",step:"1000",className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-24",disabled:t})]}),(0,r.jsx)("button",{onClick:$,disabled:t||e,className:"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:t?"\uD83D\uDD04 G\xe9n\xe9ration en cours...":"\uD83D\uDCCA G\xe9n\xe9rer les donn\xe9es"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"G\xe9n\xe8re des cas fictifs avec leurs contraintes associ\xe9es pour tester les performances. Chaque cas aura 1-3 contraintes avec des r\xe9solutions vari\xe9es."})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83E\uDDEA Lancer les tests de performance"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)("button",{onClick:()=>P("all"),disabled:e,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:e?"\uD83D\uDD04 Tests en cours...":"\uD83D\uDE80 Tous les tests"}),(0,r.jsx)("button",{onClick:()=>P("dashboard"),disabled:e,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"\uD83D\uDCCA Dashboard"}),(0,r.jsx)("button",{onClick:()=>P("listing"),disabled:e,className:"bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"\uD83D\uDCCB Listing"}),(0,r.jsx)("button",{onClick:()=>P("stats"),disabled:e,className:"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"\uD83D\uDCC8 Statistiques"})]})]}),c&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83D\uDCCB R\xe9sum\xe9 des tests"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-6 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-gray-600",children:[Math.round(c.totalDuration),"ms"]}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"Dur\xe9e totale"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:c.totalTests}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"Tests total"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:c.successfulTests}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"R\xe9ussis"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:c.failedTests}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"\xc9chou\xe9s"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:c.slowTests}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"Lents (5-10s)"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:c.verySlowTests}),(0,r.jsx)("div",{className:"text-sm text-gray-700",children:"Tr\xe8s lents (>10s)"})]})]})]}),m&&(0,r.jsxs)("div",{className:`border rounded-lg p-6 mb-6 ${"excellent"===m.performance?"bg-green-50 border-green-200":"good"===m.performance?"bg-yellow-50 border-yellow-200":"bg-red-50 border-red-200"}`,children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"excellent"===m.performance?"\uD83D\uDE80 Performance Excellente":"good"===m.performance?"⚡ Performance Correcte":"\uD83D\uDC0C Performance \xe0 Optimiser"}),m.recommendations.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCA1 Recommandations:"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-1",children:m.recommendations.map((e,s)=>(0,r.jsx)("li",{className:"text-sm",children:e},s))})]})]}),p&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-red-900 mb-2",children:"❌ Erreur"}),(0,r.jsx)("p",{className:"text-red-700",children:p})]}),o.length>0&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"\uD83D\uDCCA R\xe9sultats d\xe9taill\xe9s"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Dur\xe9e"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"R\xe9sultats"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"M\xe9moire"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Statut"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map((e,s)=>(0,r.jsxs)("tr",{className:e.success?"":"bg-red-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium ${S(e.duration)}`,children:[q(e.duration)," ",e.duration,"ms"]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.resultCount?.toLocaleString()||"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.memoryUsage?`${Math.round(e.memoryUsage.heapUsed/1024/1024)}MB`:"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.success?(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"✅ R\xe9ussi"}):(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"❌ \xc9chou\xe9"})})]},s))})]})})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},47173:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),o=t(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let c={children:["",{children:["admin",{children:["performance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,50632)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\admin\\performance\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\admin\\performance\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/performance/page",pathname:"/admin/performance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},50632:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\admin\\\\performance\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\admin\\performance\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69266:(e,s,t)=>{"use strict";async function r(e,s={}){let{method:t="GET",body:a}=s;console.log(`Making ${t} request to ${e}`),a&&console.log("Request body:",a);let l=await fetch(e,{method:t,headers:{"Content-Type":"application/json",...s.headers},credentials:s.credentials||"include",body:a?JSON.stringify(a):void 0});if(console.log("Response status:",l.status),!l.ok){let e=`HTTP error! status: ${l.status}`,s=null;try{let t=await l.text();if(console.log("Error response text:",t),t)try{s=JSON.parse(t),e=s?.error||s?.message||t}catch(s){e=t||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===l.status)throw Error("Authentication required. Please log in again.");if(403===l.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===l.status)throw Error("Resource not found.");else if(l.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===l.status)return null;let n=await l.json();return console.log("Response data:",n),n}t.d(s,{Zq:()=>r,uE:()=>a});let a={get:(e,s)=>r(e,{...s,method:"GET"}),post:(e,s,t)=>r(e,{...t,method:"POST",body:s}),put:(e,s,t)=>r(e,{...t,method:"PUT",body:s}),patch:(e,s,t)=>r(e,{...t,method:"PATCH",body:s}),delete:(e,s,t)=>r(e,{...t,method:"DELETE",body:s})}},79428:e=>{"use strict";e.exports=require("buffer")},81512:(e,s,t)=>{Promise.resolve().then(t.bind(t,32038))},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[7719,3903,5262,2797],()=>t(47173));module.exports=r})();