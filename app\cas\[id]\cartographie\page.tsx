"use client";

export const dynamic = "force-dynamic";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { fetchApi } from "@/lib/api-client";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { FormError } from "@/app/components/FormError";
import MapComponent from "@/app/components/MapComponentV2";

interface Cas {
    id: string;
    nom: string;
    superficie: number;
    observation?: string;
    geojson?: any;
    kmlData?: any;
    kmlFileName?: string;
    regularisation: boolean;
    communes: Array<{
        id: string;
        nom: string;
    }>;
    problematique?: {
        id: string;
        problematique: string;
    };
}

export default function CasCartographiePage() {
    const params = useParams();
    const casId = params.id as string;

    const [cas, setCas] = useState<Cas | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [mapCenter, setMapCenter] = useState<[number, number]>([36.75, 3.06]); // Centre par défaut (Alger)

    useEffect(() => {
        loadCasData();
    }, [casId]);

    const loadCasData = async () => {
        try {
            setIsLoading(true);
            const data = await fetchApi<Cas>(`/api/cas/${casId}`);
            setCas(data);

            // Si le cas a des coordonnées GeoJSON, centrer la carte dessus
            if (data?.geojson?.coordinates) {
                console.log(
                    "Coordonnées GeoJSON trouvées:",
                    data.geojson.coordinates
                );

                // Vérifier que les coordonnées sont valides
                if (
                    Array.isArray(data.geojson.coordinates) &&
                    data.geojson.coordinates.length === 2 &&
                    typeof data.geojson.coordinates[0] === "number" &&
                    typeof data.geojson.coordinates[1] === "number"
                ) {
                    const [lng, lat] = data.geojson.coordinates;
                    console.log("Centre de la carte défini à:", [lat, lng]);
                    setMapCenter([lat, lng]);
                } else {
                    console.warn(
                        "Coordonnées GeoJSON invalides, utilisation du centre par défaut"
                    );
                }
            }
        } catch (err) {
            console.error("Erreur lors du chargement du cas:", err);
            setError("Erreur lors du chargement des données du cas");
        } finally {
            setIsLoading(false);
        }
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-96">
                <LoadingSpinner />
            </div>
        );
    }

    if (error) {
        return <FormError message={error} />;
    }

    if (!cas) {
        return <FormError message="Cas non trouvé" />;
    }

    return (
        <div className=" mx-auto px-4 py-8">
            <div className="mb-6">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                    Cartographie - {cas.nom}
                </h1>
                <div className="bg-white p-4 rounded-lg shadow-sm border">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span className="font-medium text-gray-600">
                                Superficie:
                            </span>
                            <span className="ml-2">{cas.superficie} Ha</span>
                        </div>
                        <div>
                            <span className="font-medium text-gray-600">
                                Problématique:
                            </span>
                            <span className="ml-2">
                                {cas.problematique?.problematique ||
                                    "Non définie"}
                            </span>
                        </div>
                        <div>
                            <span className="font-medium text-gray-600">
                                Communes:
                            </span>
                            <span className="ml-2">
                                {cas.communes.map((c) => c.nom).join(", ")}
                            </span>
                        </div>
                    </div>
                    {cas.observation && (
                        <div className="mt-3 pt-3 border-t">
                            <span className="font-medium text-gray-600">
                                Observation:
                            </span>
                            <p className="mt-1 text-gray-700">
                                {cas.observation}
                            </p>
                        </div>
                    )}
                </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <MapComponent
                    casList={cas ? [cas] : []}
                    selectedCas={cas}
                    setSelectedCas={() => {}}
                    center={mapCenter}
                    zoom={13}
                    height="600px"
                />
            </div>

            {!cas.geojson && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-yellow-800">
                        <strong>Aucune donnée géographique disponible</strong>{" "}
                        pour ce cas. Les coordonnées ou le fichier GeoJSON n'ont
                        pas été fournis.
                    </p>
                </div>
            )}
        </div>
    );
}
