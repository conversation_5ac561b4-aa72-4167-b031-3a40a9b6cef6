"use client";

import React, { useEffect, useRef, useState } from "react";
import wilayaData from "@/app/data/wilaya.json";
import communesData from "@/app/data/communes.json";

interface CasMapProps {
    geojsonData: any;
    casId: string;
    onMapReady?: () => void;
}

const CasMap: React.FC<CasMapProps> = ({ geojsonData, casId, onMapReady }) => {
    const mapRef = useRef<HTMLDivElement>(null);
    const leafletMapRef = useRef<any>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        let isMounted = true;

        const initializeMap = async () => {
            try {
                if (!mapRef.current) return;

                // Nettoyer la carte existante si elle existe
                if (leafletMapRef.current) {
                    try {
                        leafletMapRef.current.remove();
                        leafletMapRef.current = null;
                    } catch (e) {
                        console.warn(
                            "Erreur lors du nettoyage de la carte:",
                            e
                        );
                    }
                }

                // Vider le conteneur
                if (mapRef.current) {
                    mapRef.current.innerHTML = "";
                }

                // Import dynamique de Leaflet
                const L = await import("leaflet");

                // Configurer les icônes par défaut de Leaflet
                delete (L.Icon.Default.prototype as any)._getIconUrl;
                L.Icon.Default.mergeOptions({
                    iconRetinaUrl:
                        "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
                    iconUrl:
                        "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
                    shadowUrl:
                        "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
                });

                if (!isMounted || !mapRef.current) return;

                // Calculer le centre de la carte
                const getMapCenter = (): [number, number] => {
                    if (geojsonData && geojsonData.coordinates) {
                        try {
                            if (
                                geojsonData.type === "Polygon" &&
                                geojsonData.coordinates[0]
                            ) {
                                const firstCoord =
                                    geojsonData.coordinates[0][0];
                                return [firstCoord[1], firstCoord[0]]; // [lat, lng]
                            }
                        } catch (error) {
                            console.warn(
                                "Error calculating center from GeoJSON:",
                                error
                            );
                        }
                    }
                    return [36.75, 3.06]; // Centre par défaut (Algérie)
                };

                const mapCenter = getMapCenter();
                const zoom = geojsonData ? 12 : 8;

                // Créer la carte
                const map = L.map(mapRef.current).setView(mapCenter, zoom);

                // Ajouter la couche de tuiles
                L.tileLayer(
                    "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                    {
                        attribution:
                            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    }
                ).addTo(map);

                // Ajouter la couche des wilayas
                const wilayaLayer = L.geoJSON(wilayaData as any, {
                    style: {
                        color: "#ff7800",
                        weight: 2,
                        opacity: 0.8,
                        fillOpacity: 0.1,
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties) {
                            const popupContent = `
                                <div style="min-width: 200px;">
                                    <h4 style="font-weight: bold; margin-bottom: 8px;">Wilaya ${feature.properties.id}</h4>
                                    <p style="margin: 4px 0;"><strong>Nom français:</strong> ${feature.properties.name_fr}</p>
                                    <p style="margin: 4px 0;"><strong>Nom arabe:</strong> ${feature.properties.name_ar}</p>
                                </div>
                            `;
                            layer.bindPopup(popupContent);
                        }
                    },
                });

                // Ajouter la couche des communes
                const communesLayer = L.geoJSON(communesData as any, {
                    style: {
                        color: "#0066cc",
                        weight: 1,
                        opacity: 0.7,
                        fillOpacity: 0.05,
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties) {
                            const popupContent = `
                                <div style="min-width: 200px;">
                                    <h4 style="font-weight: bold; margin-bottom: 8px;">Commune</h4>
                                    <p style="margin: 4px 0;"><strong>Nom français:</strong> ${
                                        feature.properties.name_fr
                                    }</p>
                                    <p style="margin: 4px 0;"><strong>Nom arabe:</strong> ${
                                        feature.properties.name_ar
                                    }</p>
                                    <p style="margin: 4px 0;"><strong>Superficie:</strong> ${feature.properties.SHAPE_Area?.toFixed(
                                        2
                                    )} km²</p>
                                </div>
                            `;
                            layer.bindPopup(popupContent);
                        }
                    },
                });

                // Ajouter le contrôle des couches
                const overlays = {
                    Wilayas: wilayaLayer,
                    Communes: communesLayer,
                };
                L.control.layers({}, overlays).addTo(map);

                // Ajouter la couche par défaut
                wilayaLayer.addTo(map);

                // Ajouter les données GeoJSON si disponibles
                if (geojsonData) {
                    const geoJsonLayer = L.geoJSON(geojsonData, {
                        style: {
                            color: "#3388ff",
                            weight: 3,
                            opacity: 0.8,
                            fillOpacity: 0.2,
                        },
                    }).addTo(map);

                    // Ajuster la vue pour inclure toute la géométrie
                    try {
                        map.fitBounds(geoJsonLayer.getBounds(), {
                            padding: [10, 10],
                        });
                    } catch (e) {
                        console.warn("Impossible d'ajuster les limites:", e);
                    }
                }

                leafletMapRef.current = map;

                if (isMounted) {
                    setIsLoading(false);
                    console.log(
                        "Carte prête pour le cas:",
                        casId,
                        "avec données:",
                        !!geojsonData
                    );
                    if (onMapReady) {
                        setTimeout(() => onMapReady(), 100);
                    }
                }
            } catch (error) {
                console.error(
                    "Erreur lors de l'initialisation de la carte:",
                    error
                );
                if (isMounted) {
                    setError("Erreur lors du chargement de la carte");
                    setIsLoading(false);
                }
            }
        };

        initializeMap();

        // Fonction de nettoyage
        return () => {
            isMounted = false;
            if (leafletMapRef.current) {
                try {
                    leafletMapRef.current.remove();
                    leafletMapRef.current = null;
                } catch (e) {
                    console.warn("Erreur lors du nettoyage de la carte:", e);
                }
            }
        };
    }, [casId, geojsonData, onMapReady]);

    if (error) {
        return (
            <div className="w-full h-[300px] bg-red-50 rounded flex items-center justify-center">
                <span className="text-red-500">{error}</span>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="w-full h-[300px] bg-gray-200 animate-pulse rounded flex items-center justify-center">
                <span className="text-gray-500">Chargement de la carte...</span>
            </div>
        );
    }

    // Si pas de données GeoJSON, afficher un message informatif
    if (!geojsonData) {
        return (
            <div className="w-full h-[300px] bg-gray-50 rounded-lg flex items-center justify-center border border-gray-200">
                <div className="text-center text-gray-500">
                    <svg
                        className="w-12 h-12 mx-auto mb-3 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                        />
                    </svg>
                    <p className="font-medium">Aucune géométrie disponible</p>
                    <p className="text-sm mt-1">
                        Ce cas n'a pas de données cartographiques
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full h-[300px] rounded overflow-hidden">
            <div
                ref={mapRef}
                className="w-full h-full"
                key={`leaflet--${casId}`}
            />
        </div>
    );
};

export default CasMap;
