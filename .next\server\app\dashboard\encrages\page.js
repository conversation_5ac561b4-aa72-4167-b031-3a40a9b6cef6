(()=>{var e={};e.id=264,e.ids=[264],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15654:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(60687),a=s(47726);function n({children:e}){let{isCollapsed:t}=(0,a.c)();return(0,r.jsx)("div",{className:"space-y-0 pt-1 md:pt-1 transition-all duration-300 w-full",style:{minHeight:"100vh"},children:e})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27395:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>b});var r=s(60687),a=s(85814),n=s.n(a),i=s(16189),o=s(47726),l=s(20816),d=s(55510),c=s(10799),m=s(72871),u=s(45994),x=s(87061),p=s(37132),h=s(43655),g=s(66524),f=s(91028);function b({user:e}){let t=(0,i.usePathname)(),{isCollapsed:s,setIsCollapsed:a}=(0,o.c)(),b=[{name:"Tableau de bord",href:"/dashboard",icon:l.A},{name:"Gestion Dossiers",href:"/dashboard/cas",icon:d.A},{name:"Cartographie",href:"/dashboard/cartographie",icon:c.A},{name:"R\xe9glementation",href:"/dashboard/reglementation",icon:m.A},{name:"Statistiques",href:"/dashboard/statistiques",icon:u.A},...e?.role==="ADMIN"?[{name:"Utilisateurs",href:"/users",icon:x.A}]:[]];return e?(0,r.jsx)("div",{className:`h-[calc(100vh-2rem)] bg-gradient-to-b   from-sky-900 to-indigo-900/90 text-white flex flex-col transition-all duration-300 shadow-2xl rounded-r-3xl backdrop-blur-md border-r border-indigo-200/30 z-30 ${s?"w-16":"w-56"}`,children:(0,r.jsxs)("div",{className:"flex flex-col h-full ",children:[(0,r.jsx)("div",{className:"p-1 font-extrabold text-xl tracking-wide border-b border-indigo-700 flex items-center gap-2",children:!s&&(0,r.jsx)("span",{className:"bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent drop-shadow-lg",children:"Assainissement"})}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-indigo-800",children:[!s&&(0,r.jsx)("div",{className:"text-base font-semibold text-white",children:"Menu"}),(0,r.jsx)("button",{onClick:()=>a(!s),className:"p-2 rounded-md text-indigo-200 hover:bg-indigo-700 hover:text-white focus:outline-none",children:s?(0,r.jsx)(p.A,{className:"w-7 h-7"}):(0,r.jsx)(h.A,{className:"w-7 h-7"})})]}),(0,r.jsxs)("div",{className:"flex-grow p-0 overflow-y-auto",children:[(0,r.jsxs)("div",{className:`mb-4 pb-2 border-b border-indigo-800 ${s?"hidden":"block"}`,children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"h-12 w-12 rounded-full bg-gradient-to-r from-sky-400 to-indigo-500 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})}),!s&&(0,r.jsxs)("div",{className:"ml-2",children:[(0,r.jsx)("div",{className:"font-semibold text-white text-sm",children:e?.username||"Utilisateur"}),(0,r.jsxs)("div",{className:"text-[11px] text-indigo-200 flex items-center",children:[(0,r.jsx)("span",{className:`inline-block w-2 h-2 rounded-full mr-1.5 ${e?.role==="ADMIN"?"bg-red-400":e?.role==="EDITOR"?"bg-green-400":e?.role==="VIEWER"?"bg-gray-400":"bg-blue-400"}`}),e?.role||"BASIC",e?.role==="VIEWER"&&(0,r.jsx)(g.A,{className:"h-3 w-3 ml-1 text-orange-300",title:"Mode lecture seule"})]})]})]})}),s&&(0,r.jsx)("div",{className:"mt-2 flex flex-col items-center space-y-2",children:(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-gradient-to-r from-sky-40.0 to-indigo-500 flex items-center justify-center text-white font-bold text-sm shadow-lg border-2 border-white",children:e?.username?e.username.charAt(0).toUpperCase():"?"})})})]}),(0,r.jsx)("nav",{children:(0,r.jsx)("ul",{className:"space-y-2",children:b.map(e=>{let a="/dashboard"===e.href?"/dashboard"===t:t===e.href||t.startsWith(`${e.href}/`),i=e.icon;return(0,r.jsx)("li",{children:(0,r.jsxs)(n(),{href:e.href,title:e.name,className:`flex items-center p-2 rounded-xl transition-colors duration-150 text-base font-medium gap-2 ${a?"bg-gradient-to-r from-sky-500 to-indigo-500 text-white shadow-md":"text-indigo-100 hover:bg-indigo-700 hover:text-white"} ${s?"justify-center":""}`,children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(i,{className:`w-7 h-7 ${!s?"mr-2":""} drop-shadow-lg`})}),!s&&(0,r.jsx)("div",{className:"flex items-center justify-between flex-1",children:(0,r.jsx)("span",{children:e.name})})]})},e.name)})})})]}),(0,r.jsx)("div",{className:`p-2 border-t border-indigo-800  ${s?"flex justify-center":""}`,children:(0,r.jsxs)("button",{onClick:()=>window.location.href="/api/auth/logout",title:"Se d\xe9connecter",className:`w-full flex items-center p-1 text-base rounded-xl transition-colors duration-150 ${s?"justify-center":""} text-indigo-100 hover:text-white hover:bg-indigo-700 font-semibold gap-2`,children:[(0,r.jsx)(f.A,{className:`w-6 h-6 ${!s?"mr-2":""}`}),!s&&"Se d\xe9connecter"]})})]})}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29228:(e,t,s)=>{Promise.resolve().then(s.bind(s,27395)),Promise.resolve().then(s.bind(s,47726)),Promise.resolve().then(s.bind(s,15654))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38171:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\encrages\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\encrages\\page.tsx","default")},40755:(e,t,s)=>{Promise.resolve().then(s.bind(s,57725))},42380:(e,t,s)=>{Promise.resolve().then(s.bind(s,97021)),Promise.resolve().then(s.bind(s,98440)),Promise.resolve().then(s.bind(s,58508))},47726:(e,t,s)=>{"use strict";s.d(t,{SidebarProvider:()=>o,c:()=>i});var r=s(60687),a=s(43210);let n=(0,a.createContext)({isCollapsed:!1,setIsCollapsed:e=>{}}),i=()=>(0,a.useContext)(n);function o({children:e}){let[t,s]=(0,a.useState)(!1);return(0,r.jsx)(n.Provider,{value:{isCollapsed:t,setIsCollapsed:s},children:e})}},55511:e=>{"use strict";e.exports=require("crypto")},57725:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(43210),n=s(9171),i=s(52643),o=s(99235),l=s(78712),d=s(89679),c=s(69266),m=s(73018);function u(){let{encrages:e,encrageStats:t,isLoading:s,error:u,page:x,totalPages:p,goToPage:h,refresh:g}=(0,m.W)({limit:50,includeStats:!0}),[f,b]=(0,a.useState)(null),[v,j]=(0,a.useState)(!1),[w,N]=(0,a.useState)(!1),[y,C]=(0,a.useState)(null),[S,P]=(0,a.useState)({nom:""}),[k,A]=(0,a.useState)(""),I="ADMIN"===f;async function $(e){if(e.preventDefault(),!I)return void A("Action non autoris\xe9e.");A("");try{let e={nom:S.nom};w&&y?await (0,c.Zq)(`/api/encrages/${y.id}`,{method:"PUT",body:e}):await (0,c.Zq)("/api/encrages",{method:"POST",body:e}),j(!1),g()}catch(e){A(e.message)}finally{}}async function R(e){if(!I)return void A("Action non autoris\xe9e.");if(confirm(`\xcates-vous s\xfbr de vouloir supprimer l'encrage : "${e.nom}" ?`))try{await (0,c.Zq)(`/api/encrages/${e.id}`,{method:"DELETE"}),g()}catch(e){A(e.message)}}return(0,a.useCallback)(async()=>{try{let e=await c.uE.get("/api/auth/session");b(e.role)}catch(e){console.error("Erreur lors du chargement de la session utilisateur:",e),b(null)}},[]),(0,r.jsxs)("div",{className:" mx-auto px-4 py-8 ",children:[t.length>0&&(0,r.jsx)("div",{className:"mb-10",children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map(e=>{let t=e.totalCas>0?e.casRegularises/e.totalCas*100:0,s="bg-green-500";return 0===t||t<50?s="bg-red-500":t<100&&(s="bg-orange-500"),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex flex-col justify-between min-h-[200px] pb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.nom}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total des cas:"}),(0,r.jsx)("span",{className:"font-medium text-primary-600",children:e.totalCas})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Cas r\xe9gularis\xe9s:"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:e.casRegularises})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"En attente:"}),(0,r.jsx)("span",{className:"font-medium text-orange-600",children:e.totalCas-e.casRegularises})]})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Progression"}),(0,r.jsxs)("span",{className:`text-sm font-medium ${0===t||t<50?"text-red-700 dark:text-red-500":t<100?"text-orange-700 dark:text-orange-500":"text-green-700 dark:text-green-500"}`,children:[t.toFixed(0),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700",children:(0,r.jsx)("div",{className:`${s} h-2.5 rounded-full transition-all duration-300`,style:{width:`${t}%`}})})]})]},e.id)})})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 mt-8 gap-4",children:[(0,r.jsx)("h1",{className:"sm:flex sm:items-center text-2xl font-semibold text-foreground",children:"Gestion des Encrages"}),I&&(0,r.jsx)(n.$,{onClick:function(){I&&(C(null),P({nom:""}),N(!1),j(!0))},title:"Ajouter un encrage",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"})})})]}),k&&(0,r.jsx)(d.j,{message:k}),(0,r.jsxs)("div",{className:"overflow-x-auto",children:[(0,r.jsx)(o.X,{data:e,columns:[{header:"Nom de l'encrage",accessorKey:"nom"}],actions:I?e=>(0,r.jsxs)("div",{className:"flex justify-center items-center space-x-1 sm:space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{I&&(C(e),P({nom:e.nom}),N(!0),j(!0))},title:"Modifier l'encrage",className:"p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"})})}),(0,r.jsx)("button",{onClick:()=>R(e),title:"Supprimer l'encrage",className:"p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}):void 0}),p>1&&(0,r.jsxs)("div",{className:"flex justify-center items-center p-4 border-t",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:()=>h(x-1),disabled:1===x,children:"Pr\xe9c\xe9dent"}),(0,r.jsxs)("span",{className:"mx-4",children:["Page ",x," sur ",p]}),(0,r.jsx)(n.$,{variant:"outline",onClick:()=>h(x+1),disabled:x===p,children:"Suivant"})]})]}),I&&v&&(0,r.jsx)(l.a,{isOpen:v,onClose:()=>j(!1),title:w?"Modifier l'encrage":"Ajouter un encrage",children:(0,r.jsxs)("form",{onSubmit:$,className:"space-y-4",children:[(0,r.jsx)(i.p,{id:"nom",label:"Nom de l'encrage",value:S.nom,onChange:e=>P({...S,nom:e.target.value}),required:!0}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:()=>j(!1),type:"button",children:"Annuler"}),(0,r.jsx)(n.$,{type:"submit",isLoading:s,children:w?"Modifier":"Ajouter"})]})]})})]})}},58508:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\MainContentClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\MainContentClient.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68564:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413);function a({children:e}){return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("div",{className:" mx-auto px-2 py-2",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-foreground mb-4",children:"Statistiques par Encrage Juridique"})}),e]})}},72955:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["dashboard",{children:["encrages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38171)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\encrages\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,68564)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\encrages\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,83249)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\dashboard\\encrages\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/encrages/page",pathname:"/dashboard/encrages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73018:(e,t,s)=>{"use strict";s.d(t,{W:()=>n});var r=s(43210),a=s(69266);function n(e={}){let{limit:t=50,includeStats:s=!1,search:i="",includeProblematiques:o=!1,sortBy:l="nom",sortDirection:d="asc",dateFrom:c="",dateTo:m="",status:u=""}=e,[x,p]=(0,r.useState)([]),[h,g]=(0,r.useState)([]),[f,b]=(0,r.useState)(!1),[v,j]=(0,r.useState)(null),[w,N]=(0,r.useState)(1),[y,C]=(0,r.useState)(1),[S,P]=(0,r.useState)(0),k=`encrages-${t}-${s}-${i}-${o}-${w}-${l}-${d}-${c}-${m}-${u}`,A=(0,r.useCallback)(async()=>{let e=sessionStorage.getItem(k);if(e)try{let t=JSON.parse(e),r=t.timestamp;if(Date.now()-r<3e5){p(t.data),s&&g(t.stats||[]),C(t.pagination?.totalPages||1),P(t.pagination?.total||0);return}}catch(e){console.error("Erreur lors de la lecture du cache:",e)}b(!0),j(null);try{let e=new URLSearchParams;e.append("page",w.toString()),e.append("limit",t.toString()),e.append("includeStats",s.toString()),e.append("includeProblematiques",o.toString()),i&&e.append("search",i),l&&e.append("sortBy",l),d&&e.append("sortDirection",d),c&&e.append("dateFrom",c),m&&e.append("dateTo",m),u&&e.append("status",u);let r=`/api/encrages?${e.toString()}`,n=await a.uE.get(r);if(p(n.data),C(n.pagination.totalPages),P(n.pagination.total),s){let e=n.data.filter(e=>e.casStats).map(e=>({id:e.id,nom:e.nom,totalCas:e.casStats.total,casRegularises:e.casStats.regularises}));g(e),sessionStorage.setItem(k,JSON.stringify({data:n.data,stats:e,pagination:n.pagination,timestamp:Date.now()}))}else sessionStorage.setItem(k,JSON.stringify({data:n.data,pagination:n.pagination,timestamp:Date.now()}))}catch(e){j(e.message||"Erreur lors du chargement des encrages"),console.error("Erreur lors du chargement des encrages:",e)}finally{b(!1)}},[k,o,s,t,w,i]);return{encrages:x,encrageStats:h,isLoading:f,error:v,page:w,totalPages:y,totalItems:S,goToPage:(0,r.useCallback)(e=>{N(e)},[]),refresh:A}}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},80667:(e,t,s)=>{Promise.resolve().then(s.bind(s,38171))},83249:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(37413),a=s(41098),n=s(39916),i=s(98440),o=s(97021),l=s(58508);async function d({children:e}){let t=await (0,a.wz)();return t||(0,n.redirect)("/login"),(0,r.jsx)(i.SidebarProvider,{children:(0,r.jsxs)("div",{className:"flex min-h-screen bg-gradient-to-br  from-red-50 via-indigo-50 to-purple-100 font-inter w-full",children:[(0,r.jsx)("aside",{className:"transition-all duration-300 shadow-2xl  ",children:(0,r.jsx)(o.Sidebar,{user:t})}),(0,r.jsx)("main",{className:"flex-1 min-w-0 px-1 md:px-1 py-1 md:py-1",children:(0,r.jsx)("div",{className:"bg-white rounded-3xl shadow-2xl p-1 md:p-1 border border-gray-100 min-h-[80vh]",children:(0,r.jsx)(l.default,{children:e})})})]})})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},97021:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\Sidebar.tsx","Sidebar")},98440:(e,t,s)=>{"use strict";s.d(t,{SidebarProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","useSidebar");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\components\\SidebarContext.tsx","SidebarProvider")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,3903,5262,2348,2082,2797,2049],()=>s(72955));module.exports=r})();