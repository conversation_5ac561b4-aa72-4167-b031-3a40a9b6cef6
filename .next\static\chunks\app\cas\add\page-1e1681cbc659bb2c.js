(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345,1381,1404,3109],{98:(e,r,t)=>{"use strict";async function n(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:t="GET",body:n}=r;console.log("Making ".concat(t," request to ").concat(e)),n&&console.log("Request body:",n);let s=await fetch(e,{method:t,headers:{"Content-Type":"application/json",...r.headers},credentials:r.credentials||"include",body:n?JSON.stringify(n):void 0});if(console.log("Response status:",s.status),!s.ok){let e="HTTP error! status: ".concat(s.status),r=null;try{let t=await s.text();if(console.log("Error response text:",t),t)try{e=(null==(r=JSON.parse(t))?void 0:r.error)||(null==r?void 0:r.message)||t}catch(r){e=t||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===s.status)throw Error("Authentication required. Please log in again.");if(403===s.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===s.status)throw Error("Resource not found.");else if(s.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===s.status)return null;let l=await s.json();return console.log("Response data:",l),l}t.d(r,{Zq:()=>n,uE:()=>s});let s={get:(e,r)=>n(e,{...r,method:"GET"}),post:(e,r,t)=>n(e,{...t,method:"POST",body:r}),put:(e,r,t)=>n(e,{...t,method:"PUT",body:r}),patch:(e,r,t)=>n(e,{...t,method:"PATCH",body:r}),delete:(e,r,t)=>n(e,{...t,method:"DELETE",body:r})}},345:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Input:()=>o,default:()=>a});var n=t(5155),s=t(2115),l=t(9688);let o=(0,s.forwardRef)((e,r)=>{let{className:t,label:s,error:o,id:a,...i}=e;return(0,n.jsxs)("div",{className:"space-y-2",children:[s&&(0,n.jsx)("label",{htmlFor:a,className:"block text-sm font-medium text-foreground",children:s}),(0,n.jsx)("input",{id:a,ref:r,className:(0,l.QP)("block w-full rounded-md border border-input px-3 py-2 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm",o&&"border-destructive focus:border-destructive focus:ring-destructive/50",t),...i}),o&&(0,n.jsx)("p",{className:"mt-1 text-sm text-destructive",children:o})]})});o.displayName="Input";let a=o},767:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Button:()=>o,default:()=>a});var n=t(5155),s=t(9688),l=t(3084);function o(e){let{children:r,className:t,variant:o="primary",size:a="default",isLoading:i=!1,disabled:d,...u}=e;return(0,n.jsx)("button",{className:(0,s.QP)("rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/90 focus:ring-secondary/50",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-primary/50",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500/50"}[o],{default:"px-4 py-2",sm:"px-3 py-1.5 text-sm",icon:"p-2"}[a],t),disabled:i||d,...u,children:i?(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)(l.LoadingSpinner,{})}):r})}let a=o},1381:(e,r,t)=>{"use strict";t.r(r),t.d(r,{TextArea:()=>o,default:()=>a});var n=t(5155),s=t(2115),l=t(9688);let o=(0,s.forwardRef)((e,r)=>{let{className:t,label:s,error:o,id:a,rows:i=4,...d}=e;return(0,n.jsxs)("div",{className:"space-y-2",children:[s&&(0,n.jsx)("label",{htmlFor:a,className:"block text-sm font-medium text-foreground",children:s}),(0,n.jsx)("textarea",{id:a,ref:r,rows:i,className:(0,l.QP)("block w-full rounded-md border border-input px-3 py-2 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm resize-y",o&&"border-destructive focus:border-destructive focus:ring-destructive/50",t),...d}),o&&(0,n.jsx)("p",{className:"mt-1 text-sm text-destructive",children:o})]})});o.displayName="TextArea";let a=o},3084:(e,r,t)=>{"use strict";t.r(r),t.d(r,{LoadingSpinner:()=>l,default:()=>o});var n=t(5155),s=t(9688);function l(e){let{className:r,color:t="light",size:l="md"}=e;return(0,n.jsx)("div",{className:(0,s.QP)("border-2 rounded-full animate-spin",{light:"border-white/80 border-t-transparent",dark:"border-gray-700 border-t-transparent"}[t],{sm:"w-4 h-4",md:"w-5 h-5",lg:"w-8 h-8"}[l],r)})}let o=l},3109:(e,r,t)=>{"use strict";t.r(r),t.d(r,{FormError:()=>s,default:()=>l});var n=t(5155);function s(e){let{message:r}=e;return r?(0,n.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsx)("p",{className:"text-sm text-red-700",children:r})})]})}):null}let l=s},6587:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var n=t(5155),s=t(2115),l=t(5695),o=t(98),a=t(767),i=t(345),d=t(1381),u=t(7055),c=t(3109),m=t(3084),p=t(6671),h=t(5028),x=t(5462);let f=(0,h.default)(()=>Promise.all([t.e(1761),t.e(9712)]).then(t.bind(t,9712)).then(e=>e.Map),{loadableGenerated:{webpack:()=>[9712]},ssr:!1}),g=(0,h.default)(()=>Promise.all([t.e(1761),t.e(9712)]).then(t.bind(t,9712)).then(e=>e.TileLayer),{loadableGenerated:{webpack:()=>[9712]},ssr:!1}),b=(0,h.default)(()=>Promise.all([t.e(1761),t.e(9712)]).then(t.bind(t,9712)).then(e=>e.GeoJSON),{loadableGenerated:{webpack:()=>[9712]},ssr:!1}),v=e=>{let{value:r,onChange:t}=e,[l,o]=(0,s.useState)(null),[a,i]=(0,s.useState)(null),d=(0,s.useRef)(null),u=async e=>{i(null);try{let r=await e.text(),n=new DOMParser().parseFromString(r,"text/xml"),s=x.bW(n);o(s),t(s)}catch(e){i("Erreur lors de la conversion du fichier KML.")}};return(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block font-medium mb-1",children:"G\xe9om\xe9trie (KML, optionnel)"}),(0,n.jsxs)("div",{className:"border-2 border-dashed rounded p-4 text-center cursor-pointer hover:bg-gray-50",onDrop:e=>{e.preventDefault(),e.dataTransfer.files&&e.dataTransfer.files[0]&&u(e.dataTransfer.files[0])},onDragOver:e=>e.preventDefault(),onClick:()=>{var e;return null==(e=d.current)?void 0:e.click()},children:[l?(0,n.jsx)("span",{className:"text-green-600",children:"Fichier charg\xe9. Aper\xe7u ci-dessous."}):(0,n.jsx)("span",{children:"D\xe9posez un fichier KML ici ou cliquez pour s\xe9lectionner."}),(0,n.jsx)("input",{type:"file",accept:".kml",ref:d,className:"hidden",onChange:e=>{e.target.files&&e.target.files[0]&&u(e.target.files[0])}})]}),a&&(0,n.jsx)("div",{className:"text-red-600 mt-2",children:a}),l&&(0,n.jsx)("div",{className:"mt-4 h-64",children:(0,n.jsxs)(f,{center:[33.5,-7.5],zoom:7,style:{height:"100%",width:"100%"},scrollWheelZoom:!1,children:[(0,n.jsx)(g,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),(0,n.jsx)(b,{data:l})]})}),l&&(0,n.jsx)("button",{className:"mt-2 text-sm text-red-500 underline",type:"button",onClick:()=>{o(null),t(null)},children:"Supprimer la g\xe9om\xe9trie"}),l&&(0,n.jsx)("section",{children:(0,n.jsxs)(f,{center:[36.75,3.06],zoom:8,style:{height:"300px",width:"100%"},children:[(0,n.jsx)(g,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),(0,n.jsx)(b,{data:l})]})})]})};function y(){let e=(0,l.useRouter)(),[r,t]=(0,s.useState)({nom:"",genre:"",nif:"",nin:"",superficie:"",observation:"",problematiqueId:"",communeIds:[],date_depot:""}),[h,x]=(0,s.useState)([]),[f,g]=(0,s.useState)([]),[b,y]=(0,s.useState)(!1),[j,N]=(0,s.useState)(null),[E,P]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{y(!0);try{let[e,r]=await Promise.all([o.uE.get("/api/problematiques"),o.uE.get("/api/communes")]);x(e),g(r)}catch(e){N("Erreur lors du chargement des donn\xe9es initiales."),console.error(e)}y(!1)})()},[]);let S=e=>{let{name:r,value:n}=e.target,s=n;"nif"===r?s=function(e){let r=e.replace(/\D/g,"").substring(0,15);if(!r)return"";let t=[];for(let e=0;e<r.length;e+=3)t.push(r.substring(e,e+3));return t.join(".").substring(0,19)}(n):"nin"===r&&(s=function(e){let r=e.replace(/\D/g,"").substring(0,20);if(!r)return"";let t=[],n=0;for(let e=0;e<6&&!(n>=r.length);e++)t.push(r.substring(n,Math.min(n+3,r.length))),n+=3;return n<r.length&&t.push(r.substring(n,Math.min(n+2,r.length))),t.join(".").substring(0,26)}(n)),t(e=>{let t={...e,[r]:s};return"genre"===r&&(s===p.TypePersonne.PERSONNE_MORALE?t.nin="":s===p.TypePersonne.PERSONNE_PHYSIQUE&&(t.nif="")),t})},I=async t=>{if(console.log("handleSubmit: Fonction appel\xe9e !"),t.preventDefault(),N(null),y(!0),!r.genre){console.log("handleSubmit: Erreur - Genre non s\xe9lectionn\xe9."),N("Veuillez s\xe9lectionner un type (Personne Physique ou Morale)."),y(!1);return}if(!r.communeIds||0===r.communeIds.length){console.log("handleSubmit: Erreur - Aucune commune s\xe9lectionn\xe9e."),N("Veuillez s\xe9lectionner au moins une commune."),y(!1);return}if(console.log("handleSubmit: formData.communeIds avant envoi:",r.communeIds),r.genre===p.TypePersonne.PERSONNE_MORALE&&r.nif&&!/^\d{3}(\.\d{3}){4}$/.test(r.nif)){console.log("handleSubmit: Erreur - Format NIF invalide."),N("Format NIF invalide. Attendu: XXX.XXX.XXX.XXX.XXX"),y(!1);return}if(r.genre===p.TypePersonne.PERSONNE_PHYSIQUE&&r.nin&&!/^\d{3}(\.\d{3}){5}\.\d{2}$/.test(r.nin)){console.log("handleSubmit: Erreur - Format NIN invalide."),N("Format NIN invalide. Attendu: XXX.XXX.XXX.XXX.XXX.XXX.XX"),y(!1);return}let n={nom:r.nom,genre:r.genre,superficie:parseFloat(r.superficie),observation:r.observation||null,problematiqueId:r.problematiqueId,communeIds:r.communeIds,date_depot:r.date_depot?new Date(r.date_depot).toISOString():null,geojson:E||null};r.genre===p.TypePersonne.PERSONNE_MORALE?n.nif=r.nif||null:r.genre===p.TypePersonne.PERSONNE_PHYSIQUE&&(n.nin=r.nin||null),console.log("handleSubmit: Donn\xe9es envoy\xe9es \xe0 l'API:",n);try{await o.uE.post("/api/cas",n),e.push("/cas")}catch(r){var s,l,a,i,d,u,c;console.error("handleSubmit: Erreur API re\xe7ue.");let e="Erreur lors de la cr\xe9ation du cas.";(null==(l=r.response)||null==(s=l.data)?void 0:s.details)&&Array.isArray(r.response.data.details)?(console.error("D\xe9tails de l'erreur de validation API:",r.response.data.details),e=r.response.data.details.map(e=>"Champ '".concat(e.path.join("."),"': ").concat(e.message)).join("; ")):(null==(i=r.response)||null==(a=i.data)?void 0:a.message)?e=r.response.data.message:(null==(u=r.response)||null==(d=u.data)?void 0:d.error)&&(e=r.response.data.error),N(e),console.error("Erreur API compl\xe8te:",(null==(c=r.response)?void 0:c.data)||r)}y(!1)};return b&&0===h.length?(0,n.jsx)(m.LoadingSpinner,{}):(0,n.jsxs)("div",{className:" mx-auto px-4 py-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8 text-gray-800",children:"Ajouter un nouveau Cas"}),(0,n.jsxs)("form",{onSubmit:I,className:"space-y-6 bg-white p-8 shadow-xl rounded-lg",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"nom",className:"block text-sm font-medium text-gray-700",children:"Nom du Cas"}),(0,n.jsx)(i.Input,{type:"text",name:"nom",id:"nom",value:r.nom,onChange:S,required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"genre",className:"block text-sm font-medium text-gray-700",children:"Type"}),(0,n.jsxs)(u.l,{name:"genre",id:"genre",value:r.genre,onChange:S,required:!0,children:[(0,n.jsx)("option",{value:"",disabled:!0,children:"S\xe9lectionner un type"}),(0,n.jsx)("option",{value:p.TypePersonne.PERSONNE_PHYSIQUE,children:"Personne Physique"}),(0,n.jsx)("option",{value:p.TypePersonne.PERSONNE_MORALE,children:"Personne Morale"})]})]}),r.genre===p.TypePersonne.PERSONNE_MORALE&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"nif",className:"block text-sm font-medium text-gray-700",children:"NIF (Num\xe9ro d'Identification Fiscale)"}),(0,n.jsx)(i.Input,{type:"text",name:"nif",id:"nif",value:r.nif||"",onChange:S,placeholder:"Ex: 123.456.789.012.345",maxLength:19}),(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Saisissez les 15 chiffres, les points seront ajout\xe9s automatiquement."})]}),r.genre===p.TypePersonne.PERSONNE_PHYSIQUE&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"nin",className:"block text-sm font-medium text-gray-700",children:"NIN (Num\xe9ro d'Identification National)"}),(0,n.jsx)(i.Input,{type:"text",name:"nin",id:"nin",value:r.nin||"",onChange:S,placeholder:"Ex: 123.456.789.012.345.678.90",maxLength:26}),(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Saisissez les 20 chiffres, les points seront ajout\xe9s automatiquement."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"superficie",className:"block text-sm font-medium text-gray-700",children:"Superficie (Ha)"}),(0,n.jsx)(i.Input,{type:"number",name:"superficie",id:"superficie",value:r.superficie,onChange:S,required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"date_depot",className:"block text-sm font-medium text-gray-700",children:"Date de d\xe9p\xf4t du dossier"})," ",(0,n.jsx)(i.Input,{type:"date",name:"date_depot",id:"date_depot",value:r.date_depot,onChange:S})," "]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"problematiqueId",className:"block text-sm font-medium text-gray-700",children:"Probl\xe9matique"}),(0,n.jsxs)(u.l,{name:"problematiqueId",id:"problematiqueId",value:r.problematiqueId,onChange:S,required:!0,children:[(0,n.jsx)("option",{value:"",disabled:!0,children:"S\xe9lectionner une probl\xe9matique"}),h.map(e=>(0,n.jsx)("option",{value:e.id,children:e.problematique},e.id))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"communeIds",className:"block text-sm font-medium text-gray-700",children:"Commune(s)"}),(0,n.jsx)(u.l,{name:"communeIds",id:"communeIds",value:r.communeIds,onChange:e=>{let r=Array.from(e.target.selectedOptions,e=>e.value);t(e=>({...e,communeIds:r}))},multiple:!0,required:!0,children:f.map(e=>(0,n.jsx)("option",{value:e.id.toString(),children:e.nom},e.id))}),(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Maintenez Ctrl (ou Cmd sur Mac) pour s\xe9lectionner plusieurs communes."})]}),(0,n.jsx)(v,{value:E,onChange:P}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"observation",className:"block text-sm font-medium text-gray-700",children:"Observation"}),(0,n.jsx)(d.TextArea,{name:"observation",id:"observation",value:r.observation,onChange:S,rows:4})]}),j&&(0,n.jsx)(c.FormError,{message:j}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(a.Button,{type:"submit",isLoading:b,disabled:b,children:b?"Cr\xe9ation en cours...":"Cr\xe9er le Cas"})})]})]})}},7055:(e,r,t)=>{"use strict";t.d(r,{l:()=>s});var n=t(5155);t(2115);let s=e=>{let{label:r,id:t,name:s,value:l,onChange:o,required:a,multiple:i,children:d,className:u,error:c,...m}=e;return(0,n.jsxs)("div",{children:[r&&(0,n.jsxs)("label",{htmlFor:t||s,className:"block text-sm font-medium text-gray-700 mb-1",children:[r," ",a&&(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("select",{id:t||s,name:s,value:l,onChange:o,required:a,multiple:i,className:"".concat("block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md shadow-sm"," ").concat(c?"border-red-500 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"," ").concat(u||""),...m,children:d}),c&&(0,n.jsx)("p",{className:"mt-2 text-sm text-red-600",children:c})]})}},7611:(e,r,t)=>{Promise.resolve().then(t.bind(t,6587))}},e=>{var r=r=>e(e.s=r);e.O(0,[9688,6721,1642,8441,1684,7358],()=>r(7611)),_N_E=e.O()}]);