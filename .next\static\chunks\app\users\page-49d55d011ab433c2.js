(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5009],{5695:(e,s,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(s,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(s,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})},7086:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var a=r(5155),t=r(2115),l=r(767),i=r(345),n=r(3157),o=r(2166),d=r(3109),c=r(98),u=r(5695);function m(){(0,u.useRouter)();let[e,s]=(0,t.useState)([]),[r,m]=(0,t.useState)(!1),[h,x]=(0,t.useState)(!1),[p,g]=(0,t.useState)(null),[v,f]=(0,t.useState)(null),[j,w]=(0,t.useState)({email:"",username:"",password:"",role:"BASIC",wilayaId:null}),[y,b]=(0,t.useState)(""),[N,I]=(0,t.useState)(!1);async function k(){try{I(!0);let e=await (0,c.Zq)("/api/users");s(e||[])}catch(e){console.error("Error fetching users:",e),e instanceof Error&&e.message.includes("Authentication")?window.location.href="/login":b("Erreur lors du chargement des utilisateurs")}finally{I(!1)}}async function E(){try{let e=await (0,c.Zq)("/api/auth/me");e&&(f(e),"ADMIN"!==e.role&&(window.location.href="/dashboard"))}catch(e){console.error("Error fetching current user:",e),e instanceof Error&&e.message.includes("Authentication")&&(window.location.href="/login")}}async function A(e){e.preventDefault(),b(""),I(!0),console.log("Submitting username:",j.username);try{if(h&&p){let e={...j},s=e.password?e:{email:e.email,username:e.username,role:e.role,wilayaId:e.wilayaId};console.log("Data to send (edit):",s),await (0,c.Zq)("/api/users/".concat(p.id),{method:"PUT",body:s})}else{if(!j.password){b("Le mot de passe est requis"),I(!1);return}console.log("Data to send (add):",j),await (0,c.Zq)("/api/users",{method:"POST",body:j})}m(!1),k()}catch(e){b(e.message)}finally{I(!1)}}async function C(e){if(confirm("\xcates-vous s\xfbr de vouloir supprimer cet utilisateur ?"))try{await (0,c.Zq)("/api/users/".concat(e.id),{method:"DELETE"}),k()}catch(e){b(e.message)}}(0,t.useEffect)(()=>{k(),E()},[]);let S=[{header:"Nom d'utilisateur",accessorKey:"username"},{header:"Email",accessorKey:"email"},{header:"R\xf4le",accessorKey:"role",cell:e=>{let s=e.role,r="";switch(s){case"ADMIN":r="bg-red-100 text-red-800 border-red-200";break;case"EDITOR":r="bg-green-100 text-green-800 border-green-200";break;case"VIEWER":r="bg-gray-100 text-gray-800 border-gray-200";break;default:r="bg-blue-100 text-blue-800 border-blue-200"}return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(r," border"),children:s})}},{header:"Cas",accessorKey:"id",cell:e=>{var s;return(null==(s=e._count)?void 0:s.cas)||0}},{header:"Date de cr\xe9ation",accessorKey:"createdAt",cell:e=>new Date(e.createdAt).toLocaleDateString()}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:" mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Gestion des Utilisateurs"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"G\xe9rez les comptes utilisateurs et leurs permissions d'acc\xe8s au syst\xe8me."})]}),(0,a.jsxs)(l.Button,{onClick:function(){g(null),w({email:"",username:"",password:"",role:"BASIC",wilayaId:null}),console.log("Adding new user, username initialized as empty string"),x(!1),m(!0)},className:"flex-shrink-0",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5 mr-2",children:(0,a.jsx)("path",{d:"M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z"})}),"Nouvel utilisateur"]})]})}),y&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,a.jsx)(d.FormError,{message:y})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total utilisateurs"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Administrateurs"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"ADMIN"===e.role).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"\xc9diteurs"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>"EDITOR"===e.role).length})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white shadow-xl rounded-xl overflow-hidden border border-gray-200",children:[(0,a.jsx)("div",{className:"px-6 py-5 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl leading-7 font-semibold text-gray-800",children:"Liste des Utilisateurs"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Visualisez, modifiez ou supprimez les utilisateurs existants."})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.length," utilisateur",e.length>1?"s":""," au total"]})]})}),(0,a.jsx)(n.X,{data:e,columns:S,pageSize:25,pageSizeOptions:[10,25,50,100],showPaginationInfo:!0,isLoading:N,actions:e=>(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-2",children:[(0,a.jsxs)(l.Button,{variant:"secondary",size:"sm",onClick:()=>{g(e),w({email:e.email,username:e.username,password:"",role:e.role,wilayaId:e.wilayaId||null}),console.log("Editing user, username set to:",e.username),x(!0),m(!0)},className:"flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-4 h-4 mr-1.5",children:(0,a.jsx)("path",{d:"M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z"})}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Modifier"})]}),(0,a.jsxs)(l.Button,{variant:"destructive",size:"sm",onClick:()=>C(e),disabled:e.id===(null==v?void 0:v.id),className:"flex items-center",title:e.id===(null==v?void 0:v.id)?"Vous ne pouvez pas supprimer votre propre compte":"Supprimer cet utilisateur",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-4 h-4 sm:mr-1.5",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.58.177-2.34.296a.75.75 0 00-.707.707A48.69 48.69 0 002 6.499V16c0 .69.56 1.25 1.25 1.25H16.75c.69 0 1.25-.56 1.25-1.25V6.5c0-.434-.025-.864-.073-1.282a.75.75 0 00-.707-.707 48.689 48.689 0 00-2.34-.296V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.538-.054 2.208-.152l.002-.003L15 3.5l.003.002A2.5 2.5 0 0115 3.75V5H5V3.75a2.5 2.5 0 012.289-2.498L7.292 3.5l.002.003L7.5 3.5A2.5 2.5 0 017.75 3.5V4h2.25zM5 6.58V16h10V6.579A47.187 47.187 0 0110 6.5c-1.944 0-3.803.146-5.524.418l-.001.003L5 6.58z",clipRule:"evenodd"})}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Supprimer"})]})]})})]})]})}),(0,a.jsx)(o.Modal,{isOpen:r,onClose:()=>m(!1),title:h?"Modifier l'utilisateur":"Ajouter un utilisateur",children:(0,a.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(i.Input,{id:"email",label:"Email",type:"email",value:j.email,onChange:e=>w(s=>({...s,email:e.target.value})),required:!0}),(0,a.jsx)(i.Input,{id:"username",label:"Nom d'utilisateur",value:j.username,onChange:e=>{let s=e.target.value;console.log("Username input changed to:",s),w(e=>({...e,username:s}))},required:!0}),(0,a.jsx)(i.Input,{id:"password",label:h?"Mot de passe (laisser vide pour ne pas changer)":"Mot de passe",type:"password",value:j.password,onChange:e=>w(s=>({...s,password:e.target.value})),required:!h}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-foreground",children:"R\xf4le"}),(0,a.jsxs)("select",{id:"role",value:j.role,onChange:e=>w(s=>({...s,role:e.target.value})),required:!0,className:"mt-1 block w-full rounded-md border-input bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm",children:[(0,a.jsx)("option",{value:"BASIC",children:"Utilisateur (BASIC)"}),(0,a.jsx)("option",{value:"EDITOR",children:"\xc9diteur (EDITOR)"}),(0,a.jsx)("option",{value:"VIEWER",children:"Lecteur (VIEWER)"}),(0,a.jsx)("option",{value:"ADMIN",children:"Administrateur (ADMIN)"})]})]}),(0,a.jsx)(i.Input,{id:"wilayaId",label:"ID Wilaya (optionnel)",type:"number",value:null!==j.wilayaId?j.wilayaId:"",onChange:e=>w(s=>({...s,wilayaId:e.target.value?parseInt(e.target.value):null}))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200",children:[(0,a.jsx)(l.Button,{variant:"outline",onClick:()=>m(!1),type:"button",children:"Annuler"}),(0,a.jsx)(l.Button,{type:"submit",isLoading:N,children:h?"Enregistrer ":"Ajouter l'utilisateur"})]})]})})]})}},7585:(e,s,r)=>{Promise.resolve().then(r.bind(r,7086))}},e=>{var s=s=>e(e.s=s);e.O(0,[9688,9741,8441,1684,7358],()=>s(7585)),_N_E=e.O()}]);