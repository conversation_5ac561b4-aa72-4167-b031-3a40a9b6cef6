"use client";

import { useState } from "react";
import { fetchApi } from "@/lib/api-client";

interface PerformanceResult {
    name: string;
    duration: number;
    success: boolean;
    resultCount?: number;
    error?: string;
    memoryUsage?: NodeJS.MemoryUsage;
}

interface TestSummary {
    totalDuration: number;
    totalTests: number;
    successfulTests: number;
    failedTests: number;
    slowTests: number;
    verySlowTests: number;
}

interface DatabaseInfo {
    cas: number;
    blocages: number;
    secteurs: number;
    problematiques: number;
    users: number;
}

export default function PerformancePage() {
    const [isRunning, setIsRunning] = useState(false);
    const [isGenerating, setIsGenerating] = useState(false);
    const [results, setResults] = useState<PerformanceResult[]>([]);
    const [summary, setSummary] = useState<TestSummary | null>(null);
    const [database, setDatabase] = useState<DatabaseInfo | null>(null);
    const [analysis, setAnalysis] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);
    const [targetCas, setTargetCas] = useState(50000);
    const [isTesting, setIsTesting] = useState(false);

    const testStatsAPIs = async () => {
        setIsTesting(true);
        setError(null);

        try {
            console.log("🧪 Test des APIs de statistiques...");

            const response = await fetchApi<{
                success: boolean;
                message: string;
                results: any[];
                baseCounts: any;
                performance: any;
                error?: string;
                details?: string;
            }>("/api/admin/test-stats-apis");

            if (response.success) {
                console.log(
                    "✅ Test APIs statistiques réussi:",
                    response.results
                );

                const results = response.results;
                const successCount = results.filter((r) => r.success).length;
                const failCount = results.filter((r) => !r.success).length;

                let message = `✅ Test APIs statistiques terminé !\n\n`;
                message += `📊 ${successCount}/${results.length} APIs fonctionnelles\n`;
                message += `📈 Données de base: ${response.baseCounts.cas} cas, ${response.baseCounts.blocages} blocages\n`;
                message += `⏱️ Durée: ${response.performance.duration}ms\n\n`;

                message += `Détails par API:\n`;
                results.forEach((result) => {
                    if (result.success) {
                        message += `✅ ${result.api}: ${result.dataCount} éléments\n`;
                    } else {
                        message += `❌ ${result.api}: ${result.error}\n`;
                    }
                });

                alert(message);

                // Mettre à jour les stats de la base
                if (response.baseCounts) {
                    setDatabase(response.baseCounts);
                }
            } else {
                setError(
                    `Test échoué: ${response.error}\nDétails: ${
                        response.details || "Aucun détail"
                    }`
                );
            }
        } catch (err: any) {
            console.error("Erreur lors du test:", err);
            setError(`Test APIs statistiques échoué: ${err.message}`);
        } finally {
            setIsTesting(false);
        }
    };

    const testSimpleStats = async () => {
        setIsTesting(true);
        setError(null);

        try {
            console.log("🧪 Test des statistiques simples...");

            const response = await fetchApi<{
                success: boolean;
                message: string;
                stats: any;
                performance: any;
                error?: string;
                details?: string;
            }>("/api/test-simple-stats");

            if (response.success) {
                console.log(
                    "✅ Test statistiques simples réussi:",
                    response.stats
                );

                const stats = response.stats;
                alert(
                    `✅ Test statistiques simples réussi !\n\n` +
                        `📊 Total cas: ${stats.totalCas.toLocaleString()}\n` +
                        `✅ Régularisés: ${stats.regularises.toLocaleString()}\n` +
                        `🚧 Total blocages: ${stats.totalBlocages.toLocaleString()}\n` +
                        `⏱️ Durée: ${response.performance.duration}ms\n` +
                        `⏰ ${response.performance.timestamp}`
                );

                // Mettre à jour les stats de la base si disponibles
                if (stats) {
                    setDatabase({
                        cas: stats.totalCas,
                        blocages: stats.totalBlocages,
                        secteurs: 0,
                        problematiques: 0,
                        users: 0,
                    });
                }
            } else {
                setError(
                    `Test échoué: ${response.error}\nDétails: ${
                        response.details || "Aucun détail"
                    }`
                );
            }
        } catch (err: any) {
            console.error("Erreur lors du test:", err);
            setError(`Test statistiques échoué: ${err.message}`);
        } finally {
            setIsTesting(false);
        }
    };

    const runFullDiagnostic = async () => {
        setIsTesting(true);
        setError(null);

        try {
            console.log("🔍 Lancement du diagnostic complet...");

            const response = await fetchApi<{
                success: boolean;
                message: string;
                results: any;
                error?: string;
                step?: string;
                details?: string;
                timestamp?: string;
            }>("/api/admin/debug-api");

            if (response.success) {
                console.log("✅ Diagnostic complet réussi:", response.results);

                const results = response.results;
                const dbCounts = results.database.counts;

                alert(
                    `✅ Diagnostic complet réussi !\n\n` +
                        `👤 Utilisateur: ${results.user.username} (${results.user.role})\n` +
                        `🗄️ Base de données: Connectée\n` +
                        `📊 Données: ${dbCounts.cas.toLocaleString()} cas, ${dbCounts.blocages.toLocaleString()} blocages\n` +
                        `🔗 Tests: JOIN OK, SQL brut OK\n` +
                        `⏰ ${response.timestamp}`
                );

                // Mettre à jour les stats de la base
                if (results.database.counts) {
                    setDatabase(results.database.counts);
                }
            } else {
                const errorMsg =
                    `❌ Diagnostic échoué à l'étape: ${response.step}\n\n` +
                    `Erreur: ${response.error}\n` +
                    `Détails: ${response.details || "Aucun détail"}`;

                console.error("❌ Diagnostic échoué:", response);
                setError(errorMsg);
                alert(errorMsg);
            }
        } catch (err: any) {
            console.error("Erreur lors du diagnostic:", err);
            const errorMsg = `💥 Erreur fatale lors du diagnostic:\n\n${err.message}`;
            setError(errorMsg);
            alert(errorMsg);
        } finally {
            setIsTesting(false);
        }
    };

    const testRealGeneration = async () => {
        setIsTesting(true);
        setError(null);

        try {
            console.log("🧪 Test de génération réelle (100 cas)...");

            const response = await fetchApi<{
                success: boolean;
                message: string;
                created: { cas: number; blocages: number };
                totals: DatabaseInfo;
                details: any;
            }>("/api/admin/generate-real-data", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ testCount: 100 }),
            });

            if (response.success) {
                setDatabase(response.totals);
                console.log("✅ Génération réelle réussie:", response.message);
                alert(
                    `✅ Génération réelle réussie !\n\n📊 ${
                        response.created.cas
                    } cas créés\n🚧 ${
                        response.created.blocages
                    } blocages créés\n📈 Total: ${response.totals.cas.toLocaleString()} cas dans la base`
                );
            } else {
                setError(
                    `Génération échouée: ${
                        response.message || "Erreur inconnue"
                    }`
                );
            }
        } catch (err: any) {
            console.error("Erreur lors de la génération:", err);
            setError(`Test de génération échoué: ${err.message}`);
        } finally {
            setIsTesting(false);
        }
    };

    const testConnection = async () => {
        setIsTesting(true);
        setError(null);

        try {
            console.log("🔍 Test de connexion...");

            const response = await fetchApi<{
                success: boolean;
                message: string;
                user: any;
                timestamp: string;
            }>("/api/admin/simple-test");

            if (response.success) {
                console.log("✅ Connexion réussie:", response.message);
                alert(
                    `✅ Test de connexion réussi !\n\n👤 Utilisateur: ${response.user.username} (${response.user.role})\n⏰ Timestamp: ${response.timestamp}`
                );
            } else {
                setError(
                    `Test échoué: ${response.message || "Erreur inconnue"}`
                );
            }
        } catch (err: any) {
            console.error("Erreur lors du test:", err);
            setError(`Test de connexion échoué: ${err.message}`);
        } finally {
            setIsTesting(false);
        }
    };

    const testSmallGeneration = async () => {
        setIsTesting(true);
        setError(null);

        try {
            console.log("🧪 Test de génération (10 cas)...");

            const response = await fetchApi<{
                success: boolean;
                message: string;
                received: any;
                user: string;
                timestamp: string;
            }>("/api/admin/simple-test", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    testCount: 10,
                    action: "test-generation",
                }),
            });

            if (response.success) {
                console.log("✅ Test POST réussi:", response.message);
                alert(
                    `✅ Test POST réussi !\n\n� Utilisateur: ${
                        response.user
                    }\n📦 Données reçues: ${JSON.stringify(
                        response.received
                    )}\n⏰ ${response.timestamp}`
                );
            } else {
                setError(
                    `Test échoué: ${response.message || "Erreur inconnue"}`
                );
            }
        } catch (err: any) {
            console.error("Erreur lors du test:", err);
            setError(`Test de génération échoué: ${err.message}`);
        } finally {
            setIsTesting(false);
        }
    };

    const generateTestData = async () => {
        setIsGenerating(true);
        setError(null);

        try {
            console.log(
                `🚀 Génération de ${targetCas.toLocaleString()} cas de test...`
            );

            const response = await fetchApi<{
                success: boolean;
                message: string;
                created: { cas: number; blocages: number };
                totals: DatabaseInfo;
                batches: number;
            }>("/api/admin/generate-test-data", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ targetCas }),
            });

            if (response.success) {
                setDatabase(response.totals);
                console.log(
                    `✅ Génération terminée: ${response.created.cas} cas, ${response.created.blocages} blocages créés`
                );
                alert(
                    `🎉 Génération réussie !\n\n📊 ${response.created.cas.toLocaleString()} cas créés\n🚧 ${response.created.blocages.toLocaleString()} blocages créés\n📦 ${
                        response.batches
                    } batches traités`
                );
            }
        } catch (err: any) {
            console.error("Erreur lors de la génération:", err);
            console.error("Détails de l'erreur:", {
                message: err.message,
                status: err.status,
                response: err.response,
            });

            let errorMessage =
                "Erreur lors de la génération des données de test";
            if (err.message?.includes("401")) {
                errorMessage =
                    "Erreur d'authentification. Veuillez vous reconnecter.";
            } else if (err.message?.includes("403")) {
                errorMessage = "Accès refusé. Vous devez être administrateur.";
            } else if (err.message?.includes("400")) {
                errorMessage =
                    "Paramètres invalides. Vérifiez le nombre de cas.";
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setIsGenerating(false);
        }
    };

    const runTests = async (testType: string = "all") => {
        setIsRunning(true);
        setError(null);
        setResults([]);
        setSummary(null);

        try {
            console.log(`🚀 Lancement des tests de performance: ${testType}`);

            const response = await fetchApi<{
                summary: TestSummary;
                database: DatabaseInfo;
                results: PerformanceResult[];
                analysis: any;
            }>(`/api/admin/performance-test?type=${testType}`);

            setResults(response.results);
            setSummary(response.summary);
            setDatabase(response.database);
            setAnalysis(response.analysis);

            console.log(
                `✅ Tests terminés: ${response.summary.successfulTests}/${response.summary.totalTests} réussis`
            );
        } catch (err: any) {
            console.error("Erreur lors des tests:", err);
            setError(err.message || "Erreur lors des tests de performance");
        } finally {
            setIsRunning(false);
        }
    };

    const getPerformanceColor = (duration: number) => {
        if (duration < 1000) return "text-green-600";
        if (duration < 5000) return "text-yellow-600";
        if (duration < 10000) return "text-orange-600";
        return "text-red-600";
    };

    const getPerformanceIcon = (duration: number) => {
        if (duration < 1000) return "🚀";
        if (duration < 5000) return "⚡";
        if (duration < 10000) return "⚠️";
        return "🐌";
    };

    return (
        <div className=" mx-auto px-4 py-8">
            <div className="max-w-6xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-8">
                    🚀 Tests de Performance
                </h1>

                {/* Informations sur la base de données */}
                {database && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                        <h2 className="text-xl font-semibold text-blue-900 mb-4">
                            📊 Base de données
                        </h2>
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {database.cas.toLocaleString()}
                                </div>
                                <div className="text-sm text-blue-700">Cas</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {database.blocages.toLocaleString()}
                                </div>
                                <div className="text-sm text-blue-700">
                                    Blocages
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {database.secteurs}
                                </div>
                                <div className="text-sm text-blue-700">
                                    Secteurs
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {database.problematiques}
                                </div>
                                <div className="text-sm text-blue-700">
                                    Problématiques
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {database.users}
                                </div>
                                <div className="text-sm text-blue-700">
                                    Utilisateurs
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Tests de diagnostic */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                    <h2 className="text-xl font-semibold text-yellow-900 mb-4">
                        🔍 Tests de diagnostic
                    </h2>
                    <div className="flex flex-wrap gap-4 mb-4">
                        <button
                            onClick={testConnection}
                            disabled={isTesting || isGenerating || isRunning}
                            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isTesting ? "🔄 Test..." : "🔍 Test Auth"}
                        </button>
                        <button
                            onClick={testSmallGeneration}
                            disabled={isTesting || isGenerating || isRunning}
                            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isTesting ? "🔄 Test..." : "🧪 Test API POST"}
                        </button>
                        <button
                            onClick={testRealGeneration}
                            disabled={isTesting || isGenerating || isRunning}
                            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isTesting
                                ? "🔄 Test..."
                                : "🚀 Test Génération (100 cas)"}
                        </button>
                        <button
                            onClick={testStatsAPIs}
                            disabled={isTesting || isGenerating || isRunning}
                            className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isTesting ? "🔄 Test..." : "📊 Test APIs Stats"}
                        </button>
                        <button
                            onClick={testSimpleStats}
                            disabled={isTesting || isGenerating || isRunning}
                            className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isTesting ? "🔄 Test..." : "🧪 Test Stats Simples"}
                        </button>
                        <button
                            onClick={runFullDiagnostic}
                            disabled={isTesting || isGenerating || isRunning}
                            className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isTesting ? "🔄 Test..." : "🔍 Diagnostic Complet"}
                        </button>
                    </div>
                    <p className="text-sm text-yellow-700">
                        Utilisez ces tests pour diagnostiquer les problèmes
                        avant la génération complète.
                    </p>
                </div>

                {/* Génération de données de test */}
                <div className="bg-white shadow rounded-lg p-6 mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                        📊 Génération de données de test
                    </h2>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                        <div className="flex items-center gap-2">
                            <label className="text-sm font-medium text-gray-700">
                                Nombre de cas à générer:
                            </label>
                            <input
                                type="number"
                                value={targetCas}
                                onChange={(e) =>
                                    setTargetCas(
                                        parseInt(e.target.value) || 50000
                                    )
                                }
                                min="1000"
                                max="100000"
                                step="1000"
                                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-24"
                                disabled={isGenerating}
                            />
                        </div>
                        <button
                            onClick={generateTestData}
                            disabled={isGenerating || isRunning}
                            className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isGenerating
                                ? "🔄 Génération en cours..."
                                : "📊 Générer les données"}
                        </button>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                        Génère des cas fictifs avec leurs contraintes associées
                        pour tester les performances. Chaque cas aura 1-3
                        contraintes avec des résolutions variées.
                    </p>
                </div>

                {/* Boutons de test */}
                <div className="bg-white shadow rounded-lg p-6 mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                        🧪 Lancer les tests de performance
                    </h2>
                    <div className="flex flex-wrap gap-4">
                        <button
                            onClick={() => runTests("all")}
                            disabled={isRunning}
                            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        >
                            {isRunning
                                ? "🔄 Tests en cours..."
                                : "🚀 Tous les tests"}
                        </button>
                        <button
                            onClick={() => runTests("dashboard")}
                            disabled={isRunning}
                            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        >
                            📊 Dashboard
                        </button>
                        <button
                            onClick={() => runTests("listing")}
                            disabled={isRunning}
                            className="bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        >
                            📋 Listing
                        </button>
                        <button
                            onClick={() => runTests("stats")}
                            disabled={isRunning}
                            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        >
                            📈 Statistiques
                        </button>
                    </div>
                </div>

                {/* Résumé des tests */}
                {summary && (
                    <div className="bg-white shadow rounded-lg p-6 mb-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">
                            📋 Résumé des tests
                        </h2>
                        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-gray-600">
                                    {Math.round(summary.totalDuration)}ms
                                </div>
                                <div className="text-sm text-gray-700">
                                    Durée totale
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {summary.totalTests}
                                </div>
                                <div className="text-sm text-gray-700">
                                    Tests total
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {summary.successfulTests}
                                </div>
                                <div className="text-sm text-gray-700">
                                    Réussis
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">
                                    {summary.failedTests}
                                </div>
                                <div className="text-sm text-gray-700">
                                    Échoués
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-orange-600">
                                    {summary.slowTests}
                                </div>
                                <div className="text-sm text-gray-700">
                                    Lents (5-10s)
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">
                                    {summary.verySlowTests}
                                </div>
                                <div className="text-sm text-gray-700">
                                    Très lents (&gt;10s)
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Analyse et recommandations */}
                {analysis && (
                    <div
                        className={`border rounded-lg p-6 mb-6 ${
                            analysis.performance === "excellent"
                                ? "bg-green-50 border-green-200"
                                : analysis.performance === "good"
                                ? "bg-yellow-50 border-yellow-200"
                                : "bg-red-50 border-red-200"
                        }`}
                    >
                        <h2 className="text-xl font-semibold mb-4">
                            {analysis.performance === "excellent"
                                ? "🚀 Performance Excellente"
                                : analysis.performance === "good"
                                ? "⚡ Performance Correcte"
                                : "🐌 Performance à Optimiser"}
                        </h2>
                        {analysis.recommendations.length > 0 && (
                            <div>
                                <h3 className="font-semibold mb-2">
                                    💡 Recommandations:
                                </h3>
                                <ul className="list-disc list-inside space-y-1">
                                    {analysis.recommendations.map(
                                        (rec: string, index: number) => (
                                            <li key={index} className="text-sm">
                                                {rec}
                                            </li>
                                        )
                                    )}
                                </ul>
                            </div>
                        )}
                    </div>
                )}

                {/* Erreur */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                        <h2 className="text-xl font-semibold text-red-900 mb-2">
                            ❌ Erreur
                        </h2>
                        <p className="text-red-700">{error}</p>
                    </div>
                )}

                {/* Résultats détaillés */}
                {results.length > 0 && (
                    <div className="bg-white shadow rounded-lg p-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">
                            📊 Résultats détaillés
                        </h2>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Test
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Durée
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Résultats
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Mémoire
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Statut
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {results.map((result, index) => (
                                        <tr
                                            key={index}
                                            className={
                                                result.success
                                                    ? ""
                                                    : "bg-red-50"
                                            }
                                        >
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {result.name}
                                            </td>
                                            <td
                                                className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${getPerformanceColor(
                                                    result.duration
                                                )}`}
                                            >
                                                {getPerformanceIcon(
                                                    result.duration
                                                )}{" "}
                                                {result.duration}ms
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {result.resultCount?.toLocaleString() ||
                                                    "-"}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {result.memoryUsage
                                                    ? `${Math.round(
                                                          result.memoryUsage
                                                              .heapUsed /
                                                              1024 /
                                                              1024
                                                      )}MB`
                                                    : "-"}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                {result.success ? (
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        ✅ Réussi
                                                    </span>
                                                ) : (
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        ❌ Échoué
                                                    </span>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
