import { getUser } from "@/lib/auth";
import { redirect } from "next/navigation";
import { SidebarProvider } from "@/app/components/SidebarContext";
import { Sidebar } from "@/app/components/Sidebar";
import MainContentClient from "./MainContentClient";

export default async function DashboardLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const user = await getUser();

    if (!user) {
        redirect("/login");
    }

    return (
        <SidebarProvider>
            <div className="flex min-h-screen bg-gradient-to-br  from-red-50 via-indigo-50 to-purple-100 font-inter w-full">
                {/* Sidebar modernisée */}
                <aside className="transition-all duration-300 shadow-2xl  ">
                    <Sidebar user={user} />
                </aside>
                {/* Contenu principal modernisé */}
                <main className="flex-1 min-w-0 px-1 md:px-1 py-1 md:py-1">
                    <div className="bg-white rounded-3xl shadow-2xl p-1 md:p-1 border border-gray-100 min-h-[80vh]">
                        <MainContentClient>{children}</MainContentClient>
                    </div>
                </main>
            </div>
        </SidebarProvider>
    );
}
