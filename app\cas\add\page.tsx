"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { apiClient } from "@/lib/api-client";
import { Button } from "@/app/components/Button";
import { Input } from "@/app/components/Input";
import { TextArea } from "@/app/components/TextArea";
import { Select } from "@/app/components/Select"; // Assurez-vous que Select est importé
import { FormError } from "@/app/components/FormError";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { Problematique, Commune, User, TypePersonne } from "@prisma/client"; // Ajoutez TypePersonne
import GeoJsonDropzone from "@/app/components/GeoJsonDropzone";

// Helper functions for formatting NIF and NIN input (place these outside the component)
function formatNifInput(value: string): string {
    const digits = value.replace(/\D/g, "").substring(0, 15);
    if (!digits) return "";

    const parts = [];
    for (let i = 0; i < digits.length; i += 3) {
        parts.push(digits.substring(i, i + 3));
    }
    // Join parts and ensure it doesn't exceed the NIF format length (XXX.XXX.XXX.XXX.XXX)
    return parts.join(".").substring(0, 19);
}

function formatNinInput(value: string): string {
    const digits = value.replace(/\D/g, "").substring(0, 20);
    if (!digits) return "";

    const parts = [];
    let i = 0;
    // First 6 groups of 3 digits
    for (let k = 0; k < 6; k++) {
        if (i >= digits.length) break;
        parts.push(digits.substring(i, Math.min(i + 3, digits.length)));
        i += 3;
    }
    // Last group of up to 2 digits
    if (i < digits.length) {
        parts.push(digits.substring(i, Math.min(i + 2, digits.length)));
    }
    // Join parts and ensure it doesn't exceed the NIN format length (XXX.XXX.XXX.XXX.XXX.XXX.XX)
    return parts.join(".").substring(0, 26);
}

interface CasFormData {
    nom: string;
    genre: TypePersonne | "";
    nif?: string; // Rendre optionnel
    nin?: string; // Rendre optionnel
    superficie: string;
    observation?: string; // Rendre optionnel
    problematiqueId: string;
    communeIds: string[];
    date_depot?: string; // MODIFIED: Renamed from depot_dossier
}

export default function AddCasPage() {
    const router = useRouter();
    const [formData, setFormData] = useState<CasFormData>({
        nom: "",
        genre: "",
        nif: "",
        nin: "",
        superficie: "",
        observation: "",
        problematiqueId: "",
        communeIds: [],
        date_depot: "", // MODIFIED: Renamed from depot_dossier
    });
    const [problematiques, setProblematiques] = useState<Problematique[]>([]);
    const [communes, setCommunes] = useState<Commune[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [geojson, setGeojson] = useState<any>(null);

    useEffect(() => {
        // Charger les problématiques et communes pour les sélecteurs
        const fetchData = async () => {
            setIsLoading(true);
            try {
                const [problematiquesRes, communesRes] = await Promise.all([
                    apiClient.get<Problematique[]>("/api/problematiques"),
                    apiClient.get<Commune[]>("/api/communes"), // Assurez-vous que cette API existe
                ]);
                setProblematiques(problematiquesRes);
                setCommunes(communesRes);
            } catch (err) {
                setError("Erreur lors du chargement des données initiales.");
                console.error(err);
            }
            setIsLoading(false);
        };
        fetchData();
    }, []);

    const handleChange = (
        e: React.ChangeEvent<
            HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
        >
    ) => {
        const { name, value } = e.target;
        let processedValue = value;

        if (name === "nif") {
            processedValue = formatNifInput(value);
        } else if (name === "nin") {
            processedValue = formatNinInput(value);
        }

        setFormData((prev) => {
            const newState = { ...prev, [name]: processedValue };
            if (name === "genre") {
                if (processedValue === TypePersonne.PERSONNE_MORALE) {
                    newState.nin = ""; // Effacer NIN
                } else if (processedValue === TypePersonne.PERSONNE_PHYSIQUE) {
                    newState.nif = ""; // Effacer NIF
                }
            }
            return newState;
        });
    };

    const handleCommuneChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const selectedOptions = Array.from(
            e.target.selectedOptions,
            (option) => option.value
        );
        setFormData((prev) => ({ ...prev, communeIds: selectedOptions }));
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        console.log("handleSubmit: Fonction appelée !"); // AJOUT DE TEST ICI
        e.preventDefault();
        setError(null);
        setIsLoading(true);

        if (!formData.genre) {
            console.log("handleSubmit: Erreur - Genre non sélectionné."); // Log supplémentaire
            setError(
                "Veuillez sélectionner un type (Personne Physique ou Morale)."
            );
            setIsLoading(false);
            return;
        }

        // Vérification que communeIds n'est pas vide
        if (!formData.communeIds || formData.communeIds.length === 0) {
            console.log("handleSubmit: Erreur - Aucune commune sélectionnée."); // Log supplémentaire
            setError("Veuillez sélectionner au moins une commune.");
            setIsLoading(false);
            return;
        }
        console.log(
            "handleSubmit: formData.communeIds avant envoi:",
            formData.communeIds
        );

        // Vérification NIF/NIN avant envoi (optionnel, car le backend valide aussi)
        if (
            formData.genre === TypePersonne.PERSONNE_MORALE &&
            formData.nif &&
            !/^\d{3}(\.\d{3}){4}$/.test(formData.nif)
        ) {
            console.log("handleSubmit: Erreur - Format NIF invalide."); // Log supplémentaire
            setError("Format NIF invalide. Attendu: XXX.XXX.XXX.XXX.XXX");
            setIsLoading(false);
            return;
        }
        if (
            formData.genre === TypePersonne.PERSONNE_PHYSIQUE &&
            formData.nin &&
            !/^\d{3}(\.\d{3}){5}\.\d{2}$/.test(formData.nin)
        ) {
            console.log("handleSubmit: Erreur - Format NIN invalide."); // Log supplémentaire
            setError(
                "Format NIN invalide. Attendu: XXX.XXX.XXX.XXX.XXX.XXX.XX"
            );
            setIsLoading(false);
            return;
        }

        const dataToSend: any = {
            nom: formData.nom,
            genre: formData.genre,
            superficie: parseFloat(formData.superficie),
            observation: formData.observation || null,
            problematiqueId: formData.problematiqueId,
            communeIds: formData.communeIds,
            date_depot: formData.date_depot
                ? new Date(formData.date_depot).toISOString()
                : null,
            geojson: geojson || null,
        };

        if (formData.genre === TypePersonne.PERSONNE_MORALE) {
            dataToSend.nif = formData.nif || null;
        } else if (formData.genre === TypePersonne.PERSONNE_PHYSIQUE) {
            dataToSend.nin = formData.nin || null;
        }

        console.log("handleSubmit: Données envoyées à l'API:", dataToSend);

        try {
            await apiClient.post("/api/cas", dataToSend);
            router.push("/cas");
        } catch (err: any) {
            console.error("handleSubmit: Erreur API reçue."); // Log supplémentaire
            let detailedError = "Erreur lors de la création du cas.";
            if (
                err.response?.data?.details &&
                Array.isArray(err.response.data.details)
            ) {
                console.error(
                    "Détails de l'erreur de validation API:",
                    err.response.data.details
                ); // AJOUT POUR DÉBOGAGE
                detailedError = err.response.data.details
                    .map(
                        (d: { path: string[]; message: string }) =>
                            `Champ '${d.path.join(".")}': ${d.message}`
                    )
                    .join("; ");
            } else if (err.response?.data?.message) {
                detailedError = err.response.data.message;
            } else if (err.response?.data?.error) {
                detailedError = err.response.data.error;
            }
            setError(detailedError);
            console.error("Erreur API complète:", err.response?.data || err);
        }
        setIsLoading(false);
    };

    if (isLoading && problematiques.length === 0) return <LoadingSpinner />; // Afficher le spinner pendant le chargement initial

    return (
        <div className=" mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-8 text-gray-800">
                Ajouter un nouveau Cas
            </h1>
            <form
                onSubmit={handleSubmit}
                className="space-y-6 bg-white p-8 shadow-xl rounded-lg"
            >
                {/* Champ Nom */}
                <div>
                    <label
                        htmlFor="nom"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Nom du Cas
                    </label>
                    <Input
                        type="text"
                        name="nom"
                        id="nom"
                        value={formData.nom}
                        onChange={handleChange}
                        required
                    />
                </div>

                {/* Champ Genre */}
                <div>
                    <label
                        htmlFor="genre"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Type
                    </label>
                    <Select
                        name="genre"
                        id="genre"
                        value={formData.genre}
                        onChange={handleChange}
                        required
                    >
                        <option value="" disabled>
                            Sélectionner un type
                        </option>
                        <option value={TypePersonne.PERSONNE_PHYSIQUE}>
                            Personne Physique
                        </option>
                        <option value={TypePersonne.PERSONNE_MORALE}>
                            Personne Morale
                        </option>
                    </Select>
                </div>

                {/* Champ NIF (conditionnel) */}
                {formData.genre === TypePersonne.PERSONNE_MORALE && (
                    <div>
                        <label
                            htmlFor="nif"
                            className="block text-sm font-medium text-gray-700"
                        >
                            NIF (Numéro d'Identification Fiscale)
                        </label>
                        <Input
                            type="text"
                            name="nif"
                            id="nif"
                            value={formData.nif || ""}
                            onChange={handleChange}
                            placeholder="Ex: 123.456.789.012.345"
                            maxLength={19} // 15 chiffres + 4 points
                            // pattern="^\d{3}(\.\d{3}){4}$" // Supprimé
                            // title="Format: 15 chiffres séparés par des points (ex: XXX.XXX.XXX.XXX.XXX)" // Supprimé ou adapté
                        />
                        <p className="mt-1 text-xs text-gray-500">
                            Saisissez les 15 chiffres, les points seront ajoutés
                            automatiquement.
                        </p>
                    </div>
                )}

                {/* Champ NIN (conditionnel) */}
                {formData.genre === TypePersonne.PERSONNE_PHYSIQUE && (
                    <div>
                        <label
                            htmlFor="nin"
                            className="block text-sm font-medium text-gray-700"
                        >
                            NIN (Numéro d'Identification National)
                        </label>
                        <Input
                            type="text"
                            name="nin"
                            id="nin"
                            value={formData.nin || ""}
                            onChange={handleChange}
                            placeholder="Ex: 123.456.789.012.345.678.90"
                            maxLength={26} // 20 chiffres + 6 points
                            // pattern="^\d{3}(\.\d{3}){5}\.\d{2}$" // Supprimé
                            // title="Format: 20 chiffres séparés par des points (ex: XXX.XXX.XXX.XXX.XXX.XXX.XX)" // Supprimé ou adapté
                        />
                        <p className="mt-1 text-xs text-gray-500">
                            Saisissez les 20 chiffres, les points seront ajoutés
                            automatiquement.
                        </p>
                    </div>
                )}

                {/* Champ Superficie */}
                <div>
                    <label
                        htmlFor="superficie"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Superficie (Ha)
                    </label>
                    <Input
                        type="number"
                        name="superficie"
                        id="superficie"
                        value={formData.superficie}
                        onChange={handleChange}
                        required
                    />
                </div>

                {/* Champ Date dépôt dossier */}
                <div>
                    <label
                        htmlFor="date_depot"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Date de dépôt du dossier
                    </label>{" "}
                    {/* MODIFIED: htmlFor */}
                    <Input
                        type="date"
                        name="date_depot"
                        id="date_depot"
                        value={formData.date_depot}
                        onChange={handleChange}
                    />{" "}
                    {/* MODIFIED: name, id, value */}
                </div>

                {/* Champ Problématique */}
                <div>
                    <label
                        htmlFor="problematiqueId"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Problématique
                    </label>
                    <Select
                        name="problematiqueId"
                        id="problematiqueId"
                        value={formData.problematiqueId}
                        onChange={handleChange}
                        required
                    >
                        <option value="" disabled>
                            Sélectionner une problématique
                        </option>
                        {problematiques.map((p) => (
                            <option key={p.id} value={p.id}>
                                {p.problematique}
                            </option>
                        ))}
                    </Select>
                </div>

                {/* Champ Communes */}
                <div>
                    <label
                        htmlFor="communeIds"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Commune(s)
                    </label>
                    <Select
                        name="communeIds"
                        id="communeIds"
                        value={formData.communeIds}
                        onChange={handleCommuneChange}
                        multiple
                        required
                    >
                        {/* <option value="" disabled>Sélectionner une ou plusieurs communes</option> */}
                        {communes.map((c) => (
                            <option key={c.id} value={c.id.toString()}>
                                {c.nom}
                            </option>
                        ))}
                    </Select>
                    <p className="mt-1 text-xs text-gray-500">
                        Maintenez Ctrl (ou Cmd sur Mac) pour sélectionner
                        plusieurs communes.
                    </p>
                </div>

                {/* Champ GeoJSON (KML) */}
                <GeoJsonDropzone value={geojson} onChange={setGeojson} />

                {/* Champ Observation */}
                <div>
                    <label
                        htmlFor="observation"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Observation
                    </label>
                    <TextArea
                        name="observation"
                        id="observation"
                        value={formData.observation}
                        onChange={handleChange}
                        rows={4}
                    />
                </div>

                {error && <FormError message={error} />}
                <div className="flex justify-end">
                    <Button
                        type="submit"
                        isLoading={isLoading}
                        disabled={isLoading}
                    >
                        {isLoading ? "Création en cours..." : "Créer le Cas"}
                    </Button>
                </div>
            </form>
        </div>
    );
}
