{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|_next\\/data|favicon.ico|public).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|_next/data|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "SjVUTp2ER7wIeR3CIzLcm", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6mMEleEDAeTUUn5cw6Lcw85BD+2TTRTY+sEGcYx/434=", "__NEXT_PREVIEW_MODE_ID": "0aeed1693fa7d2a6ea3802015f29aa6d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5dc84dae3a770c5a908230031b8cc5e41a6482b1111aa02b888527a18d36112a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "02cc7622231ea0f766c0679e0695b192fce6e9cd5a31af31e86f9a2b30e7f9af"}}}, "functions": {}, "sortedMiddleware": ["/"]}