"use client";

import { useState, useEffect } from "react";
import { Button } from "@/app/components/Button";
import { Input } from "@/app/components/Input";
import { Table } from "@/app/components/Table";
import { Modal } from "@/app/components/Modal";
import { FormError } from "@/app/components/FormError";
import { fetchApi } from "@/lib/api-client";
import { useRouter } from "next/navigation";

interface User {
    id: string;
    email: string;
    username: string;
    role: "ADMIN" | "EDITOR" | "BASIC" | "VIEWER";
    wilayaId?: number | null;
    createdAt: string;
    updatedAt: string;
    _count?: {
        cas: number;
    };
}

export default function UsersPage() {
    const router = useRouter();
    const [users, setUsers] = useState<User[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [loggedInUser, setLoggedInUser] = useState<{
        id: string;
        username: string;
        role: string;
    } | null>(null);

    const [formData, setFormData] = useState({
        email: "",
        username: "",
        password: "",
        role: "BASIC" as "ADMIN" | "EDITOR" | "BASIC" | "VIEWER",
        wilayaId: null as number | null,
    });
    const [error, setError] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        loadUsers();
        loadCurrentUser();
    }, []);

    async function loadUsers() {
        try {
            setIsLoading(true);
            const data = await fetchApi<User[]>("/api/users");
            setUsers(data || []);
        } catch (error) {
            console.error("Error fetching users:", error);
            // Handle authentication errors
            if (
                error instanceof Error &&
                error.message.includes("Authentication")
            ) {
                window.location.href = "/login";
            } else {
                setError("Erreur lors du chargement des utilisateurs");
            }
        } finally {
            setIsLoading(false);
        }
    }

    async function loadCurrentUser() {
        try {
            const response = await fetchApi<{
                id: string;
                username: string;
                role: string;
            }>("/api/auth/me");
            if (response) {
                setLoggedInUser(response);
                // Redirect if not admin
                if (response.role !== "ADMIN") {
                    window.location.href = "/dashboard";
                }
            }
        } catch (error) {
            console.error("Error fetching current user:", error);
            if (
                error instanceof Error &&
                error.message.includes("Authentication")
            ) {
                window.location.href = "/login";
            }
        }
    }

    async function handleSubmit(e: React.FormEvent) {
        e.preventDefault();
        setError("");
        setIsLoading(true);

        console.log("Submitting username:", formData.username); // Log username on submit

        try {
            if (isEditing && currentUser) {
                // For editing, we don't require password
                const updateData = { ...formData };
                // Only include password if it's not empty
                const dataToSend = !updateData.password
                    ? {
                          email: updateData.email,
                          username: updateData.username,
                          role: updateData.role,
                          wilayaId: updateData.wilayaId,
                      }
                    : updateData;

                console.log("Data to send (edit):", dataToSend); // Log data being sent for edit

                await fetchApi(`/api/users/${currentUser.id}`, {
                    method: "PUT",
                    body: dataToSend,
                });
            } else {
                // For new users, password is required
                if (!formData.password) {
                    setError("Le mot de passe est requis");
                    setIsLoading(false);
                    return;
                }

                console.log("Data to send (add):", formData); // Log data being sent for add

                await fetchApi("/api/users", {
                    method: "POST",
                    body: formData,
                });
            }

            setIsModalOpen(false);
            loadUsers();
        } catch (error) {
            setError((error as Error).message);
        } finally {
            setIsLoading(false);
        }
    }

    async function handleDelete(user: User) {
        if (!confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?"))
            return;

        try {
            await fetchApi(`/api/users/${user.id}`, { method: "DELETE" });
            loadUsers();
        } catch (error) {
            setError((error as Error).message);
        }
    }

    function handleEdit(user: User) {
        setCurrentUser(user);
        setFormData({
            email: user.email,
            username: user.username,
            password: "", // Don't include password when editing
            role: user.role,
            wilayaId: user.wilayaId || null,
        });
        console.log("Editing user, username set to:", user.username); // Log username on edit
        setIsEditing(true);
        setIsModalOpen(true);
    }

    function handleAdd() {
        setCurrentUser(null);
        setFormData({
            email: "",
            username: "",
            password: "",
            role: "BASIC",
            wilayaId: null,
        });
        console.log("Adding new user, username initialized as empty string"); // Log username on add
        setIsEditing(false);
        setIsModalOpen(true);
    }

    const columns = [
        {
            header: "Nom d'utilisateur",
            accessorKey: "username" as keyof User,
        },
        {
            header: "Email",
            accessorKey: "email" as keyof User,
        },
        {
            header: "Rôle",
            accessorKey: "role" as keyof User,
            cell: (user: User) => {
                // Correct: Expects User object directly
                const role = user.role;
                let badgeColor = "";

                switch (role) {
                    case "ADMIN":
                        badgeColor = "bg-red-100 text-red-800 border-red-200";
                        break;
                    case "EDITOR":
                        badgeColor =
                            "bg-green-100 text-green-800 border-green-200";
                        break;
                    case "VIEWER":
                        badgeColor =
                            "bg-gray-100 text-gray-800 border-gray-200";
                        break;
                    default:
                        badgeColor =
                            "bg-blue-100 text-blue-800 border-blue-200";
                }

                return (
                    <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeColor} border`}
                    >
                        {role}
                    </span>
                );
            },
        },
        {
            header: "Cas",
            accessorKey: "id" as keyof User, // Using 'id' as accessorKey, but cell handles display
            cell: (user: User) => {
                // Changed: Expect User object directly
                return user._count?.cas || 0;
            },
        },
        {
            header: "Date de création",
            accessorKey: "createdAt" as keyof User,
            cell: (user: User) => {
                // Changed: Expect User object directly
                return new Date(user.createdAt).toLocaleDateString();
            },
        },
    ];

    return (
        <div className="min-h-screen bg-gray-50">
            <div className=" mx-auto px-4 py-8">
                <div className="space-y-8">
                    {/* Header Section */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">
                                    Gestion des Utilisateurs
                                </h1>
                                <p className="text-gray-600 mt-2">
                                    Gérez les comptes utilisateurs et leurs
                                    permissions d'accès au système.
                                </p>
                            </div>
                            <Button
                                onClick={handleAdd}
                                className="flex-shrink-0"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                    className="w-5 h-5 mr-2"
                                >
                                    <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                                </svg>
                                Nouvel utilisateur
                            </Button>
                        </div>
                    </div>

                    {error && (
                        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                            <FormError message={error} />
                        </div>
                    )}

                    {/* Statistics Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg
                                            className="w-5 h-5 text-blue-600"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">
                                        Total utilisateurs
                                    </p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {users.length}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg
                                            className="w-5 h-5 text-green-600"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">
                                        Administrateurs
                                    </p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {
                                            users.filter(
                                                (u) => u.role === "ADMIN"
                                            ).length
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <svg
                                            className="w-5 h-5 text-purple-600"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">
                                        Éditeurs
                                    </p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {
                                            users.filter(
                                                (u) => u.role === "EDITOR"
                                            ).length
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Users Table */}
                    <div className="bg-white shadow-xl rounded-xl overflow-hidden border border-gray-200">
                        <div className="px-6 py-5 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-xl leading-7 font-semibold text-gray-800">
                                        Liste des Utilisateurs
                                    </h3>
                                    <p className="mt-1 text-sm text-gray-600">
                                        Visualisez, modifiez ou supprimez les
                                        utilisateurs existants.
                                    </p>
                                </div>
                                <div className="text-sm text-gray-500">
                                    {users.length} utilisateur
                                    {users.length > 1 ? "s" : ""} au total
                                </div>
                            </div>
                        </div>

                        <Table
                            data={users}
                            columns={columns}
                            pageSize={25}
                            pageSizeOptions={[10, 25, 50, 100]}
                            showPaginationInfo={true}
                            isLoading={isLoading}
                            actions={(row) => (
                                <div className="flex items-center justify-end space-x-2 py-2">
                                    <Button
                                        variant="secondary"
                                        size="sm"
                                        onClick={() => handleEdit(row)}
                                        className="flex items-center"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                            className="w-4 h-4 mr-1.5"
                                        >
                                            <path d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z" />
                                        </svg>
                                        <span className="hidden sm:inline">
                                            Modifier
                                        </span>
                                    </Button>
                                    <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => handleDelete(row)}
                                        disabled={row.id === loggedInUser?.id}
                                        className="flex items-center"
                                        title={
                                            row.id === loggedInUser?.id
                                                ? "Vous ne pouvez pas supprimer votre propre compte"
                                                : "Supprimer cet utilisateur"
                                        }
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                            className="w-4 h-4 sm:mr-1.5"
                                        >
                                            <path
                                                fillRule="evenodd"
                                                d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.58.177-2.34.296a.75.75 0 00-.707.707A48.69 48.69 0 002 6.499V16c0 .69.56 1.25 1.25 1.25H16.75c.69 0 1.25-.56 1.25-1.25V6.5c0-.434-.025-.864-.073-1.282a.75.75 0 00-.707-.707 48.689 48.689 0 00-2.34-.296V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.538-.054 2.208-.152l.002-.003L15 3.5l.003.002A2.5 2.5 0 0115 3.75V5H5V3.75a2.5 2.5 0 012.289-2.498L7.292 3.5l.002.003L7.5 3.5A2.5 2.5 0 017.75 3.5V4h2.25zM5 6.58V16h10V6.579A47.187 47.187 0 0110 6.5c-1.944 0-3.803.146-5.524.418l-.001.003L5 6.58z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                        <span className="hidden sm:inline">
                                            Supprimer
                                        </span>
                                    </Button>
                                </div>
                            )}
                        />
                    </div>
                </div>
            </div>

            <Modal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={
                    isEditing
                        ? "Modifier l'utilisateur"
                        : "Ajouter un utilisateur"
                }
            >
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-4">
                        <Input
                            id="email"
                            label="Email"
                            type="email"
                            value={formData.email}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    email: e.target.value,
                                }))
                            }
                            required
                        />

                        <Input
                            id="username"
                            label="Nom d'utilisateur"
                            value={formData.username}
                            onChange={(e) => {
                                const newUsername = e.target.value;
                                console.log(
                                    "Username input changed to:",
                                    newUsername
                                ); // Log username on input change
                                setFormData((prev) => ({
                                    ...prev,
                                    username: newUsername,
                                }));
                            }}
                            required
                        />

                        <Input
                            id="password"
                            label={
                                isEditing
                                    ? "Mot de passe (laisser vide pour ne pas changer)"
                                    : "Mot de passe"
                            }
                            type="password"
                            value={formData.password}
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    password: e.target.value,
                                }))
                            }
                            required={!isEditing}
                        />

                        <div>
                            <label
                                htmlFor="role"
                                className="block text-sm font-medium text-foreground"
                            >
                                Rôle
                            </label>
                            <select
                                id="role"
                                value={formData.role}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        role: e.target.value as
                                            | "ADMIN"
                                            | "EDITOR"
                                            | "BASIC",
                                    }))
                                }
                                required
                                className="mt-1 block w-full rounded-md border-input bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm"
                            >
                                <option value="BASIC">
                                    Utilisateur (BASIC)
                                </option>
                                <option value="EDITOR">Éditeur (EDITOR)</option>
                                <option value="VIEWER">Lecteur (VIEWER)</option>
                                <option value="ADMIN">
                                    Administrateur (ADMIN)
                                </option>
                            </select>
                        </div>

                        <Input
                            id="wilayaId"
                            label="ID Wilaya (optionnel)"
                            type="number"
                            value={
                                formData.wilayaId !== null
                                    ? formData.wilayaId
                                    : ""
                            }
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    wilayaId: e.target.value
                                        ? parseInt(e.target.value)
                                        : null,
                                }))
                            }
                        />
                    </div>

                    <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <Button
                            variant="outline"
                            onClick={() => setIsModalOpen(false)}
                            type="button"
                        >
                            Annuler
                        </Button>
                        <Button type="submit" isLoading={isLoading}>
                            {isEditing
                                ? "Enregistrer "
                                : "Ajouter l'utilisateur"}
                        </Button>
                    </div>
                </form>
            </Modal>
        </div>
    );
}
