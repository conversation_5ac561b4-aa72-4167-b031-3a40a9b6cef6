exports.id=9097,exports.ids=[9097],exports.modules={1377:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>q});var s=r(60687),a=r(43210),n=r.n(a),l=r(16189),i=r(85814),o=r.n(i),d=r(96330),c=r(69266),u=r(30036),m=r(30845);let x=({height:e=20,width:t="100%",className:r=""})=>(0,s.jsx)("div",{className:`animate-pulse bg-gray-300 rounded ${r}`,style:{height:e,width:t}}),g=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),p=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var h=r(26403),f=r(58963),b=r(71031);let j=({checked:e,onChange:t,onCheckedChange:r,className:a,id:l,...i})=>{let o=l||n().useId();return(0,s.jsxs)("label",{htmlFor:o,className:`relative inline-flex items-center cursor-pointer ${a||""}`,style:{userSelect:"none"},children:[(0,s.jsx)("input",{type:"checkbox",id:o,className:"sr-only peer",checked:e,onChange:e=>{t&&t(e),r&&r(e.target.checked)},...i}),(0,s.jsx)("div",{className:`w-11 h-6 flex items-center transition-colors duration-200 relative border-2 rounded-full overflow-hidden
          ${e?"bg-green-600 border-green-700":"bg-red-600 border-red-700"}
        `,children:(0,s.jsx)("span",{className:`inline-block h-5 w-5 rounded-full bg-white shadow transform transition-transform duration-200 absolute top-0.5
            ${e?"translate-x-5 border-green-700":"translate-x-1 border-red-700"} border-2`,style:{boxSizing:"border-box"}})})]})},y={ATTENTE:"En attente",ACCEPTE:"Accept\xe9",AJOURNE:"Ajourn\xe9",REJETE:"Rejet\xe9"},v={ATTENTE:"bg-gray-100 text-gray-800 border-gray-200",ACCEPTE:"bg-green-100 text-green-800 border-green-200",AJOURNE:"bg-orange-100 text-orange-800 border-orange-200",REJETE:"bg-red-100 text-red-800 border-red-200"};function N({value:e,onChange:t,disabled:r=!1,className:a=""}){return(0,s.jsx)("select",{value:e,onChange:e=>t(e.target.value),disabled:r,className:`
                border rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-300 focus:border-blue-400 
                transition-all ${a}
                ${r?"bg-gray-100 cursor-not-allowed":"bg-white"}
            `,children:Object.entries(y).map(([e,t])=>(0,s.jsx)("option",{value:e,children:t},e))})}function w({resolution:e,className:t=""}){return(0,s.jsx)("span",{className:`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border
            ${v[e]} ${t}
        `,children:y[e]})}let E={REGULARISE:"R\xe9gularis\xe9",AJOURNE:"Ajourn\xe9",NON_EXAMINE:"Non examin\xe9",REJETE:"Rejet\xe9"},k={REGULARISE:"bg-green-100 text-green-800 border-green-200",AJOURNE:"bg-orange-100 text-orange-800 border-orange-200",NON_EXAMINE:"bg-gray-100 text-gray-800 border-gray-200",REJETE:"bg-red-100 text-red-800 border-red-200"};function C({status:e,className:t=""}){return(0,s.jsx)("span",{className:`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border
            ${k[e]} ${t}
        `,children:E[e]})}var S=r(9171);function A(e){let t=[];return e.trim().split(/\s+/).forEach(e=>{let r=e.split(",");if(r.length>=2){let e=parseFloat(r[0]),s=parseFloat(r[1]),a=r.length>2?parseFloat(r[2]):0;isNaN(e)||isNaN(s)||t.push([e,s,a])}}),t}function L({onKMLLoaded:e,onError:t}){let[r,n]=(0,a.useState)(!1),l=(0,a.useRef)(null),i=async r=>{let s=r.target.files?.[0];if(!s)return;let a=s.name.toLowerCase().split(".").pop();if(!["kml"].includes(a||""))return void t("Veuillez s\xe9lectionner un fichier KML");n(!0);try{let t;if("kmz"===a)throw Error("Les fichiers KMZ ne sont pas encore support\xe9s. Veuillez extraire le fichier KML et l'importer directement.");t=await s.text();let r=new DOMParser().parseFromString(t,"text/xml");if(r.querySelector("parsererror"))throw Error("Le fichier KML n'est pas valide");let n=function(e){let t=[];return e.querySelectorAll("Placemark").forEach(e=>{let r={type:"Feature",properties:{},geometry:null},s=e.querySelector("name");s&&(r.properties.name=s.textContent);let a=e.querySelector("description");a&&(r.properties.description=a.textContent);let n=e.querySelector("ExtendedData");n&&n.querySelectorAll("Data").forEach(e=>{let t=e.getAttribute("name"),s=e.querySelector("value");t&&s&&(r.properties[t]=s.textContent)});let l=e.querySelector("Point coordinates"),i=e.querySelector("LineString coordinates"),o=e.querySelector("Polygon outerBoundaryIs LinearRing coordinates");if(l){let e=A(l.textContent||"");e.length>0&&(r.geometry={type:"Point",coordinates:e[0]})}else if(i){let e=A(i.textContent||"");e.length>0&&(r.geometry={type:"LineString",coordinates:e})}else if(o){let e=A(o.textContent||"");e.length>0&&(r.geometry={type:"Polygon",coordinates:[e]})}r.geometry&&t.push(r)}),{type:"FeatureCollection",features:t}}(r);if(!n||!n.features||0===n.features.length)throw Error("Aucune donn\xe9e g\xe9ographique trouv\xe9e dans le fichier");e(n,s.name)}catch(e){console.error("Erreur lors du traitement du fichier:",e),t(e instanceof Error?e.message:"Erreur lors du traitement du fichier")}finally{n(!1),l.current&&(l.current.value="")}};return(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{ref:l,type:"file",accept:".kml",onChange:i,className:"hidden"}),(0,s.jsx)(S.$,{onClick:()=>{l.current?.click()},disabled:r,variant:"outline",className:"flex items-center gap-2",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,s.jsx)("span",{children:"Traitement..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,s.jsx)("span",{children:"Importer KML"})]})}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Format support\xe9: .kml"})]})}function R({cas:e,onKMLUpdated:t,onKMLRemoved:r,onError:n,readOnly:l=!1}){let[i,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)(!1),u=async(r,s)=>{if(!e.id)return void n("ID du cas manquant");c(!0);try{let a=await fetch(`/api/cas/${e.id}/kml`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({kmlData:r,kmlFileName:s,geojson:r.features?.[0]?.geometry||null})});if(!a.ok)throw Error("Erreur lors de la mise \xe0 jour du KML");await a.json(),t(e.id,r,s),o(!1)}catch(e){console.error("Erreur:",e),n(e instanceof Error?e.message:"Erreur lors de la mise \xe0 jour")}finally{c(!1)}},m=async()=>{if(!e.id)return void n("ID du cas manquant");if(confirm("\xcates-vous s\xfbr de vouloir supprimer les donn\xe9es KML de ce cas ?")){c(!0);try{if(!(await fetch(`/api/cas/${e.id}/kml`,{method:"DELETE"})).ok)throw Error("Erreur lors de la suppression du KML");r(e.id)}catch(e){console.error("Erreur:",e),n(e instanceof Error?e.message:"Erreur lors de la suppression")}finally{c(!1)}}},x=(()=>{if(!e.kmlData)return null;let t=e.kmlData.features||[],r=new Set(t.map(e=>e.geometry?.type).filter(Boolean));return{featureCount:t.length,types:Array.from(r),fileName:e.kmlFileName||"Fichier KML"}})();return(0,s.jsxs)("div",{className:"bg-white border rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Donn\xe9es g\xe9ographiques KML"}),x&&!i&&!l&&(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(S.$,{onClick:()=>o(!0),variant:"outline",disabled:d,children:"Modifier"}),(0,s.jsx)(S.$,{onClick:m,variant:"outline",disabled:d,className:"text-red-600 hover:text-red-700",children:"Supprimer"})]}),l&&x&&(0,s.jsx)("div",{className:"text-sm text-gray-500 italic",children:"Mode lecture seule - Modification non autoris\xe9e"})]}),d&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,s.jsx)("span",{className:"ml-2 text-gray-600",children:"Traitement en cours..."})]}),!d&&(0,s.jsx)(s.Fragment,{children:x&&!i?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"font-medium text-green-800",children:"Donn\xe9es KML attach\xe9es"})]}),(0,s.jsxs)("div",{className:"text-sm text-green-700 space-y-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Fichier :"})," ",x.fileName]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"\xc9l\xe9ments :"})," ",x.featureCount]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Types :"})," ",x.types.join(", ")]})]})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Ces donn\xe9es g\xe9ographiques sont affich\xe9es sur la carte et peuvent \xeatre utilis\xe9es pour localiser pr\xe9cis\xe9ment ce cas d'assainissement."})]}):i?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-blue-800 text-sm",children:"Importez un nouveau fichier KML pour remplacer les donn\xe9es existantes."})}),(0,s.jsx)(L,{onKMLLoaded:u,onError:n}),(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsx)(S.$,{onClick:()=>o(!1),variant:"outline",children:"Annuler"})})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 text-center",children:[(0,s.jsx)("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"})}),(0,s.jsx)("p",{className:"text-gray-600 mb-3",children:"Aucune donn\xe9e g\xe9ographique KML attach\xe9e \xe0 ce cas"}),l?(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Mode lecture seule - Aucune donn\xe9e KML disponible"}):(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Importez un fichier KML pour enrichir ce cas avec des donn\xe9es g\xe9ographiques pr\xe9cises"})]}),!l&&(0,s.jsx)(L,{onKMLLoaded:u,onError:n})]})})]})}var M=r(48200),T=r(14146);let P=(0,u.default)(async()=>{},{loadableGenerated:{modules:["app\\cas\\[id]\\page.tsx -> @/app/components/LoadingSpinner"]},ssr:!1,loading:()=>(0,s.jsx)(x,{height:32,width:32})}),D=(0,u.default)(async()=>{},{loadableGenerated:{modules:["app\\cas\\[id]\\page.tsx -> @/app/components/FormError"]},ssr:!1,loading:()=>(0,s.jsx)(x,{height:20,width:200})}),_=(0,u.default)(async()=>{},{loadableGenerated:{modules:["app\\cas\\[id]\\page.tsx -> @/app/components/Button"]},ssr:!1,loading:()=>(0,s.jsx)(x,{height:36,width:100})}),I=(0,u.default)(async()=>{},{loadableGenerated:{modules:["app\\cas\\[id]\\page.tsx -> @/app/components/Input"]},ssr:!1,loading:()=>(0,s.jsx)(x,{height:36,width:200})}),F=(0,u.default)(async()=>{},{loadableGenerated:{modules:["app\\cas\\[id]\\page.tsx -> @/app/components/TextArea"]},ssr:!1,loading:()=>(0,s.jsx)(x,{height:80,width:200})}),O=(0,u.default)(async()=>{},{loadableGenerated:{modules:["app\\cas\\[id]\\page.tsx -> @/app/components/Modal"]},ssr:!1,loading:()=>(0,s.jsx)(x,{height:200,width:400})});function q(){let e=(0,l.useParams)();(0,l.useSearchParams)();let t=e.id,{afterCreate:r,afterUpdate:i,afterDelete:u}=(0,m.gD)(),[y,v]=(0,a.useState)(null),[E,k]=(0,a.useState)([]),[S,A]=(0,a.useState)([]),[L,q]=(0,a.useState)([]),[$,z]=(0,a.useState)([]),[V,U]=(0,a.useState)(!0),[B,J]=(0,a.useState)(!0),[K,G]=(0,a.useState)(!0),[W,H]=(0,a.useState)(!0),[Z,X]=(0,a.useState)(null),[Q,Y]=(0,a.useState)({description:"",secteurId:"",blocageDate:""}),[ee,et]=(0,a.useState)({description:"",secteurId:"",blocageDate:""}),[er,es]=(0,a.useState)(!1),[ea,en]=(0,a.useState)(null),[el,ei]=(0,a.useState)(null),[eo,ed]=(0,a.useState)(!1),[ec,eu]=(0,a.useState)(!1),[em,ex]=(0,a.useState)({}),[eg,ep]=(0,a.useState)(!1),[eh,ef]=(0,a.useState)(null),[eb,ej]=(0,a.useState)(!1),[ey,ev]=(0,a.useState)(null),[eN,ew]=(0,a.useState)(""),[eE,ek]=(0,a.useState)(null),[eC,eS]=(0,a.useState)(""),[eA,eL]=(0,a.useState)("ACCEPTE"),[eR,eM]=(0,a.useState)(""),[eT,eP]=(0,a.useState)(!1),[eD,e_]=(0,a.useState)([]),[eI,eF]=(0,a.useState)(!1),[eO,eq]=(0,a.useState)(null),[e$,ez]=(0,a.useState)(null),[eV,eU]=(0,a.useState)(null),[eB,eJ]=(0,a.useState)(!1),[eK,eG]=(0,a.useState)(null),eW=async()=>{if(y){eJ(!0),eG(null);try{let e=await fetch(`/api/cas/${t}/pdf`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok){let t=`Erreur HTTP ${e.status}`;try{let r=e.headers.get("content-type");if(r&&r.includes("application/json")){let r=await e.json();t=r.error||r.message||t}else{let r=await e.text();r&&(t=r)}}catch(e){console.warn("Could not parse error response:",e)}switch(e.status){case 401:t="Session expir\xe9e. Veuillez vous reconnecter.";break;case 403:t="Vous n'avez pas les permissions pour t\xe9l\xe9charger ce PDF.";break;case 404:t="Dossier non trouv\xe9.";break;case 500:t="Erreur serveur lors de la g\xe9n\xe9ration du PDF. Veuillez r\xe9essayer."}throw Error(t)}let r=await e.blob();if(r.type&&!r.type.includes("pdf"))throw Error("Le serveur n'a pas retourn\xe9 un fichier PDF valide.");let s=window.URL.createObjectURL(r),a=document.createElement("a");a.href=s;let n=new Date().toISOString().split("T")[0],l=y.nom?.replace(/[^a-zA-Z0-9]/g,"_")||"Dossier";a.download=`Fiche_Cas_${y.id}_${l}_${n}.pdf`,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s)}catch(e){console.error("Erreur lors du t\xe9l\xe9chargement PDF:",e),eG(e instanceof Error?e.message:"Erreur inconnue lors de la g\xe9n\xe9ration du PDF")}finally{eJ(!1)}}},eH=async e=>{let r=e.map(e=>e.resolution||"ATTENTE"),s=r.length>0&&r.every(e=>"ACCEPTE"===e);if(y&&y.regularisation!==s){v(t=>t?{...t,regularisation:s,blocage:e}:null);try{await c.uE.put(`/api/cas/${t}`,{regularisation:s})}catch(e){console.error("Failed to update Cas regularisation status on server:",e)}}else y&&v(t=>t?{...t,blocage:e}:null)},eZ=e=>{ex(t=>{let r=t.communeIds||[],s=r.includes(e)?r.filter(t=>t!==e):[...r,e];return{...t,communeIds:s}})},eX=async()=>{console.time("fetchAllCommunes"),eF(!0),eq(null);try{let e=await c.uE.get("/api/communes");e_(e)}catch(e){console.error("Failed to fetch communes:",e),eq(e.response?.data?.error||e.message||"Failed to fetch communes")}finally{eF(!1),console.timeEnd("fetchAllCommunes")}},eQ=async()=>{console.time("fetchProblematiques");try{let e=await c.uE.get("/api/problematiques");q(e)}catch(e){console.error("Failed to fetch problematiques:",e)}finally{console.timeEnd("fetchProblematiques")}},eY=async()=>{console.time("fetchEncrages");try{let e=await c.uE.get("/api/encrages");z(e)}catch(e){console.error("Failed to fetch encrages:",e)}finally{console.timeEnd("fetchEncrages")}};async function e0(e,t){let r=E.find(t=>t.id===e);r&&(ev(r),ew(r.solution?new Date(r.solution).toISOString().split("T")[0]:""),t?(eL(r.resolution||"ACCEPTE"),eM(r.detail_resolution||"")):(eL("ACCEPTE"),eM("")),eS(""),ej(!0))}let e1=async e=>{if(e&&e.preventDefault(),!ey||!eN)return void ek("Solution date is required");ek(null);try{if(!await c.uE.put(`/api/blocages/${ey.id}`,{regularise:"ACCEPTE"===eA,solution:new Date(eN).toISOString(),resolution:eA,detail_resolution:eR||null}))throw Error("Failed to submit solution date");let e=E.map(e=>e.id===ey.id?{...e,regularise:"ACCEPTE"===eA,solution:new Date(eN),resolution:eA,detail_resolution:eR||null}:e);k(e),await eH(e),console.log("\uD83D\uDD04 D\xe9clenchement du rafra\xeechissement apr\xe8s mise \xe0 jour de blocage"),await i("blocage"),ej(!1),ew(""),eL("ACCEPTE"),eM(""),ev(null)}catch(e){console.error("Error submitting solution date:",e),ek(e.message||"Failed to submit solution")}},e2=async()=>{U(!0),X(null);try{let e=await c.uE.get(`/api/cas/${t}`);v(e);let r=e.blocage||[];k(r),e&&await eH(r)}catch(e){console.error("Failed to fetch case details:",e),X(e.response?.data?.error||e.message||"Failed to fetch case details")}U(!1)},e5=e=>{Y({...Q,[e.target.name]:e.target.value})},e4=async e=>{if(e.preventDefault(),!Q.description||!Q.secteurId)return void en("La description et le secteur sont requis.");es(!0),en(null);try{await c.uE.post("/api/blocages",{...Q,casId:t,blocage:Q.blocageDate?new Date(Q.blocageDate):null});let e=await c.uE.get(`/api/cas/${t}/blocages`);k(e),await eH(e),Y({description:"",secteurId:"",blocageDate:""}),eP(!1),console.log("\uD83D\uDD04 D\xe9clenchement du rafra\xeechissement apr\xe8s cr\xe9ation de blocage"),await r("blocage")}catch(e){en(e.message||"Erreur lors de l'ajout du blocage.")}finally{es(!1)}},e3=e=>{ei(e),ed(!0)},e6=async()=>{if(el){X(null);try{await c.uE.delete(`/api/blocages/${el}`);let e=E.filter(e=>e.id!==el);k(e),await eH(e),ei(null),ed(!1),await u("blocage")}catch(e){console.error("Error deleting blocage:",e),X(e.response?.data?.error||e.message||"Error deleting blocage."),ei(null),ed(!1)}}},e7=()=>{ei(null),ed(!1)},e9=e=>{let{name:t,value:r,type:s}=e.target,a=r;"number"===s&&(a=""===r?void 0:parseFloat(r)),"checkbox"===e.target.type&&(a=e.target.checked),ex(e=>({...e,[t]:a}))},e8=async e=>{e.preventDefault(),ep(!0),ef(null);try{let e=em.communeIds?eD.filter(e=>em.communeIds.includes(String(e.id))).map(e=>({nom:e.nom,wilayaId:e.wilayaId})):[],{communeIds:r,...s}=em,a={...s,communes:e,geojson:e$||null};console.log("Donn\xe9es envoy\xe9es pour la modification:",a),await c.uE.put(`/api/cas/${t}`,a),eu(!1),await i("cas")}catch(e){console.error("Erreur lors de la modification:",e),ef("Erreur lors de la modification du cas.")}ep(!1)};(0,m.kF)("cas-details",e2,[t]),(0,m.kF)("communes-details",eX,[]),(0,m.kF)("problematiques-details",eQ,[]),(0,m.kF)("encrages-details",eY,[]);let[te,tt]=(0,a.useState)(null),{canWrite:tr,canDelete:ts,canUploadFiles:ta,isViewer:tn}=(0,T.Sk)();if(Z)return(0,s.jsxs)("div",{className:"  mx-auto p-1 text-center",children:[(0,s.jsx)(D,{message:Z}),(0,s.jsx)(_,{onClick:e2,className:"mt-4",children:"R\xe9essayer"})]});if(!y)return(0,s.jsx)("div",{className:"  mx-auto p-1 text-center",children:V?(0,s.jsx)(P,{}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{children:"Cas non trouv\xe9."}),(0,s.jsx)(o(),{href:"/cas",className:"text-purple-600 hover:underline",children:"Retour \xe0 la liste des cas"})]})});let tl=n().memo(function({blocage:e,blocageToDeleteId:t,onDelete:r,onCancelDelete:a,onConfirmDelete:n,onToggleRegularisation:l,isDeleteModalOpen:i,setIsDeleteModalOpen:o}){return(0,s.jsxs)("div",{className:"p-4 bg-white rounded-md shadow border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:e.description}),(0,s.jsxs)("p",{className:"text-xs text-gray-600",children:["Secteur: ",e.secteur?.nom||"N/A"]}),(0,s.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"R\xe9solution:"}),(0,s.jsx)(w,{resolution:e.resolution||"ATTENTE",className:"text-xs"})]}),e.detail_resolution&&(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-1 italic",children:e.detail_resolution})]}),(0,s.jsx)("div",{className:"flex space-x-2 flex-shrink-0",children:t===e.id?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(_,{variant:"primary",size:"icon",onClick:n,className:"p-1.5 rounded-full bg-green-100 hover:bg-green-200 text-green-700","aria-label":"Confirmer la suppression",children:(0,s.jsx)(g,{className:"w-5 h-5"})}),(0,s.jsx)(_,{variant:"secondary",size:"icon",onClick:()=>o(!1),className:"p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700","aria-label":"Annuler la suppression",children:(0,s.jsx)(p,{className:"w-5 h-5"})})]}):(0,s.jsx)(M.g1,{children:(0,s.jsx)(_,{variant:"destructive",size:"icon",onClick:()=>r(e.id),className:"ml-2 flex-shrink-0 p-1.5 rounded-full hover:bg-red-100 hover:text-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500","aria-label":"Supprimer le blocage",title:ts?void 0:"Vous n'avez pas les permissions pour supprimer des blocages",disabled:!ts,children:(0,s.jsx)(h.A,{className:"w-5 h-5"})})})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-2 pt-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(M._T,{fallback:(0,s.jsx)("div",{className:"inline-flex h-6 w-11 items-center rounded-full bg-gray-200 border border-gray-300 opacity-50 cursor-not-allowed",children:(0,s.jsx)("span",{className:`inline-block h-4 w-4 rounded-full bg-white shadow transform transition-transform ${e.regularise?"translate-x-6":"translate-x-1"}`})}),children:(0,s.jsx)(j,{checked:e.regularise,onChange:()=>l(e.id,e.regularise),disabled:!tr,className:"ACCEPTE"===e.resolution?"bg-green-600 border-green-600 focus:ring-green-500":"REJETE"===e.resolution?"bg-red-600 border-red-600 focus:ring-red-500":"AJOURNE"===e.resolution?"bg-orange-600 border-orange-600 focus:ring-orange-500":"bg-gray-400 border-gray-400 focus:ring-gray-500 inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border"})}),(0,s.jsx)("span",{className:`ml-2 text-sm font-medium cursor-pointer ${"ACCEPTE"===e.resolution?"text-green-700":"REJETE"===e.resolution?"text-red-700":"AJOURNE"===e.resolution?"text-orange-700":"text-gray-600"}`,onClick:()=>l(e.id,e.regularise),children:"ACCEPTE"===e.resolution?"Accept\xe9":"REJETE"===e.resolution?"Rejet\xe9":"AJOURNE"===e.resolution?"Ajourn\xe9":"En attente"}),e.solution&&(0,s.jsxs)("span",{className:`ml-3 text-sm ${e.regularise?"text-green-700":"text-red-700"}`,children:[e.regularise?"Solutionn\xe9":""," ","le:"," ",new Date(e.solution).toLocaleDateString("fr-CA")]})]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:e.blocage&&(0,s.jsxs)("p",{className:"text-xs text-gray-400",children:["Ajout\xe9 le:"," ",new Date(e.blocage).toLocaleDateString("fr-CA")]})})]})]})}),ti=n().memo(function({casData:e}){let t=function(e){if(!e||0===e.length)return"NON_EXAMINE";let t=e.map(e=>e.resolution||"ATTENTE");return t.every(e=>"ATTENTE"===e)?"NON_EXAMINE":t.some(e=>"REJETE"===e)?"REJETE":t.some(e=>"AJOURNE"===e)?"AJOURNE":t.every(e=>"ACCEPTE"===e)?"REGULARISE":"NON_EXAMINE"}(e?.blocage||[]);return(0,s.jsx)("div",{className:"ml-4",children:(0,s.jsx)(C,{status:t})})});return(0,s.jsxs)("div",{className:"  mx-auto p-2 md:p-2 bg-gray-50 min-h-screen",children:[V&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50 transition-opacity duration-300 animate-fade-in",children:(0,s.jsx)(P,{})}),(0,s.jsx)(O,{isOpen:eT,onClose:()=>eP(!1),title:"Ajouter une Nouvelle Contrainte",children:(0,s.jsxs)("form",{onSubmit:e4,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description de la contrainte"}),(0,s.jsx)(F,{name:"description",id:"description",value:Q.description,onChange:e5,required:!0,rows:3})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"secteurId",className:"block text-sm font-medium text-gray-700",children:"Secteur Concern\xe9"}),(0,s.jsxs)("select",{name:"secteurId",id:"secteurId",value:Q.secteurId,onChange:e5,required:!0,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",children:"S\xe9lectionner un secteur"}),S.map(e=>(0,s.jsx)("option",{value:e.id,children:e.nom},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"blocageDate",className:"block text-sm font-medium text-gray-700",children:"Date de blocage"}),(0,s.jsx)(I,{type:"date",name:"blocageDate",id:"blocageDate",value:Q.blocageDate,onChange:e5,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"})]}),ea&&(0,s.jsx)(D,{message:ea}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)(_,{type:"button",variant:"secondary",onClick:()=>eP(!1),children:"Annuler"}),(0,s.jsx)(M._T,{children:(0,s.jsx)(_,{type:"submit",disabled:er||!tr,title:tr?void 0:"Vous n'avez pas les permissions pour ajouter des blocages",children:er?(0,s.jsx)(P,{}):"Ajouter"})})]})]})}),ec?(0,s.jsxs)("form",{onSubmit:e8,className:"bg-white shadow-lg rounded-xl p-6 border border-gray-200 mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Modifier le Cas"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Communes"}),eI&&(0,s.jsx)(P,{}),eO&&(0,s.jsx)(D,{message:eO}),!eI&&!eO&&eD.length>0&&(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 max-h-60 overflow-y-auto p-2 border rounded",children:eD.map(e=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:`commune-${e.id}`,name:"communes",value:e.id,checked:em.communeIds?.includes(String(e.id))||!1,onChange:()=>eZ(String(e.id)),className:"h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"}),(0,s.jsx)("label",{htmlFor:`commune-${e.id}`,className:"ml-2 block text-sm text-gray-900",children:e.nom})]},e.id))}),!eI&&!eO&&0===eD.length&&(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Aucune commune disponible."})]}),eh&&(0,s.jsx)(D,{message:eh}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nom_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Nom du Cas"}),(0,s.jsx)(I,{type:"text",name:"nom",id:"nom_edit",value:em.nom||"",onChange:e9,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"date_depot_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date d\xe9p\xf4t dossier"}),(0,s.jsx)(I,{type:"date",name:"date_depot",id:"date_depot_edit",value:em.date_depot||"",onChange:e9})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nif_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"NIF"}),(0,s.jsx)(I,{type:"text",name:"nif",id:"nif_edit",value:em.nif||"",onChange:e9})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"nin_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"NIN"}),(0,s.jsx)(I,{type:"text",name:"nin",id:"nin_edit",value:em.nin||"",onChange:e9})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"superficie_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Superficie (Ha)"}),(0,s.jsx)(I,{type:"number",name:"superficie",id:"superficie_edit",value:em.superficie||"",onChange:e9})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"genre_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Genre"}),(0,s.jsxs)("select",{name:"genre",id:"genre_edit",value:em.genre||"",onChange:e9,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",children:"S\xe9lectionner un genre"}),(0,s.jsx)("option",{value:d.TypePersonne.PERSONNE_PHYSIQUE,children:"Personne Physique"}),(0,s.jsx)("option",{value:d.TypePersonne.PERSONNE_MORALE,children:"Personne Morale"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"problematiqueId_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Probl\xe9matique"}),(0,s.jsxs)("select",{name:"problematiqueId",id:"problematiqueId_edit",value:em.problematiqueId||"",onChange:e9,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",children:"S\xe9lectionner une probl\xe9matique"}),L.filter(e=>e.encrageId===em.encrageId).map(e=>(0,s.jsx)("option",{value:e.id,children:e.problematique},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"encrageId_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Encrage"}),(0,s.jsxs)("select",{name:"encrageId",id:"encrageId_edit",value:em.encrageId||"",onChange:e9,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm",children:[(0,s.jsx)("option",{value:"",disabled:!0,children:"S\xe9lectionner un encrage"}),(Array.isArray($)?$:[]).map(e=>(0,s.jsx)("option",{value:e.id,children:e.nom},e.id))]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{htmlFor:"observation_edit",className:"block text-sm font-medium text-gray-700 mb-1",children:"Observation"}),(0,s.jsx)(F,{name:"observation",id:"observation_edit",value:em.observation||"",onChange:e9,rows:3})]}),(0,s.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,s.jsx)(_,{type:"button",onClick:()=>eu(!1),variant:"secondary",children:"Annuler"}),(0,s.jsx)(M._T,{children:(0,s.jsx)(_,{type:"submit",isLoading:eg,disabled:eg||!tr,title:tr?void 0:"Vous n'avez pas les permissions pour modifier ce dossier",children:"Enregistrer"})})]})]}):(0,s.jsxs)("div",{className:"bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-100 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 ",children:V?(0,s.jsx)(x,{height:48,width:"100%"}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"flex items-center gap-4 min-w-0",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 truncate",children:y.nom}),(0,s.jsx)(ti,{casData:y})]})})}),(0,s.jsx)("div",{className:"flex items-center justify-end gap-4 min-w-0",children:(0,s.jsxs)("div",{className:"flex space-x-2 flex-shrink-0",children:[(0,s.jsxs)(o(),{href:`/cas/${t}/cartographie`,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"})}),"Cartographie"]}),(0,s.jsxs)(_,{onClick:eW,variant:"secondary",className:"flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white",isLoading:eB,disabled:eB,title:"T\xe9l\xe9charger la fiche PDF du dossier",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:eB?"G\xe9n\xe9ration...":"Fiche PDF"})]}),(0,s.jsx)(M._T,{children:(0,s.jsx)(_,{onClick:()=>eu(!ec),variant:ec?"secondary":"primary",title:tr?void 0:"Vous n'avez pas les permissions pour modifier ce dossier",disabled:!tr,children:ec?"Annuler la Modification":"Modifier le Dossier"})}),(0,s.jsx)(M._T,{children:(0,s.jsxs)(_,{onClick:()=>eP(!0),variant:"primary",className:"flex items-center space-x-2",title:tr?void 0:"Vous n'avez pas les permissions pour ajouter des blocages",disabled:!tr,children:[(0,s.jsx)(b.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Ajouter une Contrainte"})]})})]})}),tn&&(0,s.jsx)("div",{className:"p-3",children:(0,s.jsx)(M.Au,{message:"Vous \xeates en mode lecture seule. Vous pouvez consulter toutes les informations du dossier mais ne pouvez pas les modifier."})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-2 gap-6 xl:gap-8 p-3",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 space-y-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M10 9a3 3 0 100-6 3 3 0 000 6zM6 8a2 2 0 11-4 0 2 2 0 014 0zM1.49 15.326a.78.78 0 01-.358-.442 3 3 0 014.308-3.516 6.484 6.484 0 00-1.905 3.959c-.023.222-.014.442.025.654a4.97 4.97 0 01-2.07-.655zM16.44 15.98a4.97 4.97 0 002.07-.654.78.78 0 00.357-.442 3 3 0 00-4.308-3.517 6.484 6.484 0 011.907 3.96 2.32 2.32 0 01-.026.654zM18 8a2 2 0 11-4 0 2 2 0 014 0zM5.304 16.19a.844.844 0 01-.277-.71 5 5 0 019.947 0 .843.843 0 01-.277.71A6.975 6.975 0 0110 18a6.974 6.974 0 01-4.696-1.81z"})}),"Informations G\xe9n\xe9rales"]}),V?(0,s.jsx)(x,{height:120,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsxs)("dl",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Identifiant"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:y.nif?`NIF: ${y.nif}`:y.nin?`NIN: ${y.nin}`:"Non sp\xe9cifi\xe9"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Genre"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:y.genre===d.TypePersonne.PERSONNE_PHYSIQUE?"Personne Physique":y.genre===d.TypePersonne.PERSONNE_MORALE?"Personne Morale":"Non sp\xe9cifi\xe9"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Commune(s)"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:y.communes&&y.communes.length>0?y.communes.map(e=>e.nom).join(", "):"Non sp\xe9cifi\xe9e"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3 border-b border-gray-100",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Superficie"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:y.superficie?`${y.superficie} Ha`:"Non sp\xe9cifi\xe9e"})]}),(0,s.jsxs)("div",{className:"flex justify-between py-3",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Date d\xe9p\xf4t dossier"}),(0,s.jsx)("dd",{className:"text-sm text-gray-900",children:y.date_depot?new Date(y.date_depot).toLocaleDateString("fr-CA"):"Non sp\xe9cifi\xe9e"})]})]})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center mb-4",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-yellow-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zm0 16a3 3 0 01-3-3h6a3 3 0 01-3 3z",clipRule:"evenodd"})}),"Observations"]}),V?(0,s.jsx)(x,{height:40,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsx)("p",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:y.observation||"Aucune observation"})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center mb-4",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-blue-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})}),"Probl\xe9matique Associ\xe9e"]}),V?(0,s.jsx)(x,{height:80,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsxs)("dl",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-blue-700",children:"Probl\xe9matique"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:y.problematique?.problematique})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-blue-700",children:"Encrage Juridique"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:y.problematique?.encrage.nom})]})]})]}),(0,s.jsxs)("div",{className:"bg-green-50 rounded-lg p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center mb-4",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-green-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})}),"Informations du cr\xe9ateur"]}),V?(0,s.jsx)(x,{height:60,width:"100%",className:"transition-opacity duration-300"}):(0,s.jsxs)("dl",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-green-700",children:"Nom d'utilisateur"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:y.user?.username})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-green-700",children:"R\xf4le"}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:y.user?.role})]})]})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-2 bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100",children:[(0,s.jsx)("div",{className:"px-6 py-5 border-b border-gray-100 bg-purple-50",children:(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,s.jsxs)("svg",{className:"w-6 h-6 mr-2 text-purple-600",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{d:"M3.505 2.365A41.369 41.369 0 009 2c1.863 0 3.697.124 5.495.365 1.247.167 2.18 1.108 2.435 2.268a4.45 4.45 0 00-.577-.069 43.141 43.141 0 00-4.706 0C9.229 4.696 7.5 6.727 7.5 8.998v2.24c0 1.413.67 2.735 1.76 3.562l-2.98 2.98A.75.75 0 015 17.25v-3.443c-.501-.048-1-.106-1.495-.172C2.033 13.438 1 12.162 1 10.72V5.28c0-1.441 1.033-2.717 2.505-2.914z"}),(0,s.jsx)("path",{d:"M14 6c-.762 0-1.52.02-2.271.062C10.157 6.148 9 7.472 9 8.998v2.24c0 1.519 1.147 2.839 2.71 2.935.214.013.428.024.642.034.2.009.385.09.518.224l2.35 2.35a.75.75 0 001.28-.531v-2.07c1.453-.195 2.5-1.463 2.5-2.915V8.998c0-1.526-1.157-2.85-2.729-2.936A41.645 41.645 0 0014 6z"})]}),"Niveaux de Contrainte"]})}),(0,s.jsx)("div",{className:"p-1",children:B?(0,s.jsx)(x,{height:80,width:"100%"}):(0,s.jsx)("div",{className:"mt-2 space-y-3",children:E.length>0?E.map(e=>(0,s.jsx)(tl,{blocage:e,blocageToDeleteId:el,onDelete:e3,onCancelDelete:e7,onConfirmDelete:e6,onToggleRegularisation:e0,isDeleteModalOpen:eo,setIsDeleteModalOpen:ed},e.id)):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-gray-400",children:[(0,s.jsx)("svg",{className:"w-12 h-12 mb-2",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.75 17L6 21m0 0l-3.75-4M6 21V3"})}),(0,s.jsx)("span",{className:"text-lg",children:"Aucun point de blocage identifi\xe9 pour ce cas."})]})})})]}),(0,s.jsx)(O,{isOpen:eo,onClose:e7,title:"\xcates-vous s\xfbr de vouloir supprimer ce blocage ? Cette action est irr\xe9versible.",children:(0,s.jsxs)("div",{className:"mt-4 flex justify-end space-x-3",children:[(0,s.jsxs)(_,{variant:"secondary",onClick:e7,children:[" ","Annuler"]}),(0,s.jsx)(_,{variant:"destructive",onClick:e6,children:"Supprimer"})]})}),eb&&ey&&(0,s.jsx)(O,{isOpen:eb,onClose:()=>{ej(!1),ev(null),ek("")},title:` ${ey.description}`,children:(0,s.jsxs)("form",{onSubmit:e1,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"solutionDate_modal",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date de R\xe9solution"}),(0,s.jsx)(I,{type:"date",name:"solutionDate",id:"solutionDate_modal",value:eN,onChange:e=>ew(e.target.value),required:!0,className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"solutionResolution_modal",className:"block text-sm font-medium text-gray-700 mb-1",children:"R\xe9solution"}),(0,s.jsx)(N,{value:eA,onChange:eL,className:"mt-1 block w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"solutionDetailResolution_modal",className:"block text-sm font-medium text-gray-700 mb-1",children:"D\xe9tail de la r\xe9solution"}),(0,s.jsx)(F,{name:"solutionDetailResolution",id:"solutionDetailResolution_modal",value:eR,onChange:e=>eM(e.target.value),rows:3,placeholder:"D\xe9tails sur la r\xe9solution...",className:"mt-1 block w-full rounded-lg border-gray-300 shadow-sm py-2.5 px-3 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"})]}),eE&&(0,s.jsx)(D,{message:eE}),(0,s.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,s.jsx)(_,{type:"button",onClick:()=>{ej(!1),ev(null),ek("")},className:"bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Annuler"}),(0,s.jsx)(_,{type:"submit",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Valider"})]})]})}),y&&(0,s.jsxs)("section",{className:"mt-8",children:[(0,s.jsx)(R,{cas:y,onKMLUpdated:(e,t,r)=>{y&&v({...y,kmlData:t,kmlFileName:r}),eU(null)},onKMLRemoved:e=>{y&&v({...y,kmlData:null,kmlFileName:void 0}),eU(null)},onError:e=>{eU(e),setTimeout(()=>eU(null),5e3)},readOnly:!ta}),eV&&(0,s.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:eV})})]})]})}(0,u.default)(async()=>{},{loadableGenerated:{modules:["app\\cas\\[id]\\page.tsx -> @/app/components/CasMap"]},ssr:!1,loading:()=>(0,s.jsx)("div",{className:"w-full h-[300px] bg-gray-200 rounded animate-pulse"})})},9171:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(60687),a=r(82348),n=r(87056);function l({children:e,className:t,variant:r="primary",size:l="default",isLoading:i=!1,disabled:o,...d}){return(0,s.jsx)("button",{className:(0,a.QP)("rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/90 focus:ring-secondary/50",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-primary/50",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500/50"}[r],{default:"px-4 py-2",sm:"px-3 py-1.5 text-sm",icon:"p-2"}[l],t),disabled:i||o,...d,children:i?(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)(n.k,{})}):e})}},14146:(e,t,r)=>{"use strict";let s,a;r.d(t,{Sk:()=>l});var n=r(43210);function l(){let[e,t]=(0,n.useState)(void 0===s?null:s??null),[r,l]=(0,n.useState)(void 0===a?null:a??null),[i,o]=(0,n.useState)(void 0===s);return{user:e,permissions:r,loading:i,isAdmin:e?.role==="ADMIN",isEditor:e?.role==="EDITOR",isBasic:e?.role==="BASIC",isViewer:e?.role==="VIEWER",canRead:r?.canRead??!1,canWrite:r?.canWrite??!1,canDelete:r?.canDelete??!1,canManageUsers:r?.canManageUsers??!1,canUploadFiles:r?.canUploadFiles??!1,canSendMessages:r?.canSendMessages??!1,isReadOnly:r?.isReadOnly??!0}}},19587:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},26403:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},30036:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(49587),a=r.n(s)},35720:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))})},39109:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\cas\\[id]\\page.tsx","default")},48200:(e,t,r)=>{"use strict";r.d(t,{Au:()=>u,_T:()=>o,g1:()=>d,kM:()=>m,tK:()=>c});var s=r(60687);r(43210);var a=r(14146),n=r(66524),l=r(35720);function i({children:e,roles:t,permissions:r,fallback:n=null,requireAll:l=!1}){let{user:i,permissions:o,loading:d}=(0,a.Sk)();if(d)return(0,s.jsx)("div",{className:"animate-pulse bg-gray-200 h-4 w-16 rounded"});if(!i||!o)return(0,s.jsx)(s.Fragment,{children:n});if(t){let e=Array.isArray(t)?t:[t];if(!(l?e.every(e=>i.role===e):e.some(e=>i.role===e)))return(0,s.jsx)(s.Fragment,{children:n})}return!r||(l?r.every(e=>o[e]):r.some(e=>o[e]))?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)(s.Fragment,{children:n})}function o({children:e,fallback:t=null}){return(0,s.jsx)(i,{permissions:["canWrite"],fallback:t,children:e})}function d({children:e,fallback:t=null}){return(0,s.jsx)(i,{permissions:["canDelete"],fallback:t,children:e})}function c({className:e=""}){let{user:t,isReadOnly:r,loading:l}=(0,a.Sk)();return l||!t?null:(0,s.jsxs)("div",{className:`flex items-center space-x-2 ${e}`,children:[(0,s.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${(e=>{switch(e){case"ADMIN":return"bg-red-100 text-red-800 border-red-200";case"EDITOR":return"bg-green-100 text-green-800 border-green-200";case"BASIC":return"bg-blue-100 text-blue-800 border-blue-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(t.role)}`,children:t.role}),r&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200",children:[(0,s.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"Lecture seule"]})]})}function u({message:e="Cette action n'est pas disponible en mode lecture seule",className:t=""}){return(0,s.jsxs)("div",{className:`flex items-center space-x-2 text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded-md p-3 ${t}`,children:[(0,s.jsx)(l.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{children:e})]})}function m({requirePermission:e="canWrite",requireRole:t,children:r,disabledMessage:n,className:l="",disabled:i,title:o,...d}){let{user:c,permissions:u}=(0,a.Sk)(),m=u?.[e]??!1,x=!t||(Array.isArray(t)?t.includes(c?.role):c?.role===t),g=i||!m||!x,p=g&&n?n:o;return(0,s.jsx)("button",{...d,disabled:g,title:p,className:`${l} ${g?"opacity-50 cursor-not-allowed":""}`,children:r})}},49587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let s=r(14985)._(r(64963));function a(e,t){var r;let a={};"function"==typeof e&&(a.loader=e);let n={...a,...t};return(0,s.default)({...n,modules:null==(r=n.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let s=r(81208);function a(e){let{reason:t,children:r}=e;throw Object.defineProperty(new s.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},58963:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},64777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return i}});let s=r(60687),a=r(51215),n=r(29294),l=r(19587);function i(e){let{moduleIds:t}=e,r=n.workAsyncStorage.getStore();if(void 0===r)return null;let i=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;i.push(...t)}}return 0===i.length?null:(0,s.jsx)(s.Fragment,{children:i.map(e=>{let t=r.assetPrefix+"/_next/"+(0,l.encodeURIPath)(e);return e.endsWith(".css")?(0,s.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,a.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let s=r(60687),a=r(43210),n=r(56780),l=r(64777);function i(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},r=(0,a.lazy)(()=>t.loader().then(i)),d=t.loading;function c(e){let i=d?(0,s.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=!t.ssr||!!t.loading,c=o?a.Suspense:a.Fragment,u=t.ssr?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.PreloadChunks,{moduleIds:t.modules}),(0,s.jsx)(r,{...e})]}):(0,s.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(r,{...e})});return(0,s.jsx)(c,{...o?{fallback:i}:{},children:u})}return c.displayName="LoadableComponent",c}},69266:(e,t,r)=>{"use strict";async function s(e,t={}){let{method:r="GET",body:a}=t;console.log(`Making ${r} request to ${e}`),a&&console.log("Request body:",a);let n=await fetch(e,{method:r,headers:{"Content-Type":"application/json",...t.headers},credentials:t.credentials||"include",body:a?JSON.stringify(a):void 0});if(console.log("Response status:",n.status),!n.ok){let e=`HTTP error! status: ${n.status}`,t=null;try{let r=await n.text();if(console.log("Error response text:",r),r)try{t=JSON.parse(r),e=t?.error||t?.message||r}catch(t){e=r||e}}catch(e){console.warn("Could not read error response body:",e)}if(401===n.status)throw Error("Authentication required. Please log in again.");if(403===n.status)throw Error("Access denied. You don't have permission to perform this action.");if(404===n.status)throw Error("Resource not found.");else if(n.status>=500)throw Error("Server error. Please try again later.");throw Error(e)}if(204===n.status)return null;let l=await n.json();return console.log("Response data:",l),l}r.d(t,{Zq:()=>s,uE:()=>a});let a={get:(e,t)=>s(e,{...t,method:"GET"}),post:(e,t,r)=>s(e,{...r,method:"POST",body:t}),put:(e,t,r)=>s(e,{...r,method:"PUT",body:t}),patch:(e,t,r)=>s(e,{...r,method:"PATCH",body:t}),delete:(e,t,r)=>s(e,{...r,method:"DELETE",body:t})}},71031:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},78335:()=>{},87056:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});var s=r(60687),a=r(82348);function n({className:e,color:t="light",size:r="md"}){return(0,s.jsx)("div",{className:(0,a.QP)("border-2 rounded-full animate-spin",{light:"border-white/80 border-t-transparent",dark:"border-gray-700 border-t-transparent"}[t],{sm:"w-4 h-4",md:"w-5 h-5",lg:"w-8 h-8"}[r],e)})}},96487:()=>{}};