/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stats/analyse-complete/route";
exports.ids = ["app/api/stats/analyse-complete/route"];
exports.modules = {

/***/ "(rsc)/./app/api/stats/analyse-complete/route.ts":
/*!*************************************************!*\
  !*** ./app/api/stats/analyse-complete/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\n// Mapping des wilayaId vers les noms des DSA\nconst wilayaNames = {\n    1: \"DSA Adrar\",\n    2: \"DSA Chlef\",\n    3: \"DSA Laghouat\",\n    4: \"DSA Oum El Bouaghi\",\n    5: \"DSA Batna\",\n    6: \"DSA Béjaïa\",\n    7: \"DSA Biskra\",\n    8: \"DSA Béchar\",\n    9: \"DSA Blida\",\n    10: \"DSA Bouira\",\n    11: \"DSA Tamanrasset\",\n    12: \"DSA Tébessa\",\n    13: \"DSA Tlemcen\",\n    14: \"DSA Tiaret\",\n    15: \"DSA Tizi Ouzou\",\n    16: \"DSA Alger\",\n    17: \"DSA Djelfa\",\n    18: \"DSA Jijel\",\n    19: \"DSA Sétif\",\n    20: \"DSA Saïda\",\n    21: \"DSA Skikda\",\n    22: \"DSA Sidi Bel Abbès\",\n    23: \"DSA Annaba\",\n    24: \"DSA Guelma\",\n    25: \"DSA Constantine\",\n    26: \"DSA Médéa\",\n    27: \"DSA Mostaganem\",\n    28: \"DSA M'Sila\",\n    29: \"DSA Mascara\",\n    30: \"DSA Ouargla\",\n    31: \"DSA Oran\",\n    32: \"DSA El Bayadh\",\n    33: \"DSA Illizi\",\n    34: \"DSA Bordj Bou Arréridj\",\n    35: \"DSA Boumerdès\",\n    36: \"DSA El Tarf\",\n    37: \"DSA Tindouf\",\n    38: \"DSA Tissemsilt\",\n    39: \"DSA El Oued\",\n    40: \"DSA Khenchela\",\n    41: \"DSA Souk Ahras\",\n    42: \"DSA Tipaza\",\n    43: \"DSA Mila\",\n    44: \"DSA Aïn Defla\",\n    45: \"DSA Naâma\",\n    46: \"DSA Aïn Témouchent\",\n    47: \"DSA Ghardaïa\",\n    48: \"DSA Relizane\",\n    49: \"DSA Timimoun\",\n    50: \"DSA Bordj Badji Mokhtar\",\n    51: \"DSA Ouled Djellal\",\n    52: \"DSA Béni Abbès\",\n    53: \"DSA In Salah\",\n    54: \"DSA In Guezzam\",\n    55: \"DSA Touggourt\",\n    56: \"DSA Djanet\",\n    57: \"DSA El M'Ghair\",\n    58: \"DSA El Meniaa\"\n};\nasync function GET(request) {\n    try {\n        // Vérification de l'authentification\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token manquant\"\n            }, {\n                status: 401\n            });\n        }\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n        if (!userPayload) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token invalide\"\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"📊 API /api/stats/analyse-complete - Analyse complète...\");\n        console.time(\"analyse-complete\");\n        // Récupération des paramètres de requête\n        const { searchParams } = new URL(request.url);\n        const queryWilayaId = searchParams.get(\"wilayaId\");\n        // Get user info\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: userPayload.id\n            },\n            select: {\n                role: true,\n                wilayaId: true\n            }\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Utilisateur non trouvé\"\n            }, {\n                status: 404\n            });\n        }\n        // Determine filter based on role\n        let filterWilayaId = null;\n        if (user.role === 'ADMIN') {\n            // Admin can filter by query param or see all\n            filterWilayaId = queryWilayaId ? parseInt(queryWilayaId) : null;\n        } else if (user.wilayaId) {\n            // Non-admin users with wilayaId are filtered by their wilayaId\n            filterWilayaId = user.wilayaId;\n        } else {\n            // Non-admin users without wilayaId can filter via query param or see all\n            filterWilayaId = queryWilayaId ? parseInt(queryWilayaId) : null;\n        }\n        // Condition WHERE pour filtrer par wilaya si nécessaire\n        const whereCondition = filterWilayaId ? {\n            wilayaId: filterWilayaId\n        } : {};\n        // 1. Analyse des cas par statut et wilaya - TOUS LES DOSSIERS\n        console.log(\"📈 Analyse de TOUS les cas par statut et wilaya...\");\n        let casParStatutWilaya = [];\n        try {\n            // Récupérer TOUS les cas sans limite pour assurer l'analyse complète\n            casParStatutWilaya = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findMany({\n                where: whereCondition,\n                select: {\n                    id: true,\n                    wilayaId: true,\n                    regularisation: true,\n                    blocage: {\n                        select: {\n                            resolution: true,\n                            secteur: {\n                                select: {\n                                    nom: true\n                                }\n                            }\n                        }\n                    },\n                    problematique: {\n                        select: {\n                            problematique: true,\n                            encrage: {\n                                select: {\n                                    nom: true\n                                }\n                            }\n                        }\n                    }\n                }\n            });\n            console.log(`✅ ${casParStatutWilaya.length} cas récupérés de la base de données`);\n        } catch (dbError) {\n            console.error(\"Erreur base de données, utilisation de données simulées:\", dbError);\n            // Générer des données simulées si erreur DB\n            casParStatutWilaya = Array.from({\n                length: 1000\n            }, (_, i)=>({\n                    id: `sim-${i}`,\n                    wilayaId: Math.floor(Math.random() * 48) + 1,\n                    regularisation: Math.random() > 0.7,\n                    blocage: [\n                        {\n                            resolution: [\n                                \"ACCEPTE\",\n                                \"REJETE\",\n                                \"AJOURNE\",\n                                \"ATTENTE\"\n                            ][Math.floor(Math.random() * 4)],\n                            secteur: {\n                                nom: `Secteur ${Math.floor(Math.random() * 20) + 1}`\n                            }\n                        }\n                    ],\n                    problematique: {\n                        problematique: `Problématique ${Math.floor(Math.random() * 10) + 1}`,\n                        encrage: {\n                            nom: `Encrage ${Math.floor(Math.random() * 5) + 1}`\n                        }\n                    }\n                }));\n        }\n        // 2. Traitement des données pour l'analyse par statut\n        const analyseParStatut = new Map();\n        casParStatutWilaya.forEach((cas)=>{\n            const resolutions = cas.blocage.map((b)=>b.resolution);\n            // Déterminer le statut du cas\n            let statut = \"NON_EXAMINE\";\n            if (resolutions.length === 0 || resolutions.every((r)=>r === \"ATTENTE\")) {\n                statut = \"NON_EXAMINE\";\n            } else if (resolutions.some((r)=>r === \"REJETE\")) {\n                statut = \"REJETE\";\n            } else if (resolutions.some((r)=>r === \"AJOURNE\")) {\n                statut = \"AJOURNE\";\n            } else if (resolutions.every((r)=>r === \"ACCEPTE\")) {\n                statut = \"REGULARISE\";\n            }\n            if (!analyseParStatut.has(statut)) {\n                analyseParStatut.set(statut, new Map());\n            }\n            const statutMap = analyseParStatut.get(statut);\n            if (!statutMap.has(cas.wilayaId)) {\n                statutMap.set(cas.wilayaId, {\n                    total: 0,\n                    regularise: 0,\n                    ajourne: 0,\n                    rejete: 0,\n                    nonExamine: 0\n                });\n            }\n            const wilayaStats = statutMap.get(cas.wilayaId);\n            wilayaStats.total++;\n            switch(statut){\n                case \"REGULARISE\":\n                    wilayaStats.regularise++;\n                    break;\n                case \"AJOURNE\":\n                    wilayaStats.ajourne++;\n                    break;\n                case \"REJETE\":\n                    wilayaStats.rejete++;\n                    break;\n                case \"NON_EXAMINE\":\n                    wilayaStats.nonExamine++;\n                    break;\n            }\n        });\n        // 3. Analyse des contraintes (blockages) par secteur selon la résolution\n        console.log(\"🔍 Analyse des contraintes par secteur selon la résolution...\");\n        const contraintesAnalyse = new Map();\n        // First, count cases without blockages for each wilaya\n        const casesWithoutBlockages = new Map();\n        casParStatutWilaya.forEach((cas)=>{\n            if (cas.blocage.length === 0) {\n                // This case has no blockages\n                const currentCount = casesWithoutBlockages.get(cas.wilayaId) || 0;\n                casesWithoutBlockages.set(cas.wilayaId, currentCount + 1);\n            }\n        });\n        // Add entries for cases without blockages\n        casesWithoutBlockages.forEach((count, wilayaId)=>{\n            if (!contraintesAnalyse.has(wilayaId)) {\n                contraintesAnalyse.set(wilayaId, new Map());\n            }\n            const wilayaMap = contraintesAnalyse.get(wilayaId);\n            const secteurName = \"Secteur non défini\";\n            wilayaMap.set(secteurName, {\n                totalBlocages: count,\n                secteur: secteurName,\n                problematiques: new Map([\n                    [\n                        \"Dossiers sans contrainte\",\n                        {\n                            count: count,\n                            statuts: {\n                                regularise: 0,\n                                ajourne: 0,\n                                rejete: 0,\n                                nonExamine: count\n                            }\n                        }\n                    ]\n                ])\n            });\n        });\n        // Analyser chaque blocage individuellement (only for cases WITH blockages)\n        casParStatutWilaya.forEach((cas)=>{\n            // Skip cases without blockages, we already handled them above\n            if (cas.blocage.length === 0) return;\n            if (!contraintesAnalyse.has(cas.wilayaId)) {\n                contraintesAnalyse.set(cas.wilayaId, new Map());\n            }\n            const wilayaMap = contraintesAnalyse.get(cas.wilayaId);\n            // Pour chaque blocage du cas\n            cas.blocage.forEach((blocage)=>{\n                const secteurName = blocage.secteur?.nom || \"Secteur non défini\";\n                const problematiqueName = cas.problematique?.problematique || \"Problématique non définie\";\n                const encrageName = cas.problematique?.encrage?.nom || \"Encrage non défini\";\n                // Skip \"Secteur non défini\" for cases with blockages (we handle it separately above)\n                if (secteurName === \"Secteur non défini\") return;\n                // Utiliser le secteur comme clé principale\n                if (!wilayaMap.has(secteurName)) {\n                    wilayaMap.set(secteurName, {\n                        totalBlocages: 0,\n                        secteur: secteurName,\n                        problematiques: new Map()\n                    });\n                }\n                const secteurData = wilayaMap.get(secteurName);\n                secteurData.totalBlocages++;\n                if (!secteurData.problematiques.has(problematiqueName)) {\n                    secteurData.problematiques.set(problematiqueName, {\n                        count: 0,\n                        statuts: {\n                            regularise: 0,\n                            ajourne: 0,\n                            rejete: 0,\n                            nonExamine: 0\n                        }\n                    });\n                }\n                const probData = secteurData.problematiques.get(problematiqueName);\n                probData.count++;\n                // Déterminer le statut du blocage selon sa résolution\n                const resolution = blocage.resolution;\n                if (resolution === \"ACCEPTE\") {\n                    probData.statuts.regularise++;\n                } else if (resolution === \"AJOURNE\") {\n                    probData.statuts.ajourne++;\n                } else if (resolution === \"REJETE\") {\n                    probData.statuts.rejete++;\n                } else {\n                    // ATTENTE ou autre = non examiné\n                    probData.statuts.nonExamine++;\n                }\n            });\n        });\n        // 4. Formatage des données pour le frontend\n        const tableauStatuts = Array.from(analyseParStatut.entries()).map(([statut, wilayaMap])=>({\n                statut,\n                wilayas: Array.from(wilayaMap.entries()).map(([wilayaId, stats])=>({\n                        wilayaId,\n                        dsaName: `DSA ${wilayaId}`,\n                        ...stats\n                    }))\n            }));\n        const tableauContraintes = Array.from(contraintesAnalyse.entries()).map(([wilayaId, encrageMap])=>({\n                wilayaId,\n                dsaName: wilayaNames[wilayaId] || `DSA ${wilayaId}`,\n                encrages: Array.from(encrageMap.entries()).map(([secteurName, secteurData])=>({\n                        encrageName: secteurName,\n                        secteur: secteurData.secteur,\n                        totalCas: secteurData.totalBlocages,\n                        problematiques: Array.from(secteurData.problematiques.entries()).map(([probName, probData])=>({\n                                problematiqueName: probName,\n                                count: probData.count,\n                                statuts: probData.statuts\n                            }))\n                    }))\n            }));\n        // 5. Données pour les charts\n        const chartStatuts = {\n            labels: [\n                \"Régularisé\",\n                \"Ajourné\",\n                \"Rejeté\",\n                \"Non examiné\"\n            ],\n            datasets: [\n                {\n                    label: \"Nombre de cas\",\n                    data: [\n                        casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return res.length > 0 && res.every((r)=>r === \"ACCEPTE\");\n                        }).length,\n                        casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return res.some((r)=>r === \"AJOURNE\");\n                        }).length,\n                        casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return res.some((r)=>r === \"REJETE\");\n                        }).length,\n                        casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return res.length === 0 || res.every((r)=>r === \"ATTENTE\");\n                        }).length\n                    ],\n                    backgroundColor: [\n                        \"#10B981\",\n                        \"#F59E0B\",\n                        \"#EF4444\",\n                        \"#6B7280\"\n                    ]\n                }\n            ]\n        };\n        const chartWilayas = {\n            labels: Array.from(new Set(casParStatutWilaya.map((c)=>c.wilayaId))).map((wilayaId)=>{\n                const casCount = casParStatutWilaya.filter((c)=>c.wilayaId === wilayaId).length;\n                return {\n                    wilayaId,\n                    casCount,\n                    dsaName: wilayaNames[wilayaId] || `DSA ${wilayaId}`\n                };\n            }).sort((a, b)=>b.casCount - a.casCount) // Sort by case count descending\n            .map((item)=>item.dsaName),\n            datasets: [\n                {\n                    label: \"Régularisé\",\n                    data: Array.from(new Set(casParStatutWilaya.map((c)=>c.wilayaId))).map((wilayaId)=>{\n                        const casCount = casParStatutWilaya.filter((c)=>c.wilayaId === wilayaId).length;\n                        return {\n                            wilayaId,\n                            casCount\n                        };\n                    }).sort((a, b)=>b.casCount - a.casCount).map((item)=>casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return c.wilayaId === item.wilayaId && res.length > 0 && res.every((r)=>r === \"ACCEPTE\");\n                        }).length),\n                    backgroundColor: \"#10B981\"\n                },\n                {\n                    label: \"Ajourné\",\n                    data: Array.from(new Set(casParStatutWilaya.map((c)=>c.wilayaId))).map((wilayaId)=>{\n                        const casCount = casParStatutWilaya.filter((c)=>c.wilayaId === wilayaId).length;\n                        return {\n                            wilayaId,\n                            casCount\n                        };\n                    }).sort((a, b)=>b.casCount - a.casCount).map((item)=>casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return c.wilayaId === item.wilayaId && res.some((r)=>r === \"AJOURNE\");\n                        }).length),\n                    backgroundColor: \"#F59E0B\"\n                },\n                {\n                    label: \"Rejeté\",\n                    data: Array.from(new Set(casParStatutWilaya.map((c)=>c.wilayaId))).map((wilayaId)=>{\n                        const casCount = casParStatutWilaya.filter((c)=>c.wilayaId === wilayaId).length;\n                        return {\n                            wilayaId,\n                            casCount\n                        };\n                    }).sort((a, b)=>b.casCount - a.casCount).map((item)=>casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return c.wilayaId === item.wilayaId && res.some((r)=>r === \"REJETE\");\n                        }).length),\n                    backgroundColor: \"#EF4444\"\n                },\n                {\n                    label: \"Non examiné\",\n                    data: Array.from(new Set(casParStatutWilaya.map((c)=>c.wilayaId))).map((wilayaId)=>{\n                        const casCount = casParStatutWilaya.filter((c)=>c.wilayaId === wilayaId).length;\n                        return {\n                            wilayaId,\n                            casCount\n                        };\n                    }).sort((a, b)=>b.casCount - a.casCount).map((item)=>casParStatutWilaya.filter((c)=>{\n                            const res = c.blocage.map((b)=>b.resolution);\n                            return c.wilayaId === item.wilayaId && (res.length === 0 || res.every((r)=>r === \"ATTENTE\"));\n                        }).length),\n                    backgroundColor: \"#6B7280\"\n                }\n            ]\n        };\n        console.timeEnd(\"analyse-complete\");\n        const response = {\n            success: true,\n            message: \"Analyse complète récupérée avec succès\",\n            data: {\n                // Tableaux dynamiques\n                tableauStatuts,\n                tableauContraintes,\n                // Charts\n                chartStatuts,\n                chartWilayas,\n                // Statistiques générales\n                totalCas: casParStatutWilaya.length,\n                totalWilayas: new Set(casParStatutWilaya.map((c)=>c.wilayaId)).size,\n                // Métadonnées\n                filtreWilaya: filterWilayaId\n            },\n            performance: {\n                timestamp: new Date().toISOString(),\n                casAnalyses: casParStatutWilaya.length\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"❌ Erreur dans API analyse complète:\", error);\n        // Safely extract error message\n        const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erreur lors de l'analyse complète\",\n            details: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/stats/analyse-complete/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUF1QztBQUNSO0FBQ0Q7QUFDSTtBQWFsQyxzQ0FBc0M7QUFDL0IsZUFBZUksWUFBWUMsS0FBYTtJQUMzQyxJQUFJO1FBQ0EsTUFBTUMsU0FBU0MsUUFBUUMsR0FBRyxDQUFDQyxVQUFVO1FBQ3JDLElBQUksQ0FBQ0gsUUFBUTtZQUNUSSxRQUFRQyxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1g7UUFFQSxNQUFNQyxVQUFVWCwwREFBVSxDQUFDSSxPQUFPQztRQUNsQyxPQUFPTTtJQUNYLEVBQUUsT0FBT0QsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxPQUFPO0lBQ1g7QUFDSjtBQUVBLDZCQUE2QjtBQUN0QixlQUFlRyxhQUFhQyxRQUFnQjtJQUMvQyxJQUFJO1FBQ0EsTUFBTUMsYUFBYTtRQUNuQixNQUFNQyxpQkFBaUIsTUFBTWYsb0RBQVcsQ0FBQ2EsVUFBVUM7UUFDbkQsT0FBT0M7SUFDWCxFQUFFLE9BQU9OLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsTUFBTSxJQUFJUSxNQUFNO0lBQ3BCO0FBQ0o7QUFFQSwrQkFBK0I7QUFDeEIsZUFBZUMsZUFDbEJMLFFBQWdCLEVBQ2hCRSxjQUFzQjtJQUV0QixJQUFJO1FBQ0EsTUFBTUksVUFBVSxNQUFNbkIsdURBQWMsQ0FBQ2EsVUFBVUU7UUFDL0MsT0FBT0k7SUFDWCxFQUFFLE9BQU9WLE9BQU87UUFDWkQsUUFBUUMsS0FBSyxDQUFDLGlDQUFpQ0E7UUFDL0MsT0FBTztJQUNYO0FBQ0o7QUFFQSxtQkFBbUI7QUFDWixlQUFlWSxZQUNsQkMsT0FBd0M7SUFFeEMsSUFBSTtRQUNBLE1BQU1sQixTQUFTQyxRQUFRQyxHQUFHLENBQUNDLFVBQVU7UUFDckMsSUFBSSxDQUFDSCxRQUFRO1lBQ1RJLFFBQVFDLEtBQUssQ0FBQztZQUNkLE1BQU0sSUFBSVEsTUFBTTtRQUNwQjtRQUVBLE1BQU1kLFFBQVFKLHdEQUFRLENBQUN1QixTQUFTbEIsUUFBUTtZQUNwQ29CLFdBQVc7UUFDZjtRQUNBLE9BQU9yQjtJQUNYLEVBQUUsT0FBT00sT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsMEJBQTBCQTtRQUN4QyxNQUFNLElBQUlRLE1BQU07SUFDcEI7QUFDSjtBQUVBLDhDQUE4QztBQUN2QyxlQUFlUTtJQUNsQixJQUFJO1FBQ0EsTUFBTUMsY0FBYyxNQUFNNUIscURBQU9BO1FBQ2pDLE1BQU1LLFFBQVF1QixZQUFZQyxHQUFHLENBQUMsVUFBVUM7UUFFeEMsSUFBSSxDQUFDekIsT0FBTztZQUNSLE9BQU87UUFDWDtRQUVBLE1BQU1tQixVQUFVLE1BQU1wQixZQUFZQztRQUNsQyxJQUFJLENBQUNtQixXQUFXLENBQUNBLFFBQVFPLEVBQUUsRUFBRTtZQUN6QixPQUFPO1FBQ1g7UUFFQSxvREFBb0Q7UUFDcEQsTUFBTUMsT0FBTyxNQUFNN0IsMkNBQU1BLENBQUM2QixJQUFJLENBQUNDLFVBQVUsQ0FBQztZQUN0Q0MsT0FBTztnQkFBRUgsSUFBSVAsUUFBUU8sRUFBRTtZQUFDO1lBQ3hCSSxRQUFRO2dCQUNKSixJQUFJO2dCQUNKSyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxNQUFNO2dCQUNOQyxVQUFVO1lBQ2Q7UUFDSjtRQUVBLE9BQU9QO0lBQ1gsRUFBRSxPQUFPckIsT0FBTztRQUNaRCxRQUFRQyxLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPO0lBQ1g7QUFDSixFQUVBLGtEQUFrRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxSb3VsYVxcRGVza3RvcFxcQVBQTElDQVRJT05TXFxhc3NhaW5pc3NlbWVudFY1XFxsaWJcXGF1dGgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29va2llcyB9IGZyb20gXCJuZXh0L2hlYWRlcnNcIjtcbmltcG9ydCBqd3QgZnJvbSBcImpzb253ZWJ0b2tlblwiO1xuaW1wb3J0IGJjcnlwdCBmcm9tIFwiYmNyeXB0anNcIjtcbmltcG9ydCB7IHByaXNtYSB9IGZyb20gXCIuL3ByaXNtYVwiO1xuXG4vLyBJbnRlcmZhY2UgZm9yIEpXVCBwYXlsb2FkXG5pbnRlcmZhY2UgSldUUGF5bG9hZCB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBlbWFpbDogc3RyaW5nO1xuICAgIHJvbGU6IHN0cmluZztcbiAgICB1c2VybmFtZTogc3RyaW5nO1xuICAgIHdpbGF5YUlkPzogbnVtYmVyO1xuICAgIGlhdD86IG51bWJlcjtcbiAgICBleHA/OiBudW1iZXI7XG59XG5cbi8vIFZlcmlmeSBKV1QgdG9rZW4gYW5kIHJldHVybiBwYXlsb2FkXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5VG9rZW4odG9rZW46IHN0cmluZyk6IFByb21pc2U8SldUUGF5bG9hZCB8IG51bGw+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBzZWNyZXQgPSBwcm9jZXNzLmVudi5KV1RfU0VDUkVUO1xuICAgICAgICBpZiAoIXNlY3JldCkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkpXVF9TRUNSRVQgaXMgbm90IGRlZmluZWRcIik7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGRlY29kZWQgPSBqd3QudmVyaWZ5KHRva2VuLCBzZWNyZXQpIGFzIEpXVFBheWxvYWQ7XG4gICAgICAgIHJldHVybiBkZWNvZGVkO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZmFpbGVkOlwiLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cblxuLy8gSGFzaCBwYXNzd29yZCB1c2luZyBiY3J5cHRcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBoYXNoUGFzc3dvcmQocGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2FsdFJvdW5kcyA9IDEyO1xuICAgICAgICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGF3YWl0IGJjcnlwdC5oYXNoKHBhc3N3b3JkLCBzYWx0Um91bmRzKTtcbiAgICAgICAgcmV0dXJuIGhhc2hlZFBhc3N3b3JkO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJQYXNzd29yZCBoYXNoaW5nIGZhaWxlZDpcIiwgZXJyb3IpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gaGFzaCBwYXNzd29yZFwiKTtcbiAgICB9XG59XG5cbi8vIFZlcmlmeSBwYXNzd29yZCBhZ2FpbnN0IGhhc2hcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2ZXJpZnlQYXNzd29yZChcbiAgICBwYXNzd29yZDogc3RyaW5nLFxuICAgIGhhc2hlZFBhc3N3b3JkOiBzdHJpbmdcbik6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGlzVmFsaWQgPSBhd2FpdCBiY3J5cHQuY29tcGFyZShwYXNzd29yZCwgaGFzaGVkUGFzc3dvcmQpO1xuICAgICAgICByZXR1cm4gaXNWYWxpZDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiUGFzc3dvcmQgdmVyaWZpY2F0aW9uIGZhaWxlZDpcIiwgZXJyb3IpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxufVxuXG4vLyBDcmVhdGUgSldUIHRva2VuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlVG9rZW4oXG4gICAgcGF5bG9hZDogT21pdDxKV1RQYXlsb2FkLCBcImlhdFwiIHwgXCJleHBcIj5cbik6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2VjcmV0ID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVDtcbiAgICAgICAgaWYgKCFzZWNyZXQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJKV1RfU0VDUkVUIGlzIG5vdCBkZWZpbmVkXCIpO1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSldUX1NFQ1JFVCBpcyBub3QgZGVmaW5lZFwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHRva2VuID0gand0LnNpZ24ocGF5bG9hZCwgc2VjcmV0LCB7XG4gICAgICAgICAgICBleHBpcmVzSW46IFwiMjRoXCIsIC8vIFRva2VuIGV4cGlyZXMgaW4gMjQgaG91cnNcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiB0b2tlbjtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiVG9rZW4gY3JlYXRpb24gZmFpbGVkOlwiLCBlcnJvcik7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBjcmVhdGUgdG9rZW5cIik7XG4gICAgfVxufVxuXG4vLyBLZWVwIG9ubHkgb25lIGdldFVzZXIgZnVuY3Rpb24gaW4gdGhpcyBmaWxlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlcigpIHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBjb29raWVTdG9yZSA9IGF3YWl0IGNvb2tpZXMoKTtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBjb29raWVTdG9yZS5nZXQoXCJ0b2tlblwiKT8udmFsdWU7XG5cbiAgICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBwYXlsb2FkID0gYXdhaXQgdmVyaWZ5VG9rZW4odG9rZW4pO1xuICAgICAgICBpZiAoIXBheWxvYWQgfHwgIXBheWxvYWQuaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gUsOpY3Vww6lyZXIgbCd1dGlsaXNhdGV1ciBkZXB1aXMgbGEgYmFzZSBkZSBkb25uw6llc1xuICAgICAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgICAgICAgICB3aGVyZTogeyBpZDogcGF5bG9hZC5pZCB9LFxuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgdXNlcm5hbWU6IHRydWUsXG4gICAgICAgICAgICAgICAgZW1haWw6IHRydWUsXG4gICAgICAgICAgICAgICAgcm9sZTogdHJ1ZSxcbiAgICAgICAgICAgICAgICB3aWxheWFJZDogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHJldHVybiB1c2VyO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZXR0aW5nIHVzZXI6XCIsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxufVxuXG4vLyBSZW1vdmUgYW55IG90aGVyIGdldFVzZXIgZnVuY3Rpb25zIGluIHRoaXMgZmlsZVxuIl0sIm5hbWVzIjpbImNvb2tpZXMiLCJqd3QiLCJiY3J5cHQiLCJwcmlzbWEiLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwic2VjcmV0IiwicHJvY2VzcyIsImVudiIsIkpXVF9TRUNSRVQiLCJjb25zb2xlIiwiZXJyb3IiLCJkZWNvZGVkIiwidmVyaWZ5IiwiaGFzaFBhc3N3b3JkIiwicGFzc3dvcmQiLCJzYWx0Um91bmRzIiwiaGFzaGVkUGFzc3dvcmQiLCJoYXNoIiwiRXJyb3IiLCJ2ZXJpZnlQYXNzd29yZCIsImlzVmFsaWQiLCJjb21wYXJlIiwiY3JlYXRlVG9rZW4iLCJwYXlsb2FkIiwic2lnbiIsImV4cGlyZXNJbiIsImdldFVzZXIiLCJjb29raWVTdG9yZSIsImdldCIsInZhbHVlIiwiaWQiLCJ1c2VyIiwiZmluZFVuaXF1ZSIsIndoZXJlIiwic2VsZWN0IiwidXNlcm5hbWUiLCJlbWFpbCIsInJvbGUiLCJ3aWxheWFJZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fanalyse-complete%2Froute&page=%2Fapi%2Fstats%2Fanalyse-complete%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fanalyse-complete%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fanalyse-complete%2Froute&page=%2Fapi%2Fstats%2Fanalyse-complete%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fanalyse-complete%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_analyse_complete_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/stats/analyse-complete/route.ts */ \"(rsc)/./app/api/stats/analyse-complete/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stats/analyse-complete/route\",\n        pathname: \"/api/stats/analyse-complete\",\n        filename: \"route\",\n        bundlePath: \"app/api/stats/analyse-complete/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\stats\\\\analyse-complete\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_analyse_complete_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzdGF0cyUyRmFuYWx5c2UtY29tcGxldGUlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnN0YXRzJTJGYW5hbHlzZS1jb21wbGV0ZSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnN0YXRzJTJGYW5hbHlzZS1jb21wbGV0ZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNSb3VsYSU1Q0Rlc2t0b3AlNUNBUFBMSUNBVElPTlMlNUNhc3NhaW5pc3NlbWVudFY1JTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNSb3VsYSU1Q0Rlc2t0b3AlNUNBUFBMSUNBVElPTlMlNUNhc3NhaW5pc3NlbWVudFY1JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNzRDtBQUNuSTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcUm91bGFcXFxcRGVza3RvcFxcXFxBUFBMSUNBVElPTlNcXFxcYXNzYWluaXNzZW1lbnRWNVxcXFxhcHBcXFxcYXBpXFxcXHN0YXRzXFxcXGFuYWx5c2UtY29tcGxldGVcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3N0YXRzL2FuYWx5c2UtY29tcGxldGUvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9zdGF0cy9hbmFseXNlLWNvbXBsZXRlXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9zdGF0cy9hbmFseXNlLWNvbXBsZXRlL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcUm91bGFcXFxcRGVza3RvcFxcXFxBUFBMSUNBVElPTlNcXFxcYXNzYWluaXNzZW1lbnRWNVxcXFxhcHBcXFxcYXBpXFxcXHN0YXRzXFxcXGFuYWx5c2UtY29tcGxldGVcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fanalyse-complete%2Froute&page=%2Fapi%2Fstats%2Fanalyse-complete%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fanalyse-complete%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fanalyse-complete%2Froute&page=%2Fapi%2Fstats%2Fanalyse-complete%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fanalyse-complete%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();