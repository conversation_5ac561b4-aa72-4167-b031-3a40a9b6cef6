"use client";

import React, { useRef, useState } from "react";
import dynamic from "next/dynamic";
import * as toGeoJSON from "@tmcw/togeojson";
import { apiClient } from "@/lib/api-client"; // Assurez-vous que le chemin d'importation est correct

// Chargement dynamique des composants react-leaflet
const Map = dynamic(() => import("react-leaflet").then((mod) => mod.Map), {
    ssr: false,
});
const TileLayer = dynamic(
    () => import("react-leaflet").then((mod) => mod.TileLayer),
    { ssr: false }
);
const GeoJSON = dynamic(
    () => import("react-leaflet").then((mod) => mod.GeoJSON),
    { ssr: false }
);

interface GeoJsonDropzoneProps {
    value?: any;
    onChange: (geojson: any) => void;
}

const GeoJsonDropzone: React.FC<GeoJsonDropzoneProps> = ({
    value,
    onChange,
}) => {
    const [geojson, setGeojson] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFile = async (file: File) => {
        setError(null);
        try {
            const text = await file.text();
            const parser = new DOMParser();
            const kml = parser.parseFromString(text, "text/xml");
            const converted = toGeoJSON.kml(kml);
            setGeojson(converted);
            onChange(converted);
        } catch (e) {
            setError("Erreur lors de la conversion du fichier KML.");
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFile(e.dataTransfer.files[0]);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFile(e.target.files[0]);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        // Remplacez ceci par vos autres champs de formulaire
        const formData = new FormData();
        // Ajoutez vos autres champs de formulaire ici

        // Ajoutez le geojson au formData si disponible
        if (geojson) {
            formData.append("geojson", JSON.stringify(geojson));
        }

        // Exemple d'envoi des données à l'API
        await apiClient.post("/api/cas", formData, {
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });
    };

    const handleKmlUpload = (file: File) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const parser = new DOMParser();
            const kml = parser.parseFromString(
                e.target?.result as string,
                "text/xml"
            );
            const geojson = toGeoJSON.kml(kml);
            setGeojson(geojson); // Stocke dans le state pour l’envoi au backend
        };
        reader.readAsText(file);
    };

    return (
        <div>
            <label className="block font-medium mb-1">
                Géométrie (KML, optionnel)
            </label>
            <div
                className="border-2 border-dashed rounded p-4 text-center cursor-pointer hover:bg-gray-50"
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                onClick={() => fileInputRef.current?.click()}
            >
                {geojson ? (
                    <span className="text-green-600">
                        Fichier chargé. Aperçu ci-dessous.
                    </span>
                ) : (
                    <span>
                        Déposez un fichier KML ici ou cliquez pour sélectionner.
                    </span>
                )}
                <input
                    type="file"
                    accept=".kml"
                    ref={fileInputRef}
                    className="hidden"
                    onChange={handleInputChange}
                />
            </div>
            {error && <div className="text-red-600 mt-2">{error}</div>}
            {geojson && (
                <div className="mt-4 h-64">
                    <Map
                        center={[33.5, -7.5]}
                        zoom={7}
                        style={{ height: "100%", width: "100%" }}
                        scrollWheelZoom={false}
                    >
                        <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                        <GeoJSON data={geojson} />
                    </Map>
                </div>
            )}
            {geojson && (
                <button
                    className="mt-2 text-sm text-red-500 underline"
                    type="button"
                    onClick={() => {
                        setGeojson(null);
                        onChange(null);
                    }}
                >
                    Supprimer la géométrie
                </button>
            )}
            {geojson && (
                <section>
                    <Map
                        center={[36.75, 3.06]}
                        zoom={8}
                        style={{ height: "300px", width: "100%" }}
                    >
                        <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                        <GeoJSON data={geojson} />
                    </Map>
                </section>
            )}
        </div>
    );
};

export default GeoJsonDropzone;
