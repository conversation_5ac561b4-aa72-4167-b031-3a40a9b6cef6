"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/statistiques/page",{

/***/ "(app-pages-browser)/./app/dashboard/statistiques/page.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/statistiques/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatistiquesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-chartjs-2 */ \"(app-pages-browser)/./node_modules/react-chartjs-2/dist/index.js\");\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! chart.js */ \"(app-pages-browser)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var _app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/components/LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/components/RoleBasedAccess */ \"(app-pages-browser)/./app/components/RoleBasedAccess.tsx\");\n/* harmony import */ var _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/hooks/usePermissions */ \"(app-pages-browser)/./lib/hooks/usePermissions.ts\");\n/* harmony import */ var _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/contexts/DataRefreshContext */ \"(app-pages-browser)/./app/contexts/DataRefreshContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mapping des wilayaId vers les noms des DSA\nconst wilayaNames = {\n    1: \"Adrar\",\n    2: \"Chlef\",\n    3: \"Laghouat\",\n    4: \"Oum El Bouaghi\",\n    5: \"Batna\",\n    6: \"Béjaïa\",\n    7: \"Biskra\",\n    8: \"Béchar\",\n    9: \"Blida\",\n    10: \"Bouira\",\n    11: \"Tamanrasset\",\n    12: \"Tébessa\",\n    13: \"Tlemcen\",\n    14: \"Tiaret\",\n    15: \"Tizi Ouzou\",\n    16: \"Alger\",\n    17: \"Djelfa\",\n    18: \"Jijel\",\n    19: \"Sétif\",\n    20: \"Saïda\",\n    21: \"Skikda\",\n    22: \"Sidi Bel Abbès\",\n    23: \"Annaba\",\n    24: \"Guelma\",\n    25: \"Constantine\",\n    26: \"Médéa\",\n    27: \"Mostaganem\",\n    28: \"M'Sila\",\n    29: \"Mascara\",\n    30: \"Ouargla\",\n    31: \"Oran\",\n    32: \"El Bayadh\",\n    33: \"Illizi\",\n    34: \"Bordj Bou Arréridj\",\n    35: \"Boumerdès\",\n    36: \"El Tarf\",\n    37: \"Tindouf\",\n    38: \"Tissemsilt\",\n    39: \"El Oued\",\n    40: \"Khenchela\",\n    41: \"Souk Ahras\",\n    42: \"Tipaza\",\n    43: \"Mila\",\n    44: \"Aïn Defla\",\n    45: \"Naâma\",\n    46: \"Aïn Témouchent\",\n    47: \"Ghardaïa\",\n    48: \"Relizane\",\n    49: \"Timimoun\",\n    50: \"Bordj Badji Mokhtar\",\n    51: \"Ouled Djellal\",\n    52: \"Béni Abbès\",\n    53: \"In Salah\",\n    54: \"In Guezzam\",\n    55: \"Touggourt\",\n    56: \"Djanet\",\n    57: \"El M'Ghair\",\n    58: \"El Meniaa\"\n};\nchart_js__WEBPACK_IMPORTED_MODULE_7__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_7__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.BarElement, chart_js__WEBPACK_IMPORTED_MODULE_7__.Title, chart_js__WEBPACK_IMPORTED_MODULE_7__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_7__.Legend, chart_js__WEBPACK_IMPORTED_MODULE_7__.ArcElement);\nfunction StatistiquesPage() {\n    _s();\n    const { user, isAdmin } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const [analyseData, setAnalyseData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedWilaya, setSelectedWilaya] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"statuts\");\n    // Determine if dropdown should be shown and set initial selectedWilaya\n    const showDropdown = isAdmin || !(user === null || user === void 0 ? void 0 : user.wilayaId);\n    const initialWilaya = (user === null || user === void 0 ? void 0 : user.wilayaId) && !isAdmin ? user.wilayaId.toString() : \"\";\n    // Charger les données d'analyse\n    const loadAnalyse = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const url = selectedWilaya ? \"/api/stats/analyse-complete?wilayaId=\".concat(selectedWilaya) : \"/api/stats/analyse-complete\";\n            console.log(\"📊 Chargement de l'analyse depuis:\", url);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n            if (response.success && response.data) {\n                setAnalyseData(response.data);\n                console.log(\"✅ Analyse chargée:\", response.data);\n            } else {\n                setError(response.error || \"Erreur lors du chargement de l'analyse\");\n            }\n        } catch (err) {\n            console.error(\"Erreur lors du chargement de l'analyse:\", err);\n            setError(err.message || \"Erreur inconnue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            loadAnalyse();\n        }\n    }[\"StatistiquesPage.useEffect\"], [\n        selectedWilaya\n    ]);\n    // Set initial selectedWilaya based on user\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            if (user) {\n                setSelectedWilaya(initialWilaya);\n            }\n        }\n    }[\"StatistiquesPage.useEffect\"], [\n        user,\n        initialWilaya\n    ]);\n    // Enregistrer le callback de rafraîchissement pour les statistiques\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh)(\"statistiques-analyse-complete\", loadAnalyse, [\n        selectedWilaya\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"  mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 13\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"    mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-red-600 mb-2\",\n                            children: \"Erreur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadAnalyse,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"R\\xe9essayer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 13\n        }, this);\n    }\n    if (!analyseData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"  mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-2\",\n                            children: \"Aucune donn\\xe9e\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Aucune statistique \\xe0 afficher pour le moment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadAnalyse,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"Actualiser\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 235,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"  mx-auto px-4 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Analyse Compl\\xe8te des Dossiers et Contraintes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_4__.UserRoleBadge, {\n                                        className: \"mt-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWilaya,\n                                        onChange: (e)=>setSelectedWilaya(e.target.value),\n                                        disabled: loading,\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-48 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Toutes les DSA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 33\n                                            }, this),\n                                            Array.from({\n                                                length: 58\n                                            }, (_, i)=>{\n                                                const wilayaId = i + 1;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: wilayaId.toString(),\n                                                    children: wilayaNames[wilayaId] || \"DSA \".concat(wilayaId)\n                                                }, wilayaId, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 41\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: loadAnalyse,\n                                        disabled: loading,\n                                        className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\",\n                                        children: [\n                                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {\n                                                size: \"sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 41\n                                            }, this),\n                                            \"Actualiser\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-blue-800\",\n                                        children: \"\\uD83D\\uDD35 Total dossiers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"p text-2xl font-bold text-blue-900\",\n                                        children: analyseData.totalCas.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-green-800\",\n                                        children: \"\\uD83D\\uDFE2 R\\xe9gularis\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-900\",\n                                        children: (()=>{\n                                            const totalRegularises = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.regularise, 0), 0);\n                                            return totalRegularises.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-yellow-800\",\n                                        children: \"\\uD83D\\uDFE1 Ajourn\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-yellow-900\",\n                                        children: (()=>{\n                                            const totalAjournes = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.ajourne, 0), 0);\n                                            return totalAjournes.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-800\",\n                                        children: \"⚪ Non examin\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: (()=>{\n                                            const totalNonExamines = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.nonExamine, 0), 0);\n                                            return totalNonExamines.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-red-800\",\n                                        children: \"\\uD83D\\uDD34 Rejet\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-900\",\n                                        children: (()=>{\n                                            const totalRejetes = analyseData.tableauStatuts.reduce((sum, statut)=>sum + statut.wilayas.reduce((wilayaSum, wilaya)=>wilayaSum + wilaya.rejete, 0), 0);\n                                            return totalRejetes.toLocaleString();\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"statuts\"),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"statuts\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: \"\\uD83D\\uDCCA Analyse des Dossiers par DSA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"contraintes\"),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"contraintes\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: \"\\uD83D\\uDD0D Analyse des Contraintes par Secteur\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"charts\"),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"charts\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: \"\\uD83D\\uDCC8 Graphiques Dynamiques\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 13\n            }, this),\n            activeTab === \"statuts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto xl:overflow-visible\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full xl:min-w-0 divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"DSA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Total dossiers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"R\\xe9gularis\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Ajourn\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Rejet\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Non examin\\xe9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Taux R\\xe9gularisation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: (()=>{\n                                            // Fusionner toutes les données par DSA\n                                            const dsaStats = new Map();\n                                            analyseData.tableauStatuts.forEach((statutData)=>{\n                                                statutData.wilayas.forEach((wilaya)=>{\n                                                    if (!dsaStats.has(wilaya.wilayaId)) dsaStats.set(wilaya.wilayaId, {\n                                                        dsaName: wilayaNames[wilaya.wilayaId] || \"DSA \".concat(wilaya.wilayaId),\n                                                        total: 0,\n                                                        regularise: 0,\n                                                        ajourne: 0,\n                                                        rejete: 0,\n                                                        nonExamine: 0\n                                                    });\n                                                    const stats = dsaStats.get(wilaya.wilayaId);\n                                                    stats.total += wilaya.total;\n                                                    stats.regularise += wilaya.regularise;\n                                                    stats.ajourne += wilaya.ajourne;\n                                                    stats.rejete += wilaya.rejete;\n                                                    stats.nonExamine += wilaya.nonExamine;\n                                                });\n                                            });\n                                            return Array.from(dsaStats.entries()).sort((param, param1)=>{\n                                                let [a] = param, [b] = param1;\n                                                return a - b;\n                                            }).map((param)=>{\n                                                let [wilayaId, stats] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                            children: stats.dsaName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold\",\n                                                            children: stats.total.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium\",\n                                                            children: stats.regularise.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-yellow-600\",\n                                                            children: stats.ajourne.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-red-600\",\n                                                            children: stats.rejete.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-600\",\n                                                            children: stats.nonExamine.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium\",\n                                                            children: stats.total > 0 ? \"\".concat(Math.round(stats.regularise / stats.total * 100), \"%\") : \"0%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 57\n                                                        }, this)\n                                                    ]\n                                                }, wilayaId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 53\n                                                }, this);\n                                            });\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 442,\n                columnNumber: 17\n            }, this),\n            activeTab === \"contraintes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Analyse des Contraintes par Structure Administrative\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 21\n                    }, this),\n                    analyseData.tableauContraintes.map((dsaData)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: wilayaNames[dsaData.wilayaId] || \"DSA \".concat(dsaData.wilayaId)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: dsaData.encrages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 italic\",\n                                        children: \"Aucune contrainte identifi\\xe9e pour cette DSA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: dsaData.encrages.map((encrage, encrageIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-l-4 border-blue-500 pl-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: encrage.secteur === \"Secteur non défini\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-md font-semibold text-yellow-800\",\n                                                                    children: \"\\uD83D\\uDCCB Dossiers sans contrainte\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-yellow-700\",\n                                                                    children: [\n                                                                        \"Total de dossiersss sans contrainte:\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold text-yellow-900\",\n                                                                            children: encrage.totalCas\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-md font-semibold text-gray-800\",\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCCB\",\n                                                                        \" \",\n                                                                        encrage.secteur\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"Structure Administrative:\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: encrage.secteur\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 69\n                                                                        }, this),\n                                                                        \" \",\n                                                                        \"| Total contraintes:\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: encrage.totalCas\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 53\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto xl:overflow-visible\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"min-w-full xl:min-w-0 divide-y divide-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    className: \"bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Probl\\xe9matique\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Total Contraintes\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"R\\xe9gularis\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 665,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Ajourn\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 668,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Rejet\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Non examin\\xe9\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 69\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                children: \"Taux R\\xe9gularisation\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                lineNumber: 678,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                        lineNumber: 657,\n                                                                        columnNumber: 65\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"bg-white divide-y divide-gray-200\",\n                                                                    children: encrage.problematiques.map((prob, probIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50 \".concat(encrage.secteur === \"Secteur non défini\" ? \"bg-yellow-50\" : \"\"),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-900\",\n                                                                                    children: prob.problematiqueName\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 701,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-semibold text-gray-900\",\n                                                                                    children: prob.count\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 706,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-green-600 font-medium\",\n                                                                                    children: prob.statuts.regularise\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-yellow-600\",\n                                                                                    children: prob.statuts.ajourne\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 718,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-red-600\",\n                                                                                    children: prob.statuts.rejete\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 725,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                    children: prob.statuts.nonExamine\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 732,\n                                                                                    columnNumber: 77\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-blue-600 font-medium\",\n                                                                                    children: prob.count > 0 ? \"\".concat(Math.round(prob.statuts.regularise / prob.count * 100), \"%\") : \"0%\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                                    lineNumber: 739,\n                                                                                    columnNumber: 77\n                                                                                }, this)\n                                                                            ]\n                                                                        }, probIndex, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 73\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 57\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                ]\n                                            }, encrageIndex, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 49\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, dsaData.wilayaId, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 25\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 575,\n                columnNumber: 17\n            }, this),\n            activeTab === \"charts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Graphiques Dynamiques\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 769,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"R\\xe9partition par Statut\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                height: \"500px\"\n                                            },\n                                            className: \"xl:h-[600px] 2xl:h-[700px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Doughnut, {\n                                                data: analyseData.chartStatuts,\n                                                options: {\n                                                    responsive: true,\n                                                    maintainAspectRatio: false,\n                                                    plugins: {\n                                                        legend: {\n                                                            position: \"bottom\",\n                                                            labels: {\n                                                                padding: 20,\n                                                                usePointStyle: true\n                                                            }\n                                                        },\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Distribution des dossiers par statut\",\n                                                            font: {\n                                                                size: 16\n                                                            }\n                                                        },\n                                                        tooltip: {\n                                                            callbacks: {\n                                                                label: function(context) {\n                                                                    const total = context.dataset.data.reduce((a, b)=>a + b, 0);\n                                                                    const percentage = (context.parsed / total * 100).toFixed(1);\n                                                                    return \"\".concat(context.label, \": \").concat(context.parsed.toLocaleString(), \" (\").concat(percentage, \"%)\");\n                                                                }\n                                                            }\n                                                        }\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md border border-gray-200 lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Nombre de dossiers par DSA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                height: \"500px\"\n                                            },\n                                            className: \"xl:h-[600px] 2xl:h-[700px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Bar, {\n                                                data: analyseData.chartWilayas,\n                                                options: {\n                                                    responsive: true,\n                                                    maintainAspectRatio: false,\n                                                    plugins: {\n                                                        legend: {\n                                                            position: \"top\",\n                                                            labels: {\n                                                                usePointStyle: true,\n                                                                padding: 15\n                                                            }\n                                                        },\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Répartition des dossiers par DSA et statut\",\n                                                            font: {\n                                                                size: 16\n                                                            }\n                                                        },\n                                                        tooltip: {\n                                                            mode: \"index\",\n                                                            intersect: false,\n                                                            callbacks: {\n                                                                footer: function(tooltipItems) {\n                                                                    let total = 0;\n                                                                    tooltipItems.forEach(function(tooltipItem) {\n                                                                        total += tooltipItem.parsed.y;\n                                                                    });\n                                                                    return \"Total: \".concat(total.toLocaleString());\n                                                                }\n                                                            }\n                                                        }\n                                                    },\n                                                    scales: {\n                                                        x: {\n                                                            stacked: true,\n                                                            title: {\n                                                                display: true,\n                                                                text: \"DSA (Triées par nombre total de dossiers)\",\n                                                                font: {\n                                                                    size: 14\n                                                                }\n                                                            },\n                                                            ticks: {\n                                                                maxRotation: 45,\n                                                                minRotation: 45\n                                                            }\n                                                        },\n                                                        y: {\n                                                            stacked: true,\n                                                            beginAtZero: true,\n                                                            title: {\n                                                                display: true,\n                                                                text: \"Nombre de dossiers\",\n                                                                font: {\n                                                                    size: 14\n                                                                }\n                                                            }\n                                                        }\n                                                    },\n                                                    interaction: {\n                                                        mode: \"index\",\n                                                        intersect: false\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 773,\n                        columnNumber: 21\n                    }, this),\n                    (()=>{\n                        // Aggregate actual blocages by sector and resolution status\n                        const sectorStats = new Map();\n                        // Agréger les contraintes par secteur et statut de résolution\n                        // Chaque statut représente le nombre de contraintes avec cette résolution\n                        analyseData.tableauContraintes.forEach((dsaData)=>{\n                            dsaData.encrages.forEach((encrage)=>{\n                                const secteur = encrage.secteur;\n                                // Skip \"Secteur non défini\" as it represents cases without constraints\n                                if (secteur === \"Secteur non défini\") {\n                                    return;\n                                }\n                                // Only include sectors that actually have constraints\n                                const totalConstraints = encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.regularise + prob.statuts.ajourne + prob.statuts.rejete + prob.statuts.nonExamine, 0);\n                                // Skip if no actual constraints in this sector\n                                if (totalConstraints === 0) {\n                                    return;\n                                }\n                                if (!sectorStats.has(secteur)) {\n                                    sectorStats.set(secteur, {\n                                        regularise: 0,\n                                        ajourne: 0,\n                                        rejete: 0,\n                                        nonExamine: 0\n                                    });\n                                }\n                                const stats = sectorStats.get(secteur);\n                                // Count blocages by their resolution status\n                                // Each status count represents the number of blocages with that resolution\n                                stats.regularise += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.regularise, 0);\n                                stats.ajourne += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.ajourne, 0);\n                                stats.rejete += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.rejete, 0);\n                                stats.nonExamine += encrage.problematiques.reduce((sum, prob)=>sum + prob.statuts.nonExamine, 0);\n                            });\n                        });\n                        // Trier les secteurs par nombre total de contraintes décroissant\n                        const sortedSecteurs = Array.from(sectorStats.entries()).map((param)=>{\n                            let [secteur, stats] = param;\n                            return {\n                                secteur,\n                                total: stats.regularise + stats.ajourne + stats.rejete + stats.nonExamine,\n                                ...stats\n                            };\n                        }).sort((a, b)=>b.total - a.total);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Nombre de Contraintes par Secteur selon la R\\xe9solution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1022,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: \"600px\"\n                                        },\n                                        className: \"xl:h-[700px] 2xl:h-[800px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Bar, {\n                                            data: {\n                                                labels: sortedSecteurs.map((item)=>item.secteur),\n                                                datasets: [\n                                                    {\n                                                        label: \"Régularisé\",\n                                                        data: sortedSecteurs.map((item)=>item.regularise),\n                                                        backgroundColor: \"#10B981\"\n                                                    },\n                                                    {\n                                                        label: \"Ajourné\",\n                                                        data: sortedSecteurs.map((item)=>item.ajourne),\n                                                        backgroundColor: \"#F59E0B\"\n                                                    },\n                                                    {\n                                                        label: \"Rejeté\",\n                                                        data: sortedSecteurs.map((item)=>item.rejete),\n                                                        backgroundColor: \"#EF4444\"\n                                                    },\n                                                    {\n                                                        label: \"Non examiné\",\n                                                        data: sortedSecteurs.map((item)=>item.nonExamine),\n                                                        backgroundColor: \"#6B7280\"\n                                                    }\n                                                ]\n                                            },\n                                            options: {\n                                                responsive: true,\n                                                maintainAspectRatio: false,\n                                                plugins: {\n                                                    legend: {\n                                                        position: \"top\",\n                                                        labels: {\n                                                            usePointStyle: true,\n                                                            padding: 20\n                                                        }\n                                                    },\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Répartition des Contraintes par secteur et statut de résolution\",\n                                                        font: {\n                                                            size: 16\n                                                        }\n                                                    },\n                                                    tooltip: {\n                                                        mode: \"index\",\n                                                        intersect: false,\n                                                        callbacks: {\n                                                            footer: function(tooltipItems) {\n                                                                let total = 0;\n                                                                tooltipItems.forEach(function(tooltipItem) {\n                                                                    total += tooltipItem.parsed.y;\n                                                                });\n                                                                return \"Total contraintes dans ce secteur: \".concat(total.toLocaleString());\n                                                            }\n                                                        }\n                                                    }\n                                                },\n                                                scales: {\n                                                    x: {\n                                                        stacked: true,\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Secteurs\",\n                                                            font: {\n                                                                size: 14\n                                                            }\n                                                        },\n                                                        ticks: {\n                                                            maxRotation: 45,\n                                                            minRotation: 45\n                                                        }\n                                                    },\n                                                    y: {\n                                                        stacked: true,\n                                                        beginAtZero: true,\n                                                        title: {\n                                                            display: true,\n                                                            text: \"Nombre de contraintes\",\n                                                            font: {\n                                                                size: 14\n                                                            }\n                                                        }\n                                                    }\n                                                },\n                                                interaction: {\n                                                    mode: \"index\",\n                                                    intersect: false\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1028,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1021,\n                            columnNumber: 29\n                        }, this);\n                    })(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Vue d'ensemble\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1160,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1159,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        height: \"600px\"\n                                    },\n                                    className: \"xl:h-[700px] 2xl:h-[800px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Bar, {\n                                        data: {\n                                            ...analyseData.chartWilayas,\n                                            labels: analyseData.chartWilayas.labels.slice(0, 58),\n                                            datasets: analyseData.chartWilayas.datasets.map((dataset)=>({\n                                                    ...dataset,\n                                                    data: dataset.data.slice(0, 58)\n                                                }))\n                                        },\n                                        options: {\n                                            indexAxis: \"y\",\n                                            responsive: true,\n                                            maintainAspectRatio: false,\n                                            plugins: {\n                                                legend: {\n                                                    position: \"top\",\n                                                    labels: {\n                                                        usePointStyle: true,\n                                                        padding: 20\n                                                    }\n                                                },\n                                                title: {\n                                                    display: true,\n                                                    text: \" Nombre de dossiers - Répartition détaillée par statut\",\n                                                    font: {\n                                                        size: 18\n                                                    }\n                                                }\n                                            },\n                                            scales: {\n                                                x: {\n                                                    stacked: true,\n                                                    beginAtZero: true,\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Nombre de dossiers\",\n                                                        font: {\n                                                            size: 16\n                                                        }\n                                                    }\n                                                },\n                                                y: {\n                                                    stacked: true,\n                                                    title: {\n                                                        display: true,\n                                                        text: \"DSA (Triées par nombre décroissant)\",\n                                                        font: {\n                                                            size: 16\n                                                        }\n                                                    },\n                                                    ticks: {\n                                                        maxRotation: 0,\n                                                        minRotation: 0,\n                                                        font: {\n                                                            size: 12\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1169,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1164,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1158,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 768,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 9\n    }, this);\n}\n_s(StatistiquesPage, \"DmjzyVXiKxnNDAs8JncvHRlatEI=\", false, function() {\n    return [\n        _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_6__.useRegisterDataRefresh\n    ];\n});\n_c = StatistiquesPage;\nvar _c;\n$RefreshReg$(_c, \"StatistiquesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/statistiques/page.tsx\n"));

/***/ })

});