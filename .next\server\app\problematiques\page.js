(()=>{var e={};e.id=5144,e.ids=[5144],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14220:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),a=t(79464),i=t(44999),n=t(41098);async function o(){let e=await (0,i.UL)(),s=e.get("token")?.value,t=null;if(s)try{let e=await (0,n.nr)(s);e&&"object"==typeof e&&"role"in e&&(t=e)}catch(e){console.error("Token verification failed in getStatistics:",e)}let r={};return t&&"ADMIN"!==t.role&&t.wilayaId&&(r={communes:{some:{wilayaId:t.wilayaId}}}),(await a.z.problematique.findMany({select:{id:!0,problematique:!0,_count:{select:{cas:{where:{regularisation:!0,...r}}}},cas:{where:r,select:{id:!0}}}})).map(e=>({nom:e.problematique,totalCas:e.cas.length,casRegularises:e._count.cas}))}async function l({children:e}){let s=await o();return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Probl\xe9matiques"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Gestion des probl\xe9matiques d'assainissement"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8",children:s.map(e=>{let s=e.totalCas>0?e.casRegularises/e.totalCas*100:0,t="bg-green-500";return s<50?t="bg-red-500":s<100&&(t="bg-orange-500"),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.nom}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total des cas:"}),(0,r.jsx)("span",{className:"font-medium text-primary-600",children:e.totalCas})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Cas r\xe9gularis\xe9s:"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:e.casRegularises})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"En attente:"}),(0,r.jsx)("span",{className:"font-medium text-orange-600",children:e.totalCas-e.casRegularises})]}),e.totalCas>0&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Progression"}),(0,r.jsxs)("span",{className:`text-sm font-medium ${s<50?"text-red-700 dark:text-red-500":s<100?"text-orange-700 dark:text-orange-500":"text-green-700 dark:text-green-500"}`,children:[s.toFixed(0),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700",children:(0,r.jsx)("div",{className:`${t} h-2.5 rounded-full`,style:{width:`${s}%`}})})]})]})]},e.nom)})}),e," "]})}},18541:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(60687),a=t(43210),i=t(9171),n=t(52643),o=t(99235),l=t(78712),d=t(89679),c=t(69266);function u(){let[e,s]=(0,a.useState)([]),[t,u]=(0,a.useState)([]),[m,p]=(0,a.useState)(null),[x,h]=(0,a.useState)(!1),[g,b]=(0,a.useState)(!1),[f,j]=(0,a.useState)(null),[v,y]=(0,a.useState)({problematique:"",encrageId:""}),[q,w]=(0,a.useState)(!1),[N,k]=(0,a.useState)(""),C="ADMIN"===m,P=(0,a.useCallback)(async()=>{w(!0);try{let e=await c.uE.get("/api/problematiques");s(e)}catch(e){k(e.message||"Erreur lors du chargement des probl\xe9matiques.")}finally{w(!1)}},[]);async function I(e){e.preventDefault(),k(""),w(!0);try{let e={problematique:v.problematique,encrageId:v.encrageId};g&&f?await (0,c.Zq)(`/api/problematiques/${f.id}`,{method:"PUT",body:e}):await (0,c.Zq)("/api/problematiques",{method:"POST",body:e}),h(!1),P()}catch(e){k(e.message)}finally{w(!1)}}async function A(e){if(confirm(`\xcates-vous s\xfbr de vouloir supprimer la probl\xe9matique : "${e.problematique}" ?`))try{await (0,c.Zq)(`/api/problematiques/${e.id}`,{method:"DELETE"}),P()}catch(e){k(e.message)}}(0,a.useCallback)(async()=>{try{let e=await c.uE.get("/api/encrages");u(e)}catch(e){console.error("Erreur lors du chargement des encrages:",e)}},[]),(0,a.useCallback)(async()=>{try{let e=await c.uE.get("/api/auth/session");p(e.role)}catch(e){console.error("Erreur lors du chargement de la session utilisateur:",e),p(null)}},[]);let L=[{header:"Probl\xe9matique",accessorKey:"problematique"},{header:"Encrage",accessorKey:e=>e.encrage?.nom||"N/A"},{header:"Cas",accessorKey:e=>`${e.cas?.length||0} cas`}];return(0,r.jsxs)("div",{className:"  mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between mb-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-foreground",children:"Probl\xe9matiques"}),C&&(0,r.jsx)(i.$,{onClick:function(){j(null),y({problematique:"",encrageId:""}),b(!1),h(!0)},title:"Ajouter une probl\xe9matique",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"})})})]}),N&&(0,r.jsx)(d.j,{message:N}),(0,r.jsx)(o.X,{data:e,columns:L,actions:C?e=>(0,r.jsxs)("div",{className:"flex justify-center items-center space-x-1 sm:space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{j(e),y({problematique:e.problematique,encrageId:e.encrageId}),b(!0),h(!0)},title:"Modifier la probl\xe9matique",className:"p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"})})}),(0,r.jsx)("button",{onClick:()=>A(e),title:"Supprimer la probl\xe9matique",className:"p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}):void 0}),(0,r.jsx)(l.a,{isOpen:x,onClose:()=>h(!1),title:g?"Modifier la probl\xe9matique":"Ajouter une probl\xe9matique",children:(0,r.jsxs)("form",{onSubmit:I,className:"space-y-4",children:[(0,r.jsx)(n.p,{id:"problematique",label:"Probl\xe9matique",value:v.problematique,onChange:e=>y(s=>({...s,problematique:e.target.value})),required:!0}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"encrageId",className:"block text-sm font-medium text-gray-700",children:"Encrage"}),(0,r.jsxs)("select",{id:"encrageId",value:v.encrageId,onChange:e=>y(s=>({...s,encrageId:e.target.value})),className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm",required:!0,children:[(0,r.jsx)("option",{value:"",children:"S\xe9lectionner un encrage"}),t.map(e=>(0,r.jsx)("option",{value:e.id,children:e.nom},e.id))]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(i.$,{variant:"outline",onClick:()=>h(!1),type:"button",children:"Annuler"}),(0,r.jsx)(i.$,{type:"submit",isLoading:q,children:g?"Modifier":"Ajouter"})]})]})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24419:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\problematiques\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\problematiques\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},56846:(e,s,t)=>{Promise.resolve().then(t.bind(t,18541))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71783:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["problematiques",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,24419)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\problematiques\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,14220)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\problematiques\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\problematiques\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/problematiques/page",pathname:"/problematiques",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96166:(e,s,t)=>{Promise.resolve().then(t.bind(t,24419))},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[7719,3903,5262,2348,2797,2049],()=>t(71783));module.exports=r})();