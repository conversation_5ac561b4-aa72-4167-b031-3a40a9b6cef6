(()=>{var e={};e.id=5537,e.ids=[5537],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9834:(e,t,s)=>{"use strict";s.d(t,{Q:()=>d});var a=s(60687),r=s(43210),n=s(52643),l=s(15463),i=s(9171),o=s(73559);function d({filters:e,onFilterChange:t,className:s="",initialValues:d={}}){let[c,u]=(0,r.useState)(d),[m,p]=(0,r.useState)(!0),x=(e,t)=>{u({...c,[e]:t})};return(0,a.jsxs)("div",{className:`bg-white p-4 rounded-lg shadow-sm border border-gray-200 ${s}`,children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Filtres de Recherche"]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4",children:e.map(e=>(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("label",{htmlFor:e.id,className:"block text-sm font-medium text-gray-700 mb-1",children:e.label}),"text"===e.type&&(0,a.jsx)(n.p,{id:e.id,type:"text",value:c[e.id]||"",onChange:t=>x(e.id,t.target.value),placeholder:e.placeholder,className:"w-full"}),"select"===e.type&&e.options&&(0,a.jsxs)(l.l,{id:e.id,value:c[e.id]||"",onChange:t=>x(e.id,t.target.value),className:"w-full",children:[(0,a.jsx)("option",{value:"",children:"Tous"}),e.options.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}),"date"===e.type&&(0,a.jsx)(n.p,{id:e.id,type:"date",value:c[e.id]||"",onChange:t=>x(e.id,t.target.value),className:"w-full"}),"checkbox"===e.type&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:e.id,type:"checkbox",checked:!!c[e.id],onChange:t=>x(e.id,t.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:e.id,className:"ml-2 block text-sm text-gray-700",children:e.placeholder||e.label})]})]},e.id))}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,a.jsx)(i.$,{onClick:()=>{let e=Object.keys(c).reduce((e,t)=>(e[t]="",e),{});u(e),t(e)},variant:"secondary",size:"sm",children:"R\xe9initialiser"}),(0,a.jsx)(i.$,{onClick:()=>{t(c)},variant:"primary",size:"sm",children:"Appliquer"})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15463:(e,t,s)=>{"use strict";s.d(t,{l:()=>r});var a=s(60687);s(43210);let r=({label:e,id:t,name:s,value:r,onChange:n,required:l,multiple:i,children:o,className:d,error:c,...u})=>(0,a.jsxs)("div",{children:[e&&(0,a.jsxs)("label",{htmlFor:t||s,className:"block text-sm font-medium text-gray-700 mb-1",children:[e," ",l&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("select",{id:t||s,name:s,value:r,onChange:n,required:l,multiple:i,className:`block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md shadow-sm ${c?"border-red-500 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${d||""}`,...u,children:o}),c&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:c})]})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37132:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},43655:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))})},45585:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(37413);function r({children:e}){return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"  mx-auto px-2 py-2",children:(0,a.jsx)("h2",{className:"text-xl font-semibold text-foreground mb-4",children:"Statistiques par Encrage Juridique"})}),e]})}},52305:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(60687),r=s(43210),n=s.n(r),l=s(9171),i=s(52643),o=s(99235),d=s(78712),c=s(89679),u=s(43655),m=s(37132);function p({currentPage:e,totalPages:t,onPageChange:s,className:r=""}){return t<=1?null:(0,a.jsxs)("div",{className:`flex items-center justify-center space-x-2 mt-4 ${r}`,children:[(0,a.jsx)("button",{onClick:()=>e>1&&s(e-1),disabled:1===e,className:`p-2 rounded-md ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,"aria-label":"Page pr\xe9c\xe9dente",children:(0,a.jsx)(u.A,{className:"h-5 w-5"})}),(()=>{let s=[];if(t<=5)for(let e=1;e<=t;e++)s.push(e);else{s.push(1);let a=Math.max(2,e-1),r=Math.min(t-1,e+1);e<=3?r=4:e>=t-2&&(a=t-3),a>2&&s.push("...");for(let e=a;e<=r;e++)s.push(e);r<t-1&&s.push("..."),s.push(t)}return s})().map((t,r)=>(0,a.jsx)(n().Fragment,{children:"..."===t?(0,a.jsx)("span",{className:"px-3 py-2",children:"..."}):(0,a.jsx)("button",{onClick:()=>"number"==typeof t&&s(t),className:`px-3 py-1 rounded-md ${e===t?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100"}`,"aria-current":e===t?"page":void 0,"aria-label":`Page ${t}`,children:t})},r)),(0,a.jsx)("button",{onClick:()=>e<t&&s(e+1),disabled:e===t,className:`p-2 rounded-md ${e===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,"aria-label":"Page suivante",children:(0,a.jsx)(m.A,{className:"h-5 w-5"})})]})}var x=s(9834),h=s(73018),g=s(69266);function f(){let[e,t]=(0,r.useState)(""),[s,n]=(0,r.useState)("nom"),[u,m]=(0,r.useState)("asc"),[f,v]=(0,r.useState)(""),[b,j]=(0,r.useState)(""),[y,w]=(0,r.useState)(""),[N,k]=(0,r.useState)(1),[C,S]=(0,r.useState)(10),[P,A]=(0,r.useState)(!1),[$,L]=(0,r.useState)(!1),[E,R]=(0,r.useState)(null),[q,I]=(0,r.useState)({nom:""}),[M,O]=(0,r.useState)(""),[_,D]=(0,r.useState)(null),T="ADMIN"===_,{encrages:F,encrageStats:V,isLoading:z,page:U,totalPages:W,totalItems:B,goToPage:G,refresh:J}=(0,h.W)({limit:C,includeStats:!0,search:e,sortBy:s,sortDirection:u,dateFrom:f,dateTo:b,status:y}),H=(0,r.useCallback)(e=>{t(e.search||""),v(e.dateFrom||""),j(e.dateTo||""),w(e.status||""),k(1)},[]);(0,r.useCallback)(e=>{s===e?m("asc"===u?"desc":"asc"):(n(e),m("asc"))},[s,u]);let K=(0,r.useCallback)(e=>{k(e),G(e)},[G]),Q=(0,r.useCallback)(e=>{S(parseInt(e.target.value,10)),k(1)},[]);async function X(e){if(e.preventDefault(),!T)return void O("Action non autoris\xe9e.");O("");try{$&&E?await g.uE.put(`/api/encrages/${E.id}`,q):await g.uE.post("/api/encrages",q),J(),A(!1),I({nom:""})}catch(e){O(e.message||"Une erreur s'est produite. Veuillez r\xe9essayer.")}}async function Z(e){if(!T)return void O("Action non autoris\xe9e.");if(confirm(`\xcates-vous s\xfbr de vouloir supprimer l'encrage : "${e.nom}" ?`))try{await g.uE.delete(`/api/encrages/${e.id}`),J()}catch(e){O(e.message)}}return(0,r.useCallback)(async()=>{try{let e=await g.uE.get("/api/auth/session");D(e.role)}catch(e){console.error("Erreur lors du chargement de la session utilisateur:",e),D(null)}},[]),(0,a.jsxs)("div",{className:" mx-auto px-4 py-8 ",children:[V.length>0&&(0,a.jsx)("div",{className:"mb-10",children:(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:V.map(e=>{let t=e.totalCas>0?e.casRegularises/e.totalCas*100:0,s="bg-green-500";return 0===t||t<50?s="bg-red-500":t<100&&(s="bg-orange-500"),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex flex-col justify-between min-h-[200px] pb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.nom}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total des cas:"}),(0,a.jsx)("span",{className:"font-medium text-primary-600",children:e.totalCas})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Cas r\xe9gularis\xe9s:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:e.casRegularises})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"En attente:"}),(0,a.jsx)("span",{className:"font-medium text-orange-600",children:e.totalCas-e.casRegularises})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Progression"}),(0,a.jsxs)("span",{className:`text-sm font-medium ${0===t||t<50?"text-red-700 dark:text-red-500":t<100?"text-orange-700 dark:text-orange-500":"text-green-700 dark:text-green-500"}`,children:[t.toFixed(0),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700",children:(0,a.jsx)("div",{className:`${s} h-2.5 rounded-full transition-all duration-300`,style:{width:`${t}%`}})})]})]},e.id)})})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 mt-8 gap-4",children:[(0,a.jsx)("h1",{className:"sm:flex sm:items-center text-2xl font-semibold text-foreground",children:"Gestion des Encrages"}),T&&(0,a.jsx)(l.$,{onClick:function(){T&&(R(null),I({nom:""}),L(!1),A(!0))},title:"Ajouter un encrage",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"})})})]}),(0,a.jsx)(x.Q,{filters:[{id:"search",label:"Recherche par nom",type:"text",placeholder:"Rechercher un encrage..."},{id:"dateFrom",label:"Date de d\xe9but",type:"date"},{id:"dateTo",label:"Date de fin",type:"date"},{id:"status",label:"Statut",type:"select",options:[{value:"",label:"Tous"},{value:"active",label:"Actif"},{value:"inactive",label:"Inactif"}]}],onFilterChange:H,className:"mb-6",initialValues:{search:e,dateFrom:f,dateTo:b,status:y}}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Affichage de ",F.length," encrage(s) sur ",B," ","au total"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{htmlFor:"pageSize",className:"text-sm text-gray-600",children:"Afficher par page:"}),(0,a.jsxs)("select",{id:"pageSize",value:C,onChange:Q,className:"border border-gray-300 rounded-md text-sm p-1",children:[(0,a.jsx)("option",{value:"5",children:"5"}),(0,a.jsx)("option",{value:"10",children:"10"}),(0,a.jsx)("option",{value:"20",children:"20"}),(0,a.jsx)("option",{value:"50",children:"50"})]})]})]}),M&&(0,a.jsx)(c.j,{message:M}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)(o.X,{data:F,columns:[{header:"Nom de l'encrage",accessorKey:"nom"}],actions:T?e=>(0,a.jsxs)("div",{className:"flex justify-center items-center space-x-1 sm:space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{T&&(R(e),I({nom:e.nom}),L(!0),A(!0))},title:"Modifier l'encrage",className:"p-2 rounded-md text-sky-600 hover:text-sky-800 hover:bg-sky-100 transition-colors duration-150",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"})})}),(0,a.jsx)("button",{onClick:()=>Z(e),title:"Supprimer l'encrage",className:"p-2 rounded-md text-red-500 hover:text-red-700 hover:bg-red-100 transition-colors duration-150",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.342.052.682.107 1.022.166m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}):void 0})}),(0,a.jsx)(p,{currentPage:N,totalPages:W,onPageChange:K,className:"mt-6"}),T&&P&&(0,a.jsx)(d.a,{isOpen:P,onClose:()=>A(!1),title:$?"Modifier l'encrage":"Ajouter un encrage",children:(0,a.jsxs)("form",{onSubmit:X,className:"space-y-4",children:[(0,a.jsx)(i.p,{id:"nom",label:"Nom de l'encrage",value:q.nom,onChange:e=>I({...q,nom:e.target.value}),required:!0}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>A(!1),type:"button",children:"Annuler"}),(0,a.jsx)(l.$,{type:"submit",isLoading:z,children:$?"Modifier":"Ajouter"})]})]})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71990:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\encrages\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\encrages\\page.tsx","default")},73018:(e,t,s)=>{"use strict";s.d(t,{W:()=>n});var a=s(43210),r=s(69266);function n(e={}){let{limit:t=50,includeStats:s=!1,search:l="",includeProblematiques:i=!1,sortBy:o="nom",sortDirection:d="asc",dateFrom:c="",dateTo:u="",status:m=""}=e,[p,x]=(0,a.useState)([]),[h,g]=(0,a.useState)([]),[f,v]=(0,a.useState)(!1),[b,j]=(0,a.useState)(null),[y,w]=(0,a.useState)(1),[N,k]=(0,a.useState)(1),[C,S]=(0,a.useState)(0),P=`encrages-${t}-${s}-${l}-${i}-${y}-${o}-${d}-${c}-${u}-${m}`,A=(0,a.useCallback)(async()=>{let e=sessionStorage.getItem(P);if(e)try{let t=JSON.parse(e),a=t.timestamp;if(Date.now()-a<3e5){x(t.data),s&&g(t.stats||[]),k(t.pagination?.totalPages||1),S(t.pagination?.total||0);return}}catch(e){console.error("Erreur lors de la lecture du cache:",e)}v(!0),j(null);try{let e=new URLSearchParams;e.append("page",y.toString()),e.append("limit",t.toString()),e.append("includeStats",s.toString()),e.append("includeProblematiques",i.toString()),l&&e.append("search",l),o&&e.append("sortBy",o),d&&e.append("sortDirection",d),c&&e.append("dateFrom",c),u&&e.append("dateTo",u),m&&e.append("status",m);let a=`/api/encrages?${e.toString()}`,n=await r.uE.get(a);if(x(n.data),k(n.pagination.totalPages),S(n.pagination.total),s){let e=n.data.filter(e=>e.casStats).map(e=>({id:e.id,nom:e.nom,totalCas:e.casStats.total,casRegularises:e.casStats.regularises}));g(e),sessionStorage.setItem(P,JSON.stringify({data:n.data,stats:e,pagination:n.pagination,timestamp:Date.now()}))}else sessionStorage.setItem(P,JSON.stringify({data:n.data,pagination:n.pagination,timestamp:Date.now()}))}catch(e){j(e.message||"Erreur lors du chargement des encrages"),console.error("Erreur lors du chargement des encrages:",e)}finally{v(!1)}},[P,i,s,t,y,l]);return{encrages:p,encrageStats:h,isLoading:f,error:b,page:y,totalPages:N,totalItems:C,goToPage:(0,a.useCallback)(e=>{w(e)},[]),refresh:A}}},73559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},77905:(e,t,s)=>{Promise.resolve().then(s.bind(s,52305))},78335:()=>{},78577:(e,t,s)=>{Promise.resolve().then(s.bind(s,71990))},79428:e=>{"use strict";e.exports=require("buffer")},83841:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),l=s.n(n),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["encrages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71990)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\encrages\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,45585)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\encrages\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,28297)),"C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\APPLICATIONS\\assainissementV5\\app\\encrages\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/encrages/page",pathname:"/encrages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[7719,3903,5262,2348,2797,2049],()=>s(83841));module.exports=a})();