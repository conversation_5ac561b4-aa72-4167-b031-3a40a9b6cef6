/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/encrages/route";
exports.ids = ["app/api/encrages/route"];
exports.modules = {

/***/ "(rsc)/./app/api/encrages/route.ts":
/*!***********************************!*\
  !*** ./app/api/encrages/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\n\n\nconst encrageSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    nom: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(1).max(255)\n});\n// Get all encrages with pagination, filtering, sorting and field selection\nasync function GET(request) {\n    try {\n        console.log(\"GET /api/encrages called\");\n        const url = new URL(request.url);\n        // Pagination parameters\n        const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(url.searchParams.get(\"limit\") || \"20\");\n        const skip = (page - 1) * limit;\n        // Filtering parameters\n        const search = url.searchParams.get(\"search\") || \"\";\n        const dateFrom = url.searchParams.get(\"dateFrom\");\n        const dateTo = url.searchParams.get(\"dateTo\");\n        const status = url.searchParams.get(\"status\");\n        // Sorting parameters\n        const sortBy = url.searchParams.get(\"sortBy\") || \"nom\";\n        const sortDirection = url.searchParams.get(\"sortDirection\") || \"asc\";\n        // Field selection\n        const fields = url.searchParams.get(\"fields\");\n        const includeProblematiques = url.searchParams.get(\"includeProblematiques\") === \"true\";\n        const includeStats = url.searchParams.get(\"includeStats\") === \"true\";\n        console.log(\"Request parameters:\", {\n            page,\n            limit,\n            search,\n            includeProblematiques\n        });\n        // Build where condition for search and filters\n        let where = {};\n        if (search) {\n            where.nom = {\n                contains: search,\n                mode: \"insensitive\"\n            };\n        }\n        // Date filtering\n        if (dateFrom || dateTo) {\n            where.createdAt = {};\n            if (dateFrom) {\n                where.createdAt.gte = new Date(dateFrom);\n            }\n            if (dateTo) {\n                where.createdAt.lte = new Date(dateTo);\n            }\n        }\n        // Status filtering (requires additional logic based on your data model)\n        if (status) {\n        // Implement status filtering based on your application's logic\n        // This might involve joining with other tables or using computed fields\n        }\n        // Count total records for pagination metadata\n        const totalCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.encrage.count({\n            where\n        });\n        console.log(\"Total encrages count:\", totalCount);\n        // Validate sortBy to prevent SQL injection\n        const validSortFields = [\n            \"nom\",\n            \"createdAt\",\n            \"updatedAt\"\n        ];\n        const actualSortBy = validSortFields.includes(sortBy) ? sortBy : \"nom\";\n        // Build orderBy object\n        const orderBy = {\n            [actualSortBy]: sortDirection\n        };\n        // Base query with pagination, filtering and sorting\n        const baseQuery = {\n            where,\n            skip,\n            take: limit,\n            orderBy\n        };\n        // Conditionally include related data based on request parameters\n        let include = {};\n        if (includeProblematiques) {\n            include = {\n                problematiques: {\n                    select: {\n                        id: true,\n                        problematique: true,\n                        _count: includeStats ? {\n                            select: {\n                                cas: true\n                            }\n                        } : undefined\n                    }\n                }\n            };\n        }\n        // Execute query with appropriate includes\n        const encrages = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.encrage.findMany({\n            ...baseQuery,\n            include\n        });\n        console.log(\"Fetched encrages:\", encrages);\n        // If stats are requested but we didn't include full cas data, fetch aggregated stats separately\n        let encragesWithStats = encrages;\n        if (includeStats && !include.hasOwnProperty(\"problematiques.cas\")) {\n            // For each encrage, fetch aggregated stats using count\n            encragesWithStats = await Promise.all(encrages.map(async (encrage)=>{\n                // Get total cas count\n                const totalCasCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                    where: {\n                        problematique: {\n                            encrageId: encrage.id\n                        }\n                    }\n                });\n                // Get regularised cas count\n                const regularisesCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.cas.count({\n                    where: {\n                        problematique: {\n                            encrageId: encrage.id\n                        },\n                        regularisation: true\n                    }\n                });\n                return {\n                    ...encrage,\n                    casStats: {\n                        total: totalCasCount,\n                        regularises: regularisesCount,\n                        enAttente: totalCasCount - regularisesCount\n                    }\n                };\n            }));\n        }\n        // Prepare pagination metadata\n        const totalPages = Math.ceil(totalCount / limit);\n        // Préparer la réponse\n        const responseData = {\n            data: encragesWithStats,\n            pagination: {\n                total: totalCount,\n                page,\n                limit,\n                totalPages,\n                hasNextPage: page < totalPages,\n                hasPrevPage: page > 1\n            }\n        };\n        console.log(\"Response data:\", responseData);\n        // Vérifier si le client accepte la compression\n        const acceptEncoding = request.headers.get(\"accept-encoding\") || \"\";\n        // Préparer les headers de base\n        const headers = {\n            \"Cache-Control\": \"private, max-age=60\",\n            \"Content-Type\": \"application/json\",\n            Vary: \"Accept-Encoding\"\n        };\n        // Convertir les données en JSON\n        const jsonData = JSON.stringify(responseData);\n        // Si le client accepte la compression gzip et que la taille des données le justifie\n        if (acceptEncoding.includes(\"gzip\") && jsonData.length > 1024) {\n            // Utiliser l'API de compression de Next.js\n            return new next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse(jsonData, {\n                headers: {\n                    ...headers,\n                    \"Content-Encoding\": \"gzip\"\n                },\n                status: 200\n            });\n        }\n        // Sinon, retourner la réponse non compressée\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(responseData, {\n            headers\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/encrages:\", error);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n// Create new encrage\nasync function POST(request) {\n    try {\n        console.log(\"POST /api/encrages called\");\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_4__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            console.log(\"No token found\");\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        }\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyToken)(token);\n        console.log(\"User payload:\", userPayload);\n        if (!userPayload || !userPayload.role || userPayload.role !== \"ADMIN\" && userPayload.role !== \"EDITOR\") {\n            console.log(\"User not authorized\");\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.forbidden)();\n        }\n        const body = await request.json();\n        console.log(\"Request body:\", body);\n        const { nom } = encrageSchema.parse(body);\n        const encrage = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.encrage.create({\n            data: {\n                nom\n            },\n            include: {\n                problematiques: true\n            }\n        });\n        console.log(\"Created encrage:\", encrage);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json(encrage);\n    } catch (error) {\n        console.error(\"Error in POST /api/encrages:\", error);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/encrages/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fencrages%2Froute&page=%2Fapi%2Fencrages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fencrages%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fencrages%2Froute&page=%2Fapi%2Fencrages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fencrages%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_encrages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/encrages/route.ts */ \"(rsc)/./app/api/encrages/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/encrages/route\",\n        pathname: \"/api/encrages\",\n        filename: \"route\",\n        bundlePath: \"app/api/encrages/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\encrages\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_encrages_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fencrages%2Froute&page=%2Fapi%2Fencrages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fencrages%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fencrages%2Froute&page=%2Fapi%2Fencrages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fencrages%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();